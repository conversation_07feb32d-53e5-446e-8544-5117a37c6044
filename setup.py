from setuptools import setup, find_packages

setup(
    name="lung_ct_segmenter",
    version="1.0.0",
    packages=find_packages(),
    install_requires=[
        'numpy>=1.21.0',
        'SimpleITK>=2.1.0',
        'lungmask>=0.2.13',
        'TotalSegmentator>=1.5.0',
        'pytest>=7.0.0',
        'packaging>=21.0',
        'PyQt5>=5.15.0',
        'matplotlib>=3.5.0',
        'scikit-image>=0.19.0',
        'pandas>=1.3.0',
        'pydicom>=2.3.0',
        'Pillow>=8.3.0',
    ],
    author="Lung CT Segmenter Team",
    author_email="<EMAIL>",
    description="完整的肺部CT影像分析工具集，包含分割、分析、DICOM处理等功能",
    long_description=open('README.md', encoding='utf-8').read(),
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/lung_ct_segmenter",
    classifiers=[
        "Programming Language :: Python :: 3",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Topic :: Scientific/Engineering :: Medical Science Apps.",
        "Topic :: Scientific/Engineering :: Image Processing",
    ],
    python_requires='>=3.8',
    entry_points={
        'console_scripts': [
            'lung-segmentation=run_lung_segmentation:main',
            'lung-analyzer=run_lung_tissue_analyzer:main',
            'image-to-dicom=run_image_to_dicom:main',
            'dicom-renumber=run_dicom_renumber:main',
            'ct-region-cropper=run_ct_region_cropper:main',
        ],
    },
)