#!/usr/bin/env python3
"""
DICOM文件验证工具
用于验证JPG转DICOM工具生成的DICOM文件是否符合标准
"""

import os
import sys
import pydicom
import argparse
from pathlib import Path

def validate_single_dicom(dcm_path):
    """验证单个DICOM文件"""
    print(f"\n验证文件: {os.path.basename(dcm_path)}")
    print("-" * 50)
    
    try:
        # 读取DICOM文件
        ds = pydicom.dcmread(dcm_path, force=True)
        
        # 基本信息
        print(f"患者姓名: {getattr(ds, 'PatientName', 'N/A')}")
        print(f"患者ID: {getattr(ds, 'PatientID', 'N/A')}")
        print(f"检查描述: {getattr(ds, 'StudyDescription', 'N/A')}")
        print(f"模态: {getattr(ds, 'Modality', 'N/A')}")
        print(f"SOP类: {getattr(ds, 'SOPClassUID', 'N/A')}")
        
        # 图像信息
        print(f"图像尺寸: {getattr(ds, 'Rows', 'N/A')} x {getattr(ds, 'Columns', 'N/A')}")
        print(f"位深度: {getattr(ds, 'BitsAllocated', 'N/A')}")
        print(f"光度解释: {getattr(ds, 'PhotometricInterpretation', 'N/A')}")
        
        # 多帧信息
        if hasattr(ds, 'NumberOfFrames'):
            print(f"帧数: {ds.NumberOfFrames}")
        
        # 检查必要标签
        required_tags = [
            'PatientName', 'PatientID', 'StudyInstanceUID', 'SeriesInstanceUID',
            'SOPInstanceUID', 'SOPClassUID', 'Modality', 'Rows', 'Columns',
            'BitsAllocated', 'BitsStored', 'SamplesPerPixel', 'PhotometricInterpretation'
        ]
        
        missing_tags = []
        for tag in required_tags:
            if not hasattr(ds, tag) or getattr(ds, tag) is None:
                missing_tags.append(tag)
        
        if missing_tags:
            print(f"❌ 缺少必要标签: {', '.join(missing_tags)}")
            return False
        else:
            print("✅ 所有必要标签都存在")
        
        # 验证像素数据
        if hasattr(ds, 'PixelData') and ds.PixelData:
            expected_size = ds.Rows * ds.Columns * (ds.BitsAllocated // 8)
            if hasattr(ds, 'NumberOfFrames'):
                expected_size *= ds.NumberOfFrames
            
            actual_size = len(ds.PixelData)
            print(f"像素数据大小: {actual_size} bytes (期望: {expected_size})")
            
            if actual_size == expected_size:
                print("✅ 像素数据大小正确")
            else:
                print("❌ 像素数据大小不匹配")
                return False
        else:
            print("❌ 缺少像素数据")
            return False
        
        # 验证UID格式
        uid_tags = ['StudyInstanceUID', 'SeriesInstanceUID', 'SOPInstanceUID']
        for uid_tag in uid_tags:
            uid_value = getattr(ds, uid_tag, '')
            if not uid_value or not uid_value.replace('.', '').isdigit():
                print(f"❌ {uid_tag} 格式不正确: {uid_value}")
                return False
        
        print("✅ UID格式正确")
        
        print("🎉 DICOM文件验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def validate_directory(dir_path):
    """验证目录中的所有DICOM文件"""
    print(f"验证目录: {dir_path}")
    print("=" * 60)
    
    dcm_files = []
    for ext in ['.dcm', '.dicom']:
        dcm_files.extend(Path(dir_path).glob(f"*{ext}"))
    
    if not dcm_files:
        print("❌ 目录中没有找到DICOM文件")
        return False
    
    print(f"找到 {len(dcm_files)} 个DICOM文件")
    
    success_count = 0
    for dcm_file in dcm_files:
        if validate_single_dicom(str(dcm_file)):
            success_count += 1
    
    print("\n" + "=" * 60)
    print(f"验证总结: {success_count}/{len(dcm_files)} 个文件通过验证")
    
    if success_count == len(dcm_files):
        print("🎉 所有DICOM文件都符合标准！")
        return True
    else:
        print("⚠️ 部分DICOM文件存在问题")
        return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DICOM文件验证工具')
    parser.add_argument('path', help='DICOM文件或包含DICOM文件的目录路径')
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.path):
        print(f"❌ 路径不存在: {args.path}")
        return False
    
    print("DICOM文件验证工具 v2.0")
    print("=" * 60)
    
    try:
        if os.path.isfile(args.path):
            # 验证单个文件
            success = validate_single_dicom(args.path)
        else:
            # 验证目录
            success = validate_directory(args.path)
        
        return success
        
    except Exception as e:
        print(f"❌ 验证过程出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
