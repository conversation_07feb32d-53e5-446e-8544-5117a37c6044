"""
区域选择组件

提供裁剪区域的精确设置和管理功能
"""

import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QSpinBox, QPushButton, QGroupBox, QFormLayout,
                            QMessageBox, QCheckBox)
from PyQt5.QtCore import Qt, pyqtSignal

logger = logging.getLogger(__name__)


class RegionSelector(QWidget):
    """区域选择器组件"""
    
    # 信号定义
    region_changed = pyqtSignal(int, int, int, int)  # 区域改变信号
    region_cleared = pyqtSignal()  # 区域清除信号
    crop_requested = pyqtSignal()  # 请求裁剪信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.image_shape = None
        self.current_region = None
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 区域设置组
        region_group = self.create_region_group()
        layout.addWidget(region_group)
        
        # 操作按钮组
        action_group = self.create_action_group()
        layout.addWidget(action_group)
        
        # 信息显示组
        info_group = self.create_info_group()
        layout.addWidget(info_group)
        
    def create_region_group(self):
        """创建区域设置组"""
        group = QGroupBox("裁剪区域设置")
        layout = QFormLayout(group)
        
        # X坐标
        self.x_spin = QSpinBox()
        self.x_spin.setMinimum(0)
        self.x_spin.setMaximum(9999)
        self.x_spin.setValue(0)
        layout.addRow("X坐标:", self.x_spin)
        
        # Y坐标
        self.y_spin = QSpinBox()
        self.y_spin.setMinimum(0)
        self.y_spin.setMaximum(9999)
        self.y_spin.setValue(0)
        layout.addRow("Y坐标:", self.y_spin)
        
        # 宽度
        self.width_spin = QSpinBox()
        self.width_spin.setMinimum(1)
        self.width_spin.setMaximum(9999)
        self.width_spin.setValue(100)
        layout.addRow("宽度:", self.width_spin)
        
        # 高度
        self.height_spin = QSpinBox()
        self.height_spin.setMinimum(1)
        self.height_spin.setMaximum(9999)
        self.height_spin.setValue(100)
        layout.addRow("高度:", self.height_spin)
        
        # 约束选项
        constraint_layout = QHBoxLayout()
        
        self.maintain_aspect_ratio = QCheckBox("保持宽高比")
        constraint_layout.addWidget(self.maintain_aspect_ratio)
        
        self.center_region = QCheckBox("居中区域")
        constraint_layout.addWidget(self.center_region)
        
        layout.addRow("约束:", constraint_layout)
        
        return group
        
    def create_action_group(self):
        """创建操作按钮组"""
        group = QGroupBox("操作")
        layout = QVBoxLayout(group)
        
        # 第一行按钮
        row1_layout = QHBoxLayout()
        
        self.apply_btn = QPushButton("应用区域")
        self.apply_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; }")
        row1_layout.addWidget(self.apply_btn)
        
        self.clear_btn = QPushButton("清除区域")
        row1_layout.addWidget(self.clear_btn)
        
        layout.addLayout(row1_layout)
        
        # 第二行按钮
        row2_layout = QHBoxLayout()
        
        self.preset_quarter_btn = QPushButton("1/4区域")
        row2_layout.addWidget(self.preset_quarter_btn)
        
        self.preset_half_btn = QPushButton("1/2区域")
        row2_layout.addWidget(self.preset_half_btn)
        
        self.preset_center_btn = QPushButton("中心区域")
        row2_layout.addWidget(self.preset_center_btn)
        
        layout.addLayout(row2_layout)
        
        # 第三行按钮
        row3_layout = QHBoxLayout()
        
        self.crop_btn = QPushButton("执行裁剪")
        self.crop_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; }")
        self.crop_btn.setEnabled(False)
        row3_layout.addWidget(self.crop_btn)
        
        layout.addLayout(row3_layout)
        
        return group
        
    def create_info_group(self):
        """创建信息显示组"""
        group = QGroupBox("区域信息")
        layout = QFormLayout(group)
        
        self.region_info_label = QLabel("未设置")
        layout.addRow("当前区域:", self.region_info_label)
        
        self.area_info_label = QLabel("0")
        layout.addRow("区域面积:", self.area_info_label)
        
        self.valid_info_label = QLabel("未验证")
        layout.addRow("区域有效性:", self.valid_info_label)
        
        return group
        
    def setup_connections(self):
        """设置信号连接"""
        # 数值框变化
        self.x_spin.valueChanged.connect(self.on_region_params_changed)
        self.y_spin.valueChanged.connect(self.on_region_params_changed)
        self.width_spin.valueChanged.connect(self.on_region_params_changed)
        self.height_spin.valueChanged.connect(self.on_region_params_changed)
        
        # 约束选项
        self.maintain_aspect_ratio.toggled.connect(self.on_constraint_changed)
        self.center_region.toggled.connect(self.on_constraint_changed)
        
        # 按钮
        self.apply_btn.clicked.connect(self.apply_region)
        self.clear_btn.clicked.connect(self.clear_region)
        self.crop_btn.clicked.connect(self.request_crop)
        
        # 预设按钮
        self.preset_quarter_btn.clicked.connect(lambda: self.set_preset_region(0.25))
        self.preset_half_btn.clicked.connect(lambda: self.set_preset_region(0.5))
        self.preset_center_btn.clicked.connect(lambda: self.set_preset_region(0.75))
        
    def set_image_shape(self, shape):
        """
        设置图像形状
        
        Args:
            shape: 图像形状 (height, width)
        """
        if len(shape) >= 2:
            self.image_shape = (shape[-2], shape[-1])  # (height, width)
            
            # 更新数值框的最大值
            height, width = self.image_shape
            self.x_spin.setMaximum(width - 1)
            self.y_spin.setMaximum(height - 1)
            self.width_spin.setMaximum(width)
            self.height_spin.setMaximum(height)
            
            logger.info(f"设置图像形状: {self.image_shape}")
    
    def on_region_params_changed(self):
        """区域参数改变事件处理"""
        self.validate_and_update_info()
        
        # 如果启用了约束，应用约束
        if self.maintain_aspect_ratio.isChecked():
            self.apply_aspect_ratio_constraint()
        
        if self.center_region.isChecked():
            self.apply_center_constraint()
    
    def on_constraint_changed(self):
        """约束选项改变事件处理"""
        if self.maintain_aspect_ratio.isChecked():
            self.apply_aspect_ratio_constraint()
        
        if self.center_region.isChecked():
            self.apply_center_constraint()
    
    def apply_aspect_ratio_constraint(self):
        """应用宽高比约束"""
        # 简单实现：保持1:1比例
        sender = self.sender()
        if sender == self.width_spin:
            self.height_spin.setValue(self.width_spin.value())
        elif sender == self.height_spin:
            self.width_spin.setValue(self.height_spin.value())
    
    def apply_center_constraint(self):
        """应用居中约束"""
        if self.image_shape is None:
            return
            
        height, width = self.image_shape
        region_width = self.width_spin.value()
        region_height = self.height_spin.value()
        
        center_x = (width - region_width) // 2
        center_y = (height - region_height) // 2
        
        self.x_spin.setValue(max(0, center_x))
        self.y_spin.setValue(max(0, center_y))
    
    def validate_and_update_info(self):
        """验证区域并更新信息显示"""
        x = self.x_spin.value()
        y = self.y_spin.value()
        width = self.width_spin.value()
        height = self.height_spin.value()
        
        # 更新区域信息显示
        self.region_info_label.setText(f"({x}, {y}, {width}, {height})")
        self.area_info_label.setText(f"{width * height} 像素")
        
        # 验证区域有效性
        is_valid, error_msg = self.validate_region(x, y, width, height)
        
        if is_valid:
            self.valid_info_label.setText("有效")
            self.valid_info_label.setStyleSheet("color: green;")
            self.apply_btn.setEnabled(True)
        else:
            self.valid_info_label.setText(f"无效: {error_msg}")
            self.valid_info_label.setStyleSheet("color: red;")
            self.apply_btn.setEnabled(False)
    
    def validate_region(self, x, y, width, height):
        """
        验证区域有效性
        
        Args:
            x, y, width, height: 区域参数
            
        Returns:
            (是否有效, 错误信息)
        """
        if width <= 0 or height <= 0:
            return False, "宽度和高度必须大于0"
        
        if self.image_shape is None:
            return True, ""  # 没有图像形状信息时暂时认为有效
        
        img_height, img_width = self.image_shape
        
        if x < 0 or y < 0:
            return False, "坐标不能为负数"
        
        if x + width > img_width or y + height > img_height:
            return False, f"区域超出图像边界 ({img_width}x{img_height})"
        
        return True, ""
    
    def apply_region(self):
        """应用区域设置"""
        x = self.x_spin.value()
        y = self.y_spin.value()
        width = self.width_spin.value()
        height = self.height_spin.value()
        
        is_valid, error_msg = self.validate_region(x, y, width, height)
        
        if is_valid:
            self.current_region = (x, y, width, height)
            self.crop_btn.setEnabled(True)
            self.region_changed.emit(x, y, width, height)
            logger.info(f"应用区域: ({x}, {y}, {width}, {height})")
        else:
            QMessageBox.warning(self, "区域无效", f"无法应用区域设置:\n{error_msg}")
    
    def clear_region(self):
        """清除区域设置"""
        self.current_region = None
        self.crop_btn.setEnabled(False)
        self.region_cleared.emit()
        logger.info("清除区域设置")
    
    def request_crop(self):
        """请求执行裁剪"""
        if self.current_region is not None:
            self.crop_requested.emit()
        else:
            QMessageBox.warning(self, "未设置区域", "请先设置裁剪区域")
    
    def set_preset_region(self, scale_factor):
        """
        设置预设区域
        
        Args:
            scale_factor: 缩放因子 (0-1)
        """
        if self.image_shape is None:
            QMessageBox.warning(self, "未加载图像", "请先加载图像")
            return
        
        img_height, img_width = self.image_shape
        
        # 计算区域尺寸
        region_width = int(img_width * scale_factor)
        region_height = int(img_height * scale_factor)
        
        # 计算居中位置
        x = (img_width - region_width) // 2
        y = (img_height - region_height) // 2
        
        # 设置数值
        self.x_spin.setValue(x)
        self.y_spin.setValue(y)
        self.width_spin.setValue(region_width)
        self.height_spin.setValue(region_height)
        
        logger.info(f"设置预设区域 (缩放因子: {scale_factor}): ({x}, {y}, {region_width}, {region_height})")
    
    def set_region(self, x, y, width, height):
        """
        设置区域参数
        
        Args:
            x, y, width, height: 区域参数
        """
        self.x_spin.setValue(x)
        self.y_spin.setValue(y)
        self.width_spin.setValue(width)
        self.height_spin.setValue(height)
    
    def get_region(self):
        """获取当前区域"""
        return self.current_region
