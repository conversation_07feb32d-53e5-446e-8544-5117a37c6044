#!/usr/bin/env python3
"""
CT区域选框分割工具启动脚本

启动图形界面应用程序
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('ct_region_cropper.log')
    ]
)

logger = logging.getLogger(__name__)


def check_dependencies():
    """检查依赖包"""
    required_packages = [
        'PyQt5',
        'numpy',
        'pydicom',
        'SimpleITK',
        'matplotlib',
        'scipy'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package)
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("缺少以下依赖包:")
        for package in missing_packages:
            print(f"  - {package}")
        print("\n请运行以下命令安装依赖:")
        print("pip install -r requirements.txt")
        return False
    
    return True


def main():
    """主函数"""
    print("CT区域选框分割工具 v1.0")
    print("=" * 40)
    
    # 检查依赖
    if not check_dependencies():
        sys.exit(1)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CTRegionCropperApp
        
        # 创建应用程序
        app = QApplication(sys.argv)
        app.setApplicationName("CT区域选框分割工具")
        app.setApplicationVersion("1.0")
        
        # 创建主窗口
        window = CTRegionCropperApp()
        window.show()
        
        logger.info("应用程序启动成功")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except Exception as e:
        logger.error(f"启动应用程序失败: {e}")
        print(f"启动失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
