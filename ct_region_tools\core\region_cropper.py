"""
CT区域分割核心模块

实现CT影像的区域选框分割功能
"""

import os
import logging
import numpy as np
from typing import Tuple, Optional, Dict, Any
from .dicom_handler import DICOMHandler
from .metadata_manager import MetadataManager

logger = logging.getLogger(__name__)


class CTRegionCropper:
    """CT区域分割器"""
    
    def __init__(self):
        self.dicom_handler = DICOMHandler()
        self.metadata_manager = MetadataManager()
        self.image_array = None
        self.metadata = {}
        self.crop_region = None
        self.cropped_array = None
        
    def load_dicom_series(self, directory: str) -> bool:
        """
        加载DICOM系列文件
        
        Args:
            directory: DICOM文件目录路径
            
        Returns:
            加载是否成功
        """
        try:
            self.image_array, self.metadata = self.dicom_handler.load_dicom_series(directory)
            
            # 提取详细元数据
            self.metadata_manager.extract_metadata(self.dicom_handler.original_datasets)
            
            logger.info(f"成功加载DICOM系列，形状: {self.image_array.shape}")
            return True
            
        except Exception as e:
            logger.error(f"加载DICOM系列失败: {e}")
            return False
    
    def set_crop_region(self, x: int, y: int, width: int, height: int) -> bool:
        """
        设置裁剪区域
        
        Args:
            x: 左上角X坐标
            y: 左上角Y坐标
            width: 宽度
            height: 高度
            
        Returns:
            设置是否成功
        """
        try:
            if self.image_array is None:
                raise ValueError("请先加载DICOM系列")
            
            # 验证裁剪区域是否在图像范围内
            img_height, img_width = self.image_array.shape[1], self.image_array.shape[2]
            
            if x < 0 or y < 0 or x + width > img_width or y + height > img_height:
                raise ValueError(f"裁剪区域超出图像边界。图像尺寸: {img_width}x{img_height}, "
                               f"裁剪区域: ({x}, {y}, {width}, {height})")
            
            if width <= 0 or height <= 0:
                raise ValueError("裁剪区域的宽度和高度必须大于0")
            
            self.crop_region = (x, y, width, height)
            logger.info(f"设置裁剪区域: ({x}, {y}, {width}, {height})")
            return True
            
        except Exception as e:
            logger.error(f"设置裁剪区域失败: {e}")
            return False
    
    def crop_all_slices(self) -> bool:
        """
        对所有切片执行裁剪操作
        
        Returns:
            裁剪是否成功
        """
        try:
            if self.image_array is None:
                raise ValueError("请先加载DICOM系列")
            
            if self.crop_region is None:
                raise ValueError("请先设置裁剪区域")
            
            x, y, width, height = self.crop_region
            
            # 对所有切片执行裁剪
            self.cropped_array = self.image_array[:, y:y+height, x:x+width]
            
            logger.info(f"成功裁剪所有切片，新形状: {self.cropped_array.shape}")
            return True
            
        except Exception as e:
            logger.error(f"裁剪切片失败: {e}")
            return False
    
    def crop_single_slice(self, slice_index: int) -> Optional[np.ndarray]:
        """
        裁剪单个切片
        
        Args:
            slice_index: 切片索引
            
        Returns:
            裁剪后的切片数组或None
        """
        try:
            if self.image_array is None:
                raise ValueError("请先加载DICOM系列")
            
            if self.crop_region is None:
                raise ValueError("请先设置裁剪区域")
            
            if slice_index < 0 or slice_index >= self.image_array.shape[0]:
                raise ValueError(f"切片索引超出范围: {slice_index}")
            
            x, y, width, height = self.crop_region
            cropped_slice = self.image_array[slice_index, y:y+height, x:x+width]
            
            return cropped_slice
            
        except Exception as e:
            logger.error(f"裁剪单个切片失败: {e}")
            return None
    
    def save_cropped_series(self, output_directory: str) -> bool:
        """
        保存裁剪后的DICOM系列
        
        Args:
            output_directory: 输出目录路径
            
        Returns:
            保存是否成功
        """
        try:
            if self.cropped_array is None:
                raise ValueError("请先执行裁剪操作")
            
            # 使用DICOM处理器保存系列
            self.dicom_handler.save_dicom_series(
                self.cropped_array, 
                output_directory, 
                self.crop_region
            )
            
            logger.info(f"成功保存裁剪后的DICOM系列到: {output_directory}")
            return True
            
        except Exception as e:
            logger.error(f"保存裁剪后的系列失败: {e}")
            return False
    
    def get_slice_preview(self, slice_index: int, with_crop_overlay: bool = False) -> Optional[np.ndarray]:
        """
        获取切片预览图像
        
        Args:
            slice_index: 切片索引
            with_crop_overlay: 是否显示裁剪区域覆盖层
            
        Returns:
            预览图像数组或None
        """
        try:
            if self.image_array is None:
                raise ValueError("请先加载DICOM系列")
            
            if slice_index < 0 or slice_index >= self.image_array.shape[0]:
                raise ValueError(f"切片索引超出范围: {slice_index}")
            
            slice_image = self.image_array[slice_index].copy()
            
            # 如果需要显示裁剪区域覆盖层
            if with_crop_overlay and self.crop_region is not None:
                x, y, width, height = self.crop_region
                
                # 创建覆盖层（在裁剪区域边界绘制矩形）
                # 这里简单地将边界像素设置为最大值
                max_val = np.max(slice_image)
                
                # 绘制矩形边框
                slice_image[y:y+2, x:x+width] = max_val  # 上边
                slice_image[y+height-2:y+height, x:x+width] = max_val  # 下边
                slice_image[y:y+height, x:x+2] = max_val  # 左边
                slice_image[y:y+height, x+width-2:x+width] = max_val  # 右边
            
            return slice_image
            
        except Exception as e:
            logger.error(f"获取切片预览失败: {e}")
            return None
    
    def get_image_info(self) -> Dict[str, Any]:
        """
        获取图像信息
        
        Returns:
            图像信息字典
        """
        if self.image_array is None:
            return {}
        
        info = {
            'shape': self.image_array.shape,
            'dtype': str(self.image_array.dtype),
            'min_value': float(np.min(self.image_array)),
            'max_value': float(np.max(self.image_array)),
            'mean_value': float(np.mean(self.image_array)),
            'std_value': float(np.std(self.image_array)),
        }
        
        # 添加元数据信息
        if self.metadata:
            info.update({
                'spacing': self.metadata.get('spacing', None),
                'origin': self.metadata.get('origin', None),
                'patient_id': self.metadata.get('patient_id', ''),
                'study_description': self.metadata.get('study_description', ''),
                'series_description': self.metadata.get('series_description', ''),
                'modality': self.metadata.get('modality', ''),
            })
        
        # 添加裁剪区域信息
        if self.crop_region:
            info['crop_region'] = self.crop_region
        
        if self.cropped_array is not None:
            info['cropped_shape'] = self.cropped_array.shape
        
        return info
    
    def reset(self):
        """重置分割器状态"""
        self.image_array = None
        self.metadata = {}
        self.crop_region = None
        self.cropped_array = None
        self.dicom_handler = DICOMHandler()
        self.metadata_manager = MetadataManager()
        logger.info("分割器状态已重置")
    
    def validate_crop_region(self, x: int, y: int, width: int, height: int) -> Tuple[bool, str]:
        """
        验证裁剪区域是否有效
        
        Args:
            x: 左上角X坐标
            y: 左上角Y坐标
            width: 宽度
            height: 高度
            
        Returns:
            (是否有效, 错误信息)
        """
        if self.image_array is None:
            return False, "请先加载DICOM系列"
        
        if width <= 0 or height <= 0:
            return False, "宽度和高度必须大于0"
        
        img_height, img_width = self.image_array.shape[1], self.image_array.shape[2]
        
        if x < 0 or y < 0:
            return False, "坐标不能为负数"
        
        if x + width > img_width or y + height > img_height:
            return False, f"裁剪区域超出图像边界 (图像尺寸: {img_width}x{img_height})"
        
        return True, ""
