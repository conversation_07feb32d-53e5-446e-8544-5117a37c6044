#!/usr/bin/env python3
"""
CT区域选框分割工具演示脚本

演示如何使用CT区域选框分割工具进行DICOM图像处理
"""

import sys
import os
import numpy as np
import tempfile
import shutil
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from core.region_cropper import CTRegionCropper
from utils.file_utils import FileUtils
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def create_demo_dicom_data():
    """创建演示用的DICOM数据"""
    try:
        import pydicom
        from datetime import datetime
        
        # 创建临时目录
        temp_dir = tempfile.mkdtemp(prefix="ct_demo_")
        
        # 创建模拟的CT数据
        num_slices = 20
        height, width = 256, 256
        
        print(f"创建演示DICOM数据到: {temp_dir}")
        
        for i in range(num_slices):
            # 创建模拟的CT图像数据
            # 模拟一个中心有高密度区域的CT图像
            image_data = np.random.randint(-1000, -200, (height, width), dtype=np.int16)
            
            # 在中心添加一个高密度区域（模拟器官）
            center_x, center_y = width // 2, height // 2
            radius = 50
            y, x = np.ogrid[:height, :width]
            mask = (x - center_x)**2 + (y - center_y)**2 <= radius**2
            image_data[mask] = np.random.randint(50, 200, np.sum(mask))
            
            # 创建DICOM数据集
            ds = pydicom.Dataset()
            
            # 设置文件元信息
            file_meta = pydicom.Dataset()
            file_meta.MediaStorageSOPClassUID = '1.2.840.10008.*******.1.2'
            file_meta.MediaStorageSOPInstanceUID = pydicom.uid.generate_uid()
            file_meta.ImplementationClassUID = '1.2.276.0.7230010.3.0.3.6.1'
            file_meta.TransferSyntaxUID = pydicom.uid.ImplicitVRLittleEndian
            ds.file_meta = file_meta
            
            # 设置患者信息
            ds.PatientName = "Demo^Patient"
            ds.PatientID = "DEMO001"
            ds.PatientBirthDate = "19800101"
            ds.PatientSex = "M"
            
            # 设置检查信息
            ds.StudyDate = datetime.now().strftime('%Y%m%d')
            ds.StudyTime = datetime.now().strftime('%H%M%S')
            ds.StudyDescription = "CT Demo Study"
            ds.StudyInstanceUID = "*******.*******.**********.13.14.15"
            ds.AccessionNumber = "DEMO001"
            
            # 设置系列信息
            ds.SeriesDate = datetime.now().strftime('%Y%m%d')
            ds.SeriesTime = datetime.now().strftime('%H%M%S')
            ds.SeriesDescription = "CT Demo Series"
            ds.SeriesInstanceUID = "*******.*******.**********.13.14.16"
            ds.SeriesNumber = "1"
            ds.Modality = "CT"
            
            # 设置图像信息
            ds.InstanceNumber = str(i + 1)
            ds.SOPInstanceUID = pydicom.uid.generate_uid()
            ds.SOPClassUID = '1.2.840.10008.*******.1.2'
            
            # 设置图像参数
            ds.Rows = height
            ds.Columns = width
            ds.BitsAllocated = 16
            ds.BitsStored = 16
            ds.HighBit = 15
            ds.PixelRepresentation = 1
            ds.PhotometricInterpretation = "MONOCHROME2"
            ds.SamplesPerPixel = 1
            
            # 设置几何信息
            ds.PixelSpacing = [1.0, 1.0]
            ds.SliceThickness = 2.0
            ds.SpacingBetweenSlices = 2.0
            ds.ImagePositionPatient = [0.0, 0.0, float(i * 2.0)]
            ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
            ds.SliceLocation = float(i * 2.0)
            
            # 设置CT特定参数
            ds.RescaleIntercept = 0
            ds.RescaleSlope = 1
            ds.RescaleType = "HU"
            ds.WindowCenter = "0"
            ds.WindowWidth = "400"
            
            # 设置像素数据
            ds.PixelData = image_data.tobytes()
            
            # 保存DICOM文件
            filename = f"slice_{i+1:03d}.dcm"
            filepath = os.path.join(temp_dir, filename)
            ds.save_as(filepath, write_like_original=False)
        
        print(f"成功创建 {num_slices} 个演示DICOM文件")
        return temp_dir
        
    except ImportError:
        print("错误: 需要安装pydicom包来创建演示数据")
        print("请运行: pip install pydicom")
        return None
    except Exception as e:
        print(f"创建演示数据失败: {e}")
        return None


def demo_basic_usage():
    """演示基本使用方法"""
    print("\n" + "="*60)
    print("CT区域选框分割工具 - 基本使用演示")
    print("="*60)
    
    # 创建演示数据
    demo_dir = create_demo_dicom_data()
    if not demo_dir:
        return False
    
    try:
        # 创建输出目录
        output_dir = tempfile.mkdtemp(prefix="ct_output_")
        
        print(f"\n1. 创建CT区域分割器...")
        cropper = CTRegionCropper()
        
        print(f"2. 加载DICOM系列: {demo_dir}")
        success = cropper.load_dicom_series(demo_dir)
        if not success:
            print("❌ 加载DICOM系列失败")
            return False
        
        # 显示图像信息
        info = cropper.get_image_info()
        print(f"   ✅ 加载成功!")
        print(f"   - 图像形状: {info.get('shape', 'N/A')}")
        print(f"   - 数据类型: {info.get('dtype', 'N/A')}")
        print(f"   - 患者ID: {info.get('patient_id', 'N/A')}")
        print(f"   - 模态: {info.get('modality', 'N/A')}")
        
        print(f"\n3. 设置裁剪区域...")
        # 设置一个中心区域进行裁剪
        if cropper.image_array is not None:
            height, width = cropper.image_array.shape[1], cropper.image_array.shape[2]
            
            # 设置一个100x100的中心区域
            crop_size = 100
            x = (width - crop_size) // 2
            y = (height - crop_size) // 2
            
            success = cropper.set_crop_region(x, y, crop_size, crop_size)
            if success:
                print(f"   ✅ 设置裁剪区域: ({x}, {y}, {crop_size}, {crop_size})")
            else:
                print("   ❌ 设置裁剪区域失败")
                return False
        
        print(f"\n4. 执行裁剪操作...")
        success = cropper.crop_all_slices()
        if success:
            print(f"   ✅ 裁剪完成!")
            print(f"   - 原始形状: {cropper.image_array.shape}")
            print(f"   - 裁剪后形状: {cropper.cropped_array.shape}")
        else:
            print("   ❌ 裁剪操作失败")
            return False
        
        print(f"\n5. 保存结果到: {output_dir}")
        success = cropper.save_cropped_series(output_dir)
        if success:
            print("   ✅ 保存完成!")
            
            # 统计输出文件
            output_files = [f for f in os.listdir(output_dir) if f.endswith('.dcm')]
            print(f"   - 输出文件数: {len(output_files)}")
            
            # 计算文件大小
            total_size = sum(os.path.getsize(os.path.join(output_dir, f)) for f in output_files)
            print(f"   - 总文件大小: {FileUtils.format_file_size(total_size)}")
        else:
            print("   ❌ 保存结果失败")
            return False
        
        print(f"\n6. 验证输出文件...")
        # 验证输出的DICOM文件
        try:
            import pydicom
            sample_file = os.path.join(output_dir, output_files[0])
            ds = pydicom.dcmread(sample_file)
            print(f"   ✅ DICOM文件验证通过!")
            print(f"   - 图像尺寸: {ds.Rows} x {ds.Columns}")
            print(f"   - 患者ID: {ds.PatientID}")
            print(f"   - 系列描述: {ds.SeriesDescription}")
        except Exception as e:
            print(f"   ⚠️  DICOM文件验证失败: {e}")
        
        print(f"\n✅ 演示完成!")
        print(f"演示数据位置: {demo_dir}")
        print(f"输出结果位置: {output_dir}")
        print(f"\n注意: 演示完成后，临时文件将被自动清理")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示过程中发生错误: {e}")
        return False
    
    finally:
        # 清理临时文件
        try:
            if demo_dir and os.path.exists(demo_dir):
                shutil.rmtree(demo_dir)
            if 'output_dir' in locals() and os.path.exists(output_dir):
                shutil.rmtree(output_dir)
        except:
            pass


def demo_gui_launch():
    """演示GUI界面启动"""
    print("\n" + "="*60)
    print("CT区域选框分割工具 - GUI界面演示")
    print("="*60)
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CTRegionCropperApp
        
        print("正在启动GUI界面...")
        print("\nGUI使用说明:")
        print("1. 点击'加载DICOM文件夹'选择包含DICOM文件的文件夹")
        print("2. 在图像上拖拽选择裁剪区域，或使用右侧面板精确设置")
        print("3. 点击'执行裁剪'进行裁剪操作")
        print("4. 点击'保存裁剪结果'保存结果")
        print("\n关闭GUI窗口返回演示菜单...")
        
        app = QApplication(sys.argv)
        window = CTRegionCropperApp()
        window.show()
        
        app.exec_()
        
    except ImportError as e:
        print(f"❌ GUI依赖缺失: {e}")
        print("请安装PyQt5: pip install PyQt5")
    except Exception as e:
        print(f"❌ 启动GUI失败: {e}")


def demo_validation():
    """演示验证功能"""
    print("\n" + "="*60)
    print("CT区域选框分割工具 - 功能验证演示")
    print("="*60)
    
    print("运行功能测试...")
    
    try:
        from tests.test_cropper import run_tests
        success = run_tests()
        
        if success:
            print("\n✅ 所有功能测试通过!")
        else:
            print("\n❌ 部分功能测试失败")
        
        return success
        
    except Exception as e:
        print(f"❌ 运行测试失败: {e}")
        return False


def main():
    """主函数"""
    print("🏥 CT区域选框分割工具 - 交互式演示")
    print("=" * 60)
    print("这是一个专门用于CT影像区域选框分割的工具演示程序")
    print("支持DICOM格式输入输出，保持原有元数据信息")
    
    while True:
        print("\n📋 请选择演示项目:")
        print("1. 基本使用演示 (创建演示数据并执行完整流程)")
        print("2. GUI界面演示 (启动图形界面)")
        print("3. 功能验证演示 (运行所有测试)")
        print("4. 查看项目信息")
        print("0. 退出演示")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            demo_basic_usage()
        elif choice == '2':
            demo_gui_launch()
        elif choice == '3':
            demo_validation()
        elif choice == '4':
            show_project_info()
        elif choice == '0':
            print("\n👋 感谢使用CT区域选框分割工具演示!")
            break
        else:
            print("❌ 无效选择，请重新输入")


def show_project_info():
    """显示项目信息"""
    print("\n" + "="*60)
    print("CT区域选框分割工具 - 项目信息")
    print("="*60)
    
    print("📦 项目结构:")
    print("ct_region_cropper/")
    print("├── core/                   # 核心功能模块")
    print("│   ├── dicom_handler.py    # DICOM文件处理")
    print("│   ├── region_cropper.py   # 区域分割功能")
    print("│   └── metadata_manager.py # 元数据管理")
    print("├── gui/                    # 图形界面")
    print("│   ├── main_window.py      # 主窗口")
    print("│   ├── image_viewer.py     # 图像显示组件")
    print("│   └── region_selector.py  # 区域选择组件")
    print("├── utils/                  # 工具函数")
    print("│   ├── image_utils.py      # 图像处理工具")
    print("│   └── file_utils.py       # 文件处理工具")
    print("├── tests/                  # 测试文件")
    print("├── examples/               # 示例代码")
    print("└── 使用说明.md             # 详细使用说明")
    
    print("\n🔧 主要功能:")
    print("• DICOM文件读取和写入")
    print("• 交互式区域选择")
    print("• 多层面批量分割")
    print("• 元数据完整保持")
    print("• 实时图像预览")
    print("• 窗宽窗位调整")
    
    print("\n📋 技术特点:")
    print("• 完全支持DICOM标准")
    print("• 保持原有UID和几何信息")
    print("• 支持批量处理")
    print("• 提供编程和图形界面")
    print("• 包含完整的测试套件")
    
    print("\n📄 更多信息请查看:")
    print("• README.md - 项目概述")
    print("• 使用说明.md - 详细使用指南")
    print("• examples/basic_usage.py - 编程示例")


if __name__ == "__main__":
    main()
