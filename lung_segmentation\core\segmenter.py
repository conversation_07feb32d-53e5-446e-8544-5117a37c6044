import logging
import numpy as np
import <PERSON>IT<PERSON> as sitk
from typing import Optional, Dict, List, Tuple, Union
from pathlib import Path
import os
import glob
import traceback

from lung_segmentation.models.lungmask_wrapper import LungmaskModel
from lung_segmentation.models.totalseg_wrapper import TotalSegmentatorModel
from shared_utils.common.io_utils import load_image, save_image, load_nifti, save_nifti
from shared_utils.common.preprocessing import preprocess_ct_image

class LungCTSegmenter:
    """独立的肺部CT分割器，支持多种AI模型"""
    
    # Lungmask 模型标签映射
    LUNGMASK_R231_LABELS = {
        1: "right_lung",
        2: "left_lung"
    }
    LUNGMASK_LTRCLOBES_LABELS = {
        1: "left_upper_lobe",
        2: "left_lower_lobe",
        3: "right_upper_lobe",
        4: "right_middle_lobe",
        5: "right_lower_lobe"
    }
    LUNGMASK_R231COVIDWEB_LABELS = {
        1: "right_lung",
        2: "left_lung"
    }
    
    def __init__(self, model_type: str = "lungmask", 
                 model_variant: str = "R231",
                 gaussian_sigma: float = 2.0, 
                 morphological_radius: int = 1):
        """
        初始化分割器
        
        Args:
            model_type: AI模型类型 ('lungmask' 或 'totalsegmentator')
            model_variant: 模型变体 (lungmask: 'R231', 'LTRCLobes', 'R231CovidWeb')
            gaussian_sigma: 高斯平滑的sigma值
            morphological_radius: 形态学操作的半径
        """
        self.model_type = model_type
        self.model_variant = model_variant
        self.model = None
        self.logger = logging.getLogger(__name__)
        
        # 分割参数
        self.enable_lobes = False
        self.enable_airways = False
        self.enable_vessels = False
        self.smooth_results = True
        self.shrink_masks = True
        self.gaussian_sigma = gaussian_sigma
        self.morphological_radius = morphological_radius
        
        # 输出格式，默认为NIFTI
        self.output_format = "nifti"
        
        # 进度回调函数
        self.progress_callback = None
        
        # 初始化模型
        self._initialize_model()
    
    def _initialize_model(self):
        """初始化AI模型"""
        try:
            if self.model_type == "lungmask":
                self.model = LungmaskModel(self.model_variant)
            elif self.model_type == "totalsegmentator":
                self.model = TotalSegmentatorModel()
                # 设置TotalSegmentator的任务类型
                if hasattr(self.model, 'set_task'):
                    self.model.set_task(self.model_variant)
            else:
                raise ValueError(f"不支持的模型类型: {self.model_type}")
            
            self.logger.info(f"成功初始化 {self.model_type} 模型")
        except Exception as e:
            self.logger.error(f"模型初始化失败: {e}")
            raise
    
    def set_output_format(self, format_type: str):
        """
        设置输出格式
        
        Args:
            format_type: 输出格式，可选 "nifti" 或 "dicom"
        """
        format_type = format_type.lower()
        if format_type not in ["nifti", "dicom"]:
            raise ValueError(f"不支持的输出格式: {format_type}，支持的格式有: nifti, dicom")
        
        self.output_format = format_type
        self.logger.info(f"已设置输出格式为: {self.output_format}")
    
    def set_progress_callback(self, callback_function):
        """
        设置进度回调函数
        
        Args:
            callback_function: 回调函数，接受消息字符串和可选的百分比进度
        """
        self.progress_callback = callback_function
    
    def _report_progress(self, message, percent=None):
        """
        报告进度
        
        Args:
            message: 进度消息
            percent: 百分比进度(0-100)
        """
        self.logger.info(message)
        if self.progress_callback is not None:
            self.progress_callback(message, percent)
    
    def segment_from_file(self, input_path: str, output_path: str, crop_lung: bool = False, series_id: str = None) -> Dict[str, np.ndarray]:
        """
        从文件加载图像并执行分割

        Args:
            input_path: 输入图像路径（NIFTI文件或DICOM目录）
            output_path: 输出目录路径
            crop_lung: 是否裁剪肺部区域
            series_id: 指定要加载的DICOM序列ID（仅对DICOM目录有效）

        Returns:
            Dict: 包含分割结果的字典
        """
        self.logger.info(f"从 {input_path} 加载图像...")
        self._report_progress(f"从 {input_path} 加载图像...", 5)
        
        # 加载图像
        try:
            image_data, metadata = load_image(input_path, series_id)
        except Exception as e:
            error_msg = f"加载图像失败: {str(e)}"
            self.logger.error(error_msg)
            self._report_progress(error_msg, 0)
            raise
            
        self.logger.info(f"图像加载完成，形状: {image_data.shape}")
        self._report_progress(f"图像加载完成，形状: {image_data.shape}", 10)
        
        # 执行分割
        results = self.segment(image_data, metadata)
        
        # 确保输出目录存在
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        # 保存结果
        self._report_progress("正在保存分割结果...", 90)
        
        # 根据输出格式选择保存方式
        if self.output_format == "nifti":
            self._save_nifti_results(results, image_data, metadata, output_path, crop_lung)
        elif self.output_format == "dicom":
            self._save_dicom_results(results, image_data, metadata, output_path, crop_lung)
        else:
            error_msg = f"不支持的输出格式: {self.output_format}"
            self.logger.error(error_msg)
            self._report_progress(error_msg, 0)
            raise ValueError(error_msg)
            
        self._report_progress("分割完成！", 100)
        return results
        
    def segment(self, image_data: np.ndarray, metadata: Dict = None) -> Dict[str, np.ndarray]:
        """
        对3D图像数据执行肺部分割
        
        Args:
            image_data: 3D图像数据
            metadata: 图像元数据
            
        Returns:
            Dict: 包含分割结果的字典
        """
        if image_data is None or len(image_data.shape) != 3:
            error_msg = f"无效的图像数据，形状: {image_data.shape if image_data is not None else 'None'}"
            self.logger.error(error_msg)
            self._report_progress(error_msg, 0)
            raise ValueError(error_msg)
            
        self.logger.info("开始肺部分割...")
        self._report_progress("开始肺部分割...", 20)
        
        # 如果是TotalSegmentator，确保设置正确的任务类型
        if self.model_type == "totalsegmentator" and hasattr(self.model, 'set_task'):
            self.logger.info(f"为TotalSegmentator设置任务类型: {self.model_variant}")
            self.model.set_task(self.model_variant)
        
        # 使用选定的模型执行分割
        if self.model_type == "lungmask":
            lung_mask = self._segment_with_lungmask(image_data, metadata)
        elif self.model_type == "totalsegmentator":
            lung_mask = self._segment_with_totalsegmentator(image_data, metadata)
        else:
            error_msg = f"不支持的模型类型: {self.model_type}"
            self.logger.error(error_msg)
            self._report_progress(error_msg, 0)
            raise ValueError(error_msg)
            
        self._report_progress("肺部分割完成", 50)
        
        # 后处理
        self._report_progress("正在进行后处理...", 60)
        if self.smooth_results:
            self.logger.info(f"使用高斯平滑处理结果 (sigma={self.gaussian_sigma})...")
            lung_mask = self._smooth_mask(lung_mask)
            
        if self.shrink_masks:
            self.logger.info(f"使用形态学操作处理结果 (radius={self.morphological_radius})...")
            lung_mask = self._apply_morphological_operations(lung_mask)
            
        # 创建肺部区域的裁剪版本
        self._report_progress("正在创建肺部裁剪版本...", 70)
        cropped_lung = self._crop_lung_region(image_data, lung_mask)
        
        # 检查裁剪结果
        if cropped_lung is not None:
            non_background_pixels = np.count_nonzero(cropped_lung != -1000)
            total_pixels = cropped_lung.size
            percentage = (non_background_pixels / total_pixels) * 100 if total_pixels > 0 else 0
            self.logger.info(f"裁剪肺部: 非背景像素={non_background_pixels} ({percentage:.2f}%)")
            
            if percentage < 1:
                self.logger.warning("裁剪后的肺部图像几乎全是背景值，可能是分割失败")
        
        # 计算肺部统计信息
        self._report_progress("正在计算肺部统计信息...", 80)
        stats = self._calculate_lung_stats(image_data, lung_mask)
        
        self.logger.info(f"肺部统计信息: {stats}")
        
        # 返回结果字典
        results = {
            "lung_mask": lung_mask,
            "cropped_lung": cropped_lung,
            "stats": stats
        }
        
        # 添加额外的分割结果（如果有）
        if hasattr(self, 'lobes_mask') and self.lobes_mask is not None:
            results["lobes_mask"] = self.lobes_mask
            
        if hasattr(self, 'airways_mask') and self.airways_mask is not None:
            results["airways_mask"] = self.airways_mask
            
        if hasattr(self, 'vessels_mask') and self.vessels_mask is not None:
            results["vessels_mask"] = self.vessels_mask
            
        # 记录返回的结果类型
        result_types = [f"{k}: {type(v).__name__}" for k, v in results.items() if isinstance(v, np.ndarray)]
        self.logger.info(f"返回结果类型: {result_types}")
        
        return results

    def _segment_with_lungmask(self, image: np.ndarray, metadata: Dict = None) -> np.ndarray:
        """使用Lungmask模型进行分割"""
        segmentation = self.model.segment(image)
        results = {}
        
        label_map = {}
        if self.model_variant == "R231":
            label_map = self.LUNGMASK_R231_LABELS
        elif self.model_variant == "LTRCLobes":
            label_map = self.LUNGMASK_LTRCLOBES_LABELS
        elif self.model_variant == "R231CovidWeb":
            label_map = self.LUNGMASK_R231COVIDWEB_LABELS
        
        for value, name in label_map.items():
            results[name] = (segmentation == value).astype(np.uint8)
            
        # 合并左右肺为整个肺部掩码
        if "left_lung" in results and "right_lung" in results:
            results["lung_mask"] = (results["left_lung"] | results["right_lung"]).astype(np.uint8)
        elif "left_upper_lobe" in results and "left_lower_lobe" in results and "right_upper_lobe" in results:
            # 如果有肺叶分割，合并所有肺叶
            left_lung = (results["left_upper_lobe"] | results["left_lower_lobe"]).astype(np.uint8)
            right_lung = np.zeros_like(left_lung)
            if "right_upper_lobe" in results:
                right_lung = right_lung | results["right_upper_lobe"]
            if "right_middle_lobe" in results:
                right_lung = right_lung | results["right_middle_lobe"]
            if "right_lower_lobe" in results:
                right_lung = right_lung | results["right_lower_lobe"]
            right_lung = right_lung.astype(np.uint8)
            results["left_lung"] = left_lung
            results["right_lung"] = right_lung
            results["lung_mask"] = (left_lung | right_lung).astype(np.uint8)
            
        # 确保有lung_mask
        if "lung_mask" not in results:
            # 使用第一个结果作为lung_mask
            first_key = list(results.keys())[0] if results else None
            if first_key:
                results["lung_mask"] = results[first_key]
            else:
                # 创建空掩码
                results["lung_mask"] = np.zeros_like(segmentation, dtype=np.uint8)
                
        return results["lung_mask"]
    
    def _segment_with_totalsegmentator(self, image: np.ndarray, metadata: Dict = None) -> np.ndarray:
        """使用TotalSegmentator模型进行分割"""
        try:
            # 记录调试信息
            self.logger.info(f"开始使用TotalSegmentator进行分割，任务类型: {self.model_variant}")
            self._report_progress(f"使用TotalSegmentator进行分割 ({self.model_variant})...", 30)
            
            # 执行分割
            segmentation_dict = self.model.segment(image, 
                                                enable_lobes=self.enable_lobes,
                                                enable_airways=self.enable_airways,
                                                enable_vessels=self.enable_vessels)
            
            # 记录分割结果的键
            self.logger.info(f"TotalSegmentator分割结果包含以下组件: {list(segmentation_dict.keys())}")
            
            # 检查分割结果是否为空
            if not segmentation_dict:
                self.logger.error("TotalSegmentator返回空结果")
                # 创建默认掩码
                lung_mask = np.zeros_like(image, dtype=np.uint8)
                return lung_mask
                
            # 保存额外的分割结果
            lung_mask = None
            
            # 尝试获取肺部掩码
            if "lung" in segmentation_dict:
                # 使用整个肺部掩码
                lung_mask = segmentation_dict["lung"].astype(np.uint8)
                self.logger.info(f"使用'lung'作为肺部掩码，形状: {lung_mask.shape}, 非零像素: {np.count_nonzero(lung_mask)}")
            elif "left_lung" in segmentation_dict and "right_lung" in segmentation_dict:
                # 合并左右肺为整个肺部掩码
                lung_mask = (segmentation_dict["left_lung"] | segmentation_dict["right_lung"]).astype(np.uint8)
                self.logger.info(f"合并左右肺作为肺部掩码，形状: {lung_mask.shape}, 非零像素: {np.count_nonzero(lung_mask)}")
            
            # 如果没有找到肺部掩码，尝试从肺叶合并
            if lung_mask is None or np.count_nonzero(lung_mask) == 0:
                lobes_keys = ["left_upper_lobe", "left_lower_lobe", "right_upper_lobe", "right_middle_lobe", "right_lower_lobe"]
                available_lobes = [key for key in lobes_keys if key in segmentation_dict]
                
                if available_lobes:
                    self.logger.info(f"从肺叶合并肺部掩码，可用肺叶: {available_lobes}")
                    if lung_mask is None:
                        lung_mask = np.zeros_like(image, dtype=np.uint8)
                    
                    # 合并所有可用肺叶
                    for lobe_name in available_lobes:
                        lobe_mask = segmentation_dict[lobe_name]
                        if np.count_nonzero(lobe_mask) > 0:
                            lung_mask = lung_mask | lobe_mask
                    
                    lung_mask = lung_mask.astype(np.uint8)
                    self.logger.info(f"从肺叶合并的肺部掩码，形状: {lung_mask.shape}, 非零像素: {np.count_nonzero(lung_mask)}")
            
            # 尝试从其他肺部相关结构创建掩码
            if lung_mask is None or np.count_nonzero(lung_mask) == 0:
                lung_related_keys = [key for key in segmentation_dict.keys() if 'lung' in key.lower()]
                if lung_related_keys:
                    self.logger.info(f"尝试从其他肺部相关结构创建掩码: {lung_related_keys}")
                    if lung_mask is None:
                        lung_mask = np.zeros_like(image, dtype=np.uint8)
                    
                    # 合并所有肺部相关结构
                    for key in lung_related_keys:
                        if key != 'lung_vessels' and key != 'airways':  # 排除血管和气道
                            mask = segmentation_dict[key]
                            if np.count_nonzero(mask) > 0:
                                lung_mask = lung_mask | mask
                    
                    lung_mask = lung_mask.astype(np.uint8)
                    self.logger.info(f"从其他肺部相关结构创建的掩码，非零像素: {np.count_nonzero(lung_mask)}")
            
            # 如果仍然没有找到肺部掩码，创建空掩码
            if lung_mask is None:
                self.logger.warning("未找到肺部掩码，创建空掩码")
                lung_mask = np.zeros_like(image, dtype=np.uint8)
                
            # 检查掩码是否为空（全零）
            if np.count_nonzero(lung_mask) == 0:
                self.logger.warning("肺部掩码为空（全零），可能分割失败")
                
                # 尝试一种简单的阈值分割方法作为后备
                self.logger.info("尝试使用简单阈值分割作为后备方法")
                try:
                    # 假设CT值在[-1000, -700]范围内的是肺部
                    threshold_mask = ((image >= -1000) & (image <= -700)).astype(np.uint8)
                    # 移除小区域
                    from scipy import ndimage
                    labeled, num_features = ndimage.label(threshold_mask)
                    sizes = ndimage.sum(threshold_mask, labeled, range(1, num_features + 1))
                    # 保留最大的两个区域（假设是左右肺）
                    if len(sizes) > 1:
                        sorted_sizes = sorted(zip(sizes, range(1, num_features + 1)), reverse=True)
                        keep_labels = [label for _, label in sorted_sizes[:2]]
                        threshold_mask = np.isin(labeled, keep_labels).astype(np.uint8)
                        
                    if np.count_nonzero(threshold_mask) > 0:
                        lung_mask = threshold_mask
                        self.logger.info(f"使用阈值分割创建肺部掩码，非零像素: {np.count_nonzero(lung_mask)}")
                except Exception as e:
                    self.logger.error(f"阈值分割失败: {e}")
            
            # 保存其他分割结果供后续使用
            if self.enable_lobes:
                lobes_present = any(key in segmentation_dict for key in ["left_upper_lobe", "left_lower_lobe", "right_upper_lobe"])
                if lobes_present:
                    self.lobes_mask = np.zeros_like(lung_mask, dtype=np.uint8)
                    lobe_values = {
                        "left_upper_lobe": 1,
                        "left_lower_lobe": 2,
                        "right_upper_lobe": 3,
                        "right_middle_lobe": 4,
                        "right_lower_lobe": 5
                    }
                    for lobe_name, value in lobe_values.items():
                        if lobe_name in segmentation_dict and np.count_nonzero(segmentation_dict[lobe_name]) > 0:
                            self.lobes_mask[segmentation_dict[lobe_name] > 0] = value
                            
                    self.logger.info(f"保存肺叶掩码，非零像素: {np.count_nonzero(self.lobes_mask)}")
            
            if self.enable_airways and "airways" in segmentation_dict:
                self.airways_mask = segmentation_dict["airways"].astype(np.uint8)
                self.logger.info(f"保存气道掩码，非零像素: {np.count_nonzero(self.airways_mask)}")
                
            if self.enable_vessels and "lung_vessels" in segmentation_dict:
                self.vessels_mask = segmentation_dict["lung_vessels"].astype(np.uint8)
                self.logger.info(f"保存血管掩码，非零像素: {np.count_nonzero(self.vessels_mask)}")
            
            return lung_mask
            
        except Exception as e:
            self.logger.error(f"TotalSegmentator分割过程中出错: {e}")
            self.logger.error(traceback.format_exc())
            # 创建空掩码
            return np.zeros_like(image, dtype=np.uint8)
    
    def _smooth_mask(self, mask: np.ndarray) -> np.ndarray:
        """平滑分割结果"""
        smoothed = sitk.SmoothingRecursiveGaussian(sitk.GetImageFromArray(mask.astype(np.float32)), self.gaussian_sigma)
        return sitk.GetArrayFromImage(smoothed) > 0.5
    
    def _apply_morphological_operations(self, mask: np.ndarray) -> np.ndarray:
        """收缩掩码边界"""
        image = sitk.GetImageFromArray(mask.astype(np.uint8))
        eroded = sitk.BinaryErode(image, [self.morphological_radius] * 3)
        shrinked_mask = sitk.GetArrayFromImage(eroded)
        
        return shrinked_mask
    
    def _crop_lung_region(self, image: np.ndarray, mask: np.ndarray) -> np.ndarray:
        """裁剪肺部区域"""
        background_value = -1000  # 背景区域填充值，默认为-1000 (空气的HU值)
        
        # 检查掩码是否为空
        if np.count_nonzero(mask) == 0:
            self.logger.warning("裁剪肺部区域时发现掩码为空，返回原始图像")
            return image.copy()
            
        # 裁剪肺部区域
        cropped = image.copy()
        cropped[mask == 0] = background_value
        
        self.logger.info(f"裁剪肺部区域完成，原始图像范围: [{np.min(image)}, {np.max(image)}], " 
                        f"裁剪后范围: [{np.min(cropped)}, {np.max(cropped)}]")
        
        # 检查裁剪结果是否只有背景值
        if np.all(cropped == background_value):
            self.logger.warning("裁剪后的图像只包含背景值，可能是掩码有问题")
            return image.copy()
            
        return cropped
    
    def _calculate_lung_stats(self, image: np.ndarray, mask: np.ndarray) -> Dict:
        """计算肺部统计信息"""
        non_zero = np.count_nonzero(mask)
        total_pixels = mask.size
        percentage = (non_zero / total_pixels) * 100 if total_pixels > 0 else 0
        self.logger.info(f"分割结果: 非零像素={non_zero} ({percentage:.2f}%)")
        
        return {
            "non_zero_pixels": non_zero,
            "total_pixels": total_pixels,
            "percentage": percentage
        }
    
    def _save_nifti_results(self, results: Dict[str, np.ndarray], image_data: np.ndarray, metadata: Dict, output_path: str, crop_lung: bool):
        """保存NIFTI格式的分割结果"""
        # 确保输出路径存在
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取基本文件名
        base_name = Path(output_path).stem
        
        # 遍历结果字典中的每个项目
        for name, data in results.items():
            # 跳过非数组类型的项目（如统计信息）
            if not isinstance(data, np.ndarray) or data.ndim != 3:
                continue

            # 构建完整的输出文件路径
            filename = f"{base_name}_{name}.nii.gz"
            filepath = output_dir / filename

            try:
                # 保存数据
                if name == "cropped_lung":
                    # 保存裁剪后的肺部图像
                    save_image(data, str(filepath), metadata, "nifti")
                    self.logger.info(f"保存裁剪后的肺部图像: {filepath}")
                else:
                    # 确保掩码是uint8类型
                    if data.dtype == bool:
                        data = data.astype(np.uint8)

                    # 验证掩码数据
                    if data.size == 0:
                        self.logger.warning(f"跳过空的分割结果: {name}")
                        continue

                    # 保存掩码
                    save_image(data, str(filepath), metadata, "nifti")
                    self.logger.info(f"保存分割结果: {filepath}")

            except Exception as e:
                self.logger.error(f"保存分割结果失败 {name}: {e}")
                # 尝试使用简化的文件名（移除特殊字符）
                try:
                    import re
                    safe_filename = re.sub(r'[^\w\-_.]', '_', filename)
                    safe_filepath = output_dir / safe_filename
                    self.logger.info(f"尝试使用安全文件名: {safe_filepath}")

                    if name == "cropped_lung":
                        save_image(data, str(safe_filepath), metadata, "nifti")
                    else:
                        if data.dtype == bool:
                            data = data.astype(np.uint8)
                        save_image(data, str(safe_filepath), metadata, "nifti")

                    self.logger.info(f"使用安全文件名保存成功: {safe_filepath}")

                except Exception as e2:
                    self.logger.error(f"使用安全文件名保存也失败: {e2}")
                    # 继续处理其他结果，不中断整个过程
                    continue
    
    def _save_dicom_results(self, results: Dict[str, np.ndarray], image_data: np.ndarray, metadata: Dict, output_path: str, crop_lung: bool):
        """保存DICOM格式的分割结果"""
        # 确保输出路径存在
        output_dir = Path(output_path).parent
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # 获取基本文件名
        base_name = Path(output_path).stem
        
        # 遍历结果字典中的每个项目
        for name, data in results.items():
            # 跳过非数组类型的项目（如统计信息）
            if not isinstance(data, np.ndarray) or data.ndim != 3:
                continue
                
            # 构建完整的输出文件路径
            dirname = f"{base_name}_{name}"
            filepath = output_dir / dirname
            # 确保DICOM输出目录存在
            os.makedirs(filepath, exist_ok=True)
            
            # 保存数据
            if name == "cropped_lung":
                # 保存裁剪后的肺部图像
                save_image(data, str(filepath), metadata, "dicom")
                self.logger.info(f"保存裁剪后的肺部图像: {filepath}")
            else:
                # 确保掩码是uint8类型
                if data.dtype == bool:
                    data = data.astype(np.uint8)
                    
                # 保存掩码
                save_image(data, str(filepath), metadata, "dicom")
                self.logger.info(f"保存分割结果: {filepath}")
    
    def batch_segment(self, input_dir: str, output_dir: str, crop_lung: bool = False) -> None:
        """
        批量处理目录中的所有CT图像
        
        Args:
            input_dir: 输入目录路径
            output_dir: 输出目录路径
            crop_lung: 是否裁剪肺部区域
        """
        self.logger.info(f"批量处理目录: {input_dir}")
        self._report_progress(f"批量处理目录: {input_dir}", 0)
        
        # 确保输出目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        # 如果input_dir是一个列表，表示用户选择了多个文件或目录
        if isinstance(input_dir, list):
            batch_files = input_dir
            self.logger.info(f"批量处理 {len(batch_files)} 个文件/目录")
        else:
            # 查找所有NIFTI文件
            nifti_files = glob.glob(os.path.join(input_dir, "*.nii.gz"))
            nifti_files.extend(glob.glob(os.path.join(input_dir, "*.nii")))
            
            # 递归查找所有DICOM文件和目录
            potential_dicom_dirs = []
            dicom_files_found = False
            
            # 首先检查当前目录是否直接包含DICOM文件
            direct_dicom_files = glob.glob(os.path.join(input_dir, "*.dcm"))
            if direct_dicom_files:
                potential_dicom_dirs.append(input_dir)
                dicom_files_found = True
                self.logger.info(f"在目录 {input_dir} 中直接找到 {len(direct_dicom_files)} 个DICOM文件")
            
            # 然后检查子目录
            for root, dirs, files in os.walk(input_dir):
                # 跳过当前目录，因为已经检查过了
                if root == input_dir:
                    continue
                    
                dicom_files = [f for f in files if f.lower().endswith('.dcm')]
                if dicom_files:
                    potential_dicom_dirs.append(root)
                    dicom_files_found = True
                    self.logger.info(f"在子目录 {root} 中找到 {len(dicom_files)} 个DICOM文件")
            
            batch_files = nifti_files + potential_dicom_dirs
            
        total_files = len(batch_files)
        if total_files == 0:
            self.logger.warning(f"在指定的输入路径中未找到NIFTI文件或DICOM目录")
            self._report_progress(f"在指定的输入路径中未找到NIFTI文件或DICOM目录", 100)
            return
            
        self.logger.info(f"找到 {total_files} 个文件/目录需要处理")
        self._report_progress(f"找到 {total_files} 个文件/目录需要处理", 5)
        
        # 处理所有文件
        processed_count = 0
        successful_count = 0
        failed_count = 0
        
        # 处理所有文件
        for i, file_path in enumerate(batch_files):
            file_name = os.path.basename(file_path)
            
            self.logger.info(f"处理文件 {i+1}/{total_files}: {file_name}")
            self._report_progress(f"处理文件 {i+1}/{total_files}: {file_name}", 
                               int(5 + (i / total_files) * 95))
            
            try:
                # 检查文件是否存在
                if not os.path.exists(file_path):
                    raise FileNotFoundError(f"文件或目录不存在: {file_path}")
                
                # 检查DICOM目录是否有效
                if os.path.isdir(file_path):
                    # 检查目录中是否有DICOM文件
                    dicom_files = []
                    for root, dirs, files in os.walk(file_path):
                        dicom_files.extend([os.path.join(root, f) for f in files if f.lower().endswith('.dcm')])
                    
                    if not dicom_files:
                        raise ValueError(f"目录中未找到DICOM文件: {file_path}")
                    
                    self.logger.info(f"找到 {len(dicom_files)} 个DICOM文件在 {file_path}")
                
                # 为每个文件创建输出路径
                if os.path.isdir(file_path):
                    # DICOM目录
                    output_path = os.path.join(output_dir, f"{file_name}_segmented")
                else:
                    # NIFTI文件
                    base_name = os.path.splitext(file_name)[0]
                    if base_name.endswith('.nii'):
                        base_name = os.path.splitext(base_name)[0]
                    output_path = os.path.join(output_dir, f"{base_name}_segmented")
                
                # 确保输出目录存在
                os.makedirs(os.path.dirname(output_path), exist_ok=True)
                
                # 执行分割
                results = self.segment_from_file(file_path, output_path, crop_lung)
                
                # 检查结果
                if results and "lung_mask" in results:
                    non_zero = np.count_nonzero(results["lung_mask"])
                    if non_zero > 0:
                        self.logger.info(f"成功分割 {file_name}，肺部像素: {non_zero}")
                        successful_count += 1
                    else:
                        self.logger.warning(f"警告: {file_name} 的肺部掩码为空")
                        failed_count += 1
                else:
                    self.logger.warning(f"警告: {file_name} 的分割结果不包含肺部掩码")
                    failed_count += 1
                
                processed_count += 1
                
            except Exception as e:
                self.logger.error(f"处理文件 {file_name} 时出错: {str(e)}")
                self.logger.error(traceback.format_exc())
                self._report_progress(f"处理文件 {file_name} 时出错: {str(e)}", 
                                   int(5 + (i / total_files) * 95))
                failed_count += 1
                continue
        
        self.logger.info(f"批处理完成，成功: {successful_count}, 失败: {failed_count}, 总计: {total_files}")
        self._report_progress(f"批处理完成，成功: {successful_count}, 失败: {failed_count}, 总计: {total_files}", 100)