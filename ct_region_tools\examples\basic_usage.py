"""
基本使用示例

演示CT区域选框分割工具的基本使用方法
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from core.region_cropper import CTRegionCropper
from utils.file_utils import FileUtils

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def example_programmatic_usage():
    """编程接口使用示例"""
    print("=== CT区域选框分割工具 - 编程接口示例 ===\n")
    
    # 创建分割器实例
    cropper = CTRegionCropper()
    
    # 示例DICOM文件夹路径（请替换为实际路径）
    dicom_folder = r"C:\path\to\your\dicom\folder"
    output_folder = r"C:\path\to\output\folder"
    
    # 检查输入文件夹是否存在
    if not os.path.exists(dicom_folder):
        print(f"错误: DICOM文件夹不存在: {dicom_folder}")
        print("请修改 dicom_folder 变量为实际的DICOM文件夹路径")
        return
    
    try:
        # 步骤1: 加载DICOM系列
        print("步骤1: 加载DICOM系列...")
        success = cropper.load_dicom_series(dicom_folder)
        if not success:
            print("加载DICOM系列失败")
            return
        
        # 显示图像信息
        info = cropper.get_image_info()
        print(f"图像形状: {info.get('shape', 'N/A')}")
        print(f"数据类型: {info.get('dtype', 'N/A')}")
        print(f"患者ID: {info.get('patient_id', 'N/A')}")
        print(f"模态: {info.get('modality', 'N/A')}")
        print()
        
        # 步骤2: 设置裁剪区域
        print("步骤2: 设置裁剪区域...")
        
        # 示例: 在图像中心设置一个200x200的区域
        if cropper.image_array is not None:
            height, width = cropper.image_array.shape[1], cropper.image_array.shape[2]
            
            # 计算中心区域
            crop_width, crop_height = 200, 200
            x = (width - crop_width) // 2
            y = (height - crop_height) // 2
            
            # 确保区域在图像范围内
            x = max(0, min(x, width - crop_width))
            y = max(0, min(y, height - crop_height))
            crop_width = min(crop_width, width - x)
            crop_height = min(crop_height, height - y)
            
            success = cropper.set_crop_region(x, y, crop_width, crop_height)
            if success:
                print(f"设置裁剪区域: ({x}, {y}, {crop_width}, {crop_height})")
            else:
                print("设置裁剪区域失败")
                return
        print()
        
        # 步骤3: 执行裁剪
        print("步骤3: 执行裁剪...")
        success = cropper.crop_all_slices()
        if not success:
            print("裁剪操作失败")
            return
        
        print(f"裁剪完成，新形状: {cropper.cropped_array.shape}")
        print()
        
        # 步骤4: 保存结果
        print("步骤4: 保存结果...")
        
        # 创建输出目录
        os.makedirs(output_folder, exist_ok=True)
        
        success = cropper.save_cropped_series(output_folder)
        if success:
            print(f"结果已保存到: {output_folder}")
        else:
            print("保存结果失败")
            return
        
        print("\n=== 处理完成 ===")
        
    except Exception as e:
        logger.error(f"处理过程中发生错误: {e}")
        print(f"错误: {e}")


def example_gui_usage():
    """GUI界面使用示例"""
    print("=== CT区域选框分割工具 - GUI界面示例 ===\n")
    
    try:
        from PyQt5.QtWidgets import QApplication
        from gui.main_window import CTRegionCropperApp
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 创建主窗口
        window = CTRegionCropperApp()
        window.show()
        
        print("GUI界面已启动")
        print("使用说明:")
        print("1. 点击'加载DICOM文件夹'按钮选择包含DICOM文件的文件夹")
        print("2. 在图像上拖拽鼠标选择裁剪区域，或使用右侧面板精确设置")
        print("3. 点击'执行裁剪'按钮进行裁剪")
        print("4. 点击'保存裁剪结果'按钮保存结果")
        
        # 运行应用程序
        sys.exit(app.exec_())
        
    except ImportError as e:
        print(f"GUI依赖缺失: {e}")
        print("请安装PyQt5: pip install PyQt5")
    except Exception as e:
        print(f"启动GUI失败: {e}")


def example_batch_processing():
    """批量处理示例"""
    print("=== CT区域选框分割工具 - 批量处理示例 ===\n")
    
    # 批量处理多个DICOM文件夹
    input_folders = [
        r"C:\path\to\dicom\folder1",
        r"C:\path\to\dicom\folder2",
        r"C:\path\to\dicom\folder3"
    ]
    
    output_base_folder = r"C:\path\to\batch\output"
    
    # 统一的裁剪区域设置
    crop_region = (100, 100, 200, 200)  # (x, y, width, height)
    
    cropper = CTRegionCropper()
    
    for i, input_folder in enumerate(input_folders):
        print(f"处理文件夹 {i+1}/{len(input_folders)}: {input_folder}")
        
        if not os.path.exists(input_folder):
            print(f"跳过不存在的文件夹: {input_folder}")
            continue
        
        try:
            # 加载DICOM系列
            success = cropper.load_dicom_series(input_folder)
            if not success:
                print(f"加载失败: {input_folder}")
                continue
            
            # 设置裁剪区域
            x, y, width, height = crop_region
            success = cropper.set_crop_region(x, y, width, height)
            if not success:
                print(f"设置裁剪区域失败: {input_folder}")
                continue
            
            # 执行裁剪
            success = cropper.crop_all_slices()
            if not success:
                print(f"裁剪失败: {input_folder}")
                continue
            
            # 保存结果
            output_folder = os.path.join(output_base_folder, f"cropped_{i+1}")
            os.makedirs(output_folder, exist_ok=True)
            
            success = cropper.save_cropped_series(output_folder)
            if success:
                print(f"保存成功: {output_folder}")
            else:
                print(f"保存失败: {output_folder}")
            
            # 重置分割器以处理下一个文件夹
            cropper.reset()
            
        except Exception as e:
            print(f"处理文件夹时发生错误 {input_folder}: {e}")
            continue
    
    print("\n=== 批量处理完成 ===")


def example_validation():
    """验证示例"""
    print("=== CT区域选框分割工具 - 验证示例 ===\n")
    
    cropper = CTRegionCropper()
    
    # 示例: 验证裁剪区域
    test_regions = [
        (0, 0, 100, 100),      # 有效区域
        (-10, 0, 100, 100),    # 无效: 负坐标
        (0, 0, 0, 100),        # 无效: 零宽度
        (400, 400, 200, 200),  # 可能无效: 超出边界（取决于图像大小）
    ]
    
    # 假设图像尺寸为512x512
    image_shape = (100, 512, 512)  # (slices, height, width)
    
    print("验证裁剪区域:")
    for i, (x, y, width, height) in enumerate(test_regions):
        is_valid, error_msg = cropper.validate_crop_region(x, y, width, height)
        status = "有效" if is_valid else f"无效: {error_msg}"
        print(f"区域 {i+1} ({x}, {y}, {width}, {height}): {status}")
    
    print()
    
    # 示例: 文件验证
    test_paths = [
        r"C:\existing\dicom\folder",
        r"C:\nonexistent\folder",
        r"C:\empty\folder"
    ]
    
    print("验证文件路径:")
    for path in test_paths:
        is_valid, error_msg = FileUtils.validate_path(path, must_exist=True, must_be_dir=True)
        status = "有效" if is_valid else f"无效: {error_msg}"
        print(f"路径 '{path}': {status}")
    
    print("\n=== 验证完成 ===")


def main():
    """主函数"""
    print("CT区域选框分割工具 - 使用示例")
    print("=" * 50)
    
    while True:
        print("\n请选择示例:")
        print("1. 编程接口使用示例")
        print("2. GUI界面使用示例")
        print("3. 批量处理示例")
        print("4. 验证示例")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == '1':
            example_programmatic_usage()
        elif choice == '2':
            example_gui_usage()
        elif choice == '3':
            example_batch_processing()
        elif choice == '4':
            example_validation()
        elif choice == '0':
            print("退出示例程序")
            break
        else:
            print("无效选择，请重新输入")


if __name__ == "__main__":
    main()
