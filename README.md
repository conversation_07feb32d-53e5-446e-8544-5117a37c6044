# 肺部CT影像分析工具集

一个完整的肺部CT影像分析工具集，包含多个功能模块，支持从图像预处理到定量分析的完整工作流程。

## 项目概述

本项目提供了一套完整的肺部CT影像分析解决方案，包括：

### 🫁 肺组织分割模块 (lung_segmentation)
- 基于AI模型的肺部分割（Lungmask、TotalSegmentator）
- 肺组织定量分析和肺气肿评估
- 三维重建和可视化
- 批量处理功能

### 🖼️ 图片转DICOM模块 (image_to_dicom)
- JPG/PNG等格式图片转DICOM
- HU值映射和调整
- DICOM元数据管理
- 批量转换功能

### 🔧 DICOM工具模块 (dicom_tools)
- DICOM文件重新编号
- DICOM几何信息修复
- DICOM调试和验证工具
- HU值调整工具

### ✂️ CT区域裁剪工具 (ct_region_tools)
- CT影像区域选框分割
- 保持完整DICOM元数据
- 多层面批量处理

### 🛠️ 共享工具模块 (shared_utils)
- 文件I/O操作
- 图像预处理
- 通用工具函数

## 快速开始

### 安装依赖
```bash
pip install -r requirements.txt
```

### 启动各功能模块

#### 1. 肺组织分割工具
```bash
python run_lung_segmentation.py
```

#### 2. 肺组织分析工具
```bash
python run_lung_tissue_analyzer.py
```

#### 3. 图片转DICOM工具
```bash
python run_image_to_dicom.py
```

#### 4. DICOM重新编号工具
```bash
python run_dicom_renumber.py
```

#### 5. CT区域裁剪工具
```bash
python run_ct_region_cropper.py
```