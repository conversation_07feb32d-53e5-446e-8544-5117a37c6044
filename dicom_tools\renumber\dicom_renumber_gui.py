#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICOM文件重新编号工具 - GUI版本
解决3D Slicer中DICOM文件排序问题

作者: AI Assistant
日期: 2025-01-17
"""

import os
import sys
import pydicom
import shutil
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QFileDialog, QTextEdit, QProgressBar, QGroupBox,
                             QCheckBox, QMessageBox, QSplitter, QFrame)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
import tempfile
from typing import List, Tuple


class RenumberWorker(QThread):
    """重新编号工作线程"""
    progress_updated = pyqtSignal(int)
    log_updated = pyqtSignal(str)
    finished = pyqtSignal(bool, str)

    def __init__(self, input_dir, output_dir, create_backup):
        super().__init__()
        self.input_dir = input_dir
        self.output_dir = output_dir
        self.create_backup = create_backup

    def run(self):
        try:
            self.renumber_dicom_files()
        except Exception as e:
            self.finished.emit(False, f"处理失败: {str(e)}")

    def get_dicom_files(self, directory: str) -> List[str]:
        """获取目录中的所有DICOM文件"""
        dicom_files = []
        directory = Path(directory)
        
        for file_path in directory.iterdir():
            if file_path.is_file():
                try:
                    pydicom.dcmread(str(file_path), stop_before_pixels=True)
                    dicom_files.append(str(file_path))
                except:
                    continue
        
        return dicom_files

    def extract_instance_info(self, dicom_files: List[str]) -> List[Tuple[str, int, float, str]]:
        """提取DICOM文件的实例信息"""
        file_info = []

        for i, file_path in enumerate(dicom_files):
            try:
                ds = pydicom.dcmread(file_path, stop_before_pixels=True)

                instance_number = getattr(ds, 'InstanceNumber', i + 1)

                # 尝试多种方式获取切片位置
                slice_location = None

                # 方法1: 直接使用SliceLocation
                if hasattr(ds, 'SliceLocation') and ds.SliceLocation is not None:
                    slice_location = float(ds.SliceLocation)

                # 方法2: 使用ImagePositionPatient的Z坐标
                elif hasattr(ds, 'ImagePositionPatient') and ds.ImagePositionPatient is not None:
                    if len(ds.ImagePositionPatient) >= 3:
                        slice_location = float(ds.ImagePositionPatient[2])

                # 方法3: 使用Instance Number作为位置
                if slice_location is None:
                    slice_location = float(instance_number)
                    self.log_updated.emit(f"警告: {os.path.basename(file_path)} 缺少位置信息，使用Instance Number")

                # 获取原始文件名用于调试
                original_filename = os.path.basename(file_path)

                file_info.append((file_path, int(instance_number), slice_location, original_filename))

                # 更新进度
                progress = int((i + 1) / len(dicom_files) * 30)  # 前30%用于分析
                self.progress_updated.emit(progress)

            except Exception as e:
                self.log_updated.emit(f"错误: 无法读取文件 {file_path}: {e}")
                continue

        return file_info

    def renumber_dicom_files(self):
        """重新编号DICOM文件"""
        
        input_path = Path(self.input_dir)
        if not input_path.exists():
            raise FileNotFoundError(f"输入目录不存在: {self.input_dir}")
        
        # 判断是否原地操作
        in_place = (self.output_dir is None or self.output_dir == self.input_dir)
        if not in_place:
            os.makedirs(self.output_dir, exist_ok=True)
        
        # 获取所有DICOM文件
        self.log_updated.emit("正在扫描DICOM文件...")
        dicom_files = self.get_dicom_files(self.input_dir)
        
        if not dicom_files:
            raise ValueError("未找到DICOM文件！")
        
        self.log_updated.emit(f"找到 {len(dicom_files)} 个DICOM文件")
        
        # 提取实例信息
        self.log_updated.emit("正在分析文件信息...")
        file_info = self.extract_instance_info(dicom_files)
        
        if not file_info:
            raise ValueError("无法读取任何DICOM文件信息！")
        
        # 按切片位置排序，如果位置相同则按Instance Number排序
        file_info.sort(key=lambda x: (x[2], x[1]))

        self.log_updated.emit(f"文件排序信息 (共{len(file_info)}个文件):")
        for i, (file_path, old_instance, slice_loc, orig_name) in enumerate(file_info[:10]):
            self.log_updated.emit(f"  {i+1:3d}. {orig_name} (原Instance: {old_instance}, 位置: {slice_loc:.2f})")
        if len(file_info) > 10:
            self.log_updated.emit(f"  ... 还有 {len(file_info)-10} 个文件")

        # 检查是否有重复的位置
        positions = [info[2] for info in file_info]
        unique_positions = set(positions)
        if len(positions) != len(unique_positions):
            self.log_updated.emit(f"警告: 发现 {len(positions) - len(unique_positions)} 个重复位置")
        
        # 创建备份
        if self.create_backup and in_place:
            backup_dir = input_path.parent / f"{input_path.name}_backup"
            if backup_dir.exists():
                shutil.rmtree(backup_dir)
            
            self.log_updated.emit(f"正在创建备份到: {backup_dir}")
            shutil.copytree(self.input_dir, backup_dir)
            self.progress_updated.emit(40)
        
        # 重新编号并保存文件
        self.log_updated.emit("正在重新编号DICOM文件...")

        self.log_updated.emit(f"重新排序映射:")
        for i in range(min(10, len(file_info))):
            old_name = file_info[i][3]  # 使用存储的原始文件名
            self.log_updated.emit(f"  位置{i+1}: {old_name} -> {i+1:04d}.dcm (原Instance: {file_info[i][1]}, 位置: {file_info[i][2]:.2f})")
        if len(file_info) > 10:
            self.log_updated.emit(f"  ... 还有 {len(file_info)-10} 个文件")

        with tempfile.TemporaryDirectory() as temp_dir:
            temp_files = []

            for i, (file_path, old_instance, slice_loc, orig_name) in enumerate(file_info):
                new_instance_number = i + 1

                try:
                    ds = pydicom.dcmread(file_path)

                    # 更新Instance Number
                    ds.InstanceNumber = new_instance_number

                    # 更新Slice Location确保连续性
                    ds.SliceLocation = float(i)

                    # 确保完整的几何信息
                    # 1. Image Position Patient (必须有完整的X,Y,Z坐标)
                    if not hasattr(ds, 'ImagePositionPatient') or ds.ImagePositionPatient is None:
                        ds.ImagePositionPatient = [0.0, 0.0, float(i)]
                    else:
                        # 确保是3个元素的列表
                        if len(ds.ImagePositionPatient) < 3:
                            ds.ImagePositionPatient = [0.0, 0.0, float(i)]
                        else:
                            ds.ImagePositionPatient[2] = float(i)

                    # 2. Image Orientation Patient (定义图像方向)
                    if not hasattr(ds, 'ImageOrientationPatient') or ds.ImageOrientationPatient is None:
                        # 标准轴向图像方向：行方向(1,0,0)，列方向(0,1,0)
                        ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]

                    # 3. Pixel Spacing (像素间距)
                    if not hasattr(ds, 'PixelSpacing') or ds.PixelSpacing is None:
                        ds.PixelSpacing = [1.0, 1.0]  # 默认1mm x 1mm

                    # 4. Slice Thickness (切片厚度)
                    if not hasattr(ds, 'SliceThickness') or ds.SliceThickness is None:
                        ds.SliceThickness = 1.0  # 默认1mm

                    # 5. Spacing Between Slices (切片间距)
                    if not hasattr(ds, 'SpacingBetweenSlices') or ds.SpacingBetweenSlices is None:
                        ds.SpacingBetweenSlices = 1.0  # 默认1mm

                    # 重要：按新的顺序重新命名文件
                    # 统一使用4位数格式化
                    new_filename = f"{new_instance_number:04d}.dcm"

                    temp_file = os.path.join(temp_dir, new_filename)
                    ds.save_as(temp_file, write_like_original=False)

                    # 记录原始文件路径和新文件名的对应关系
                    temp_files.append((temp_file, file_path, new_filename))

                    if i % 50 == 0 or i == len(file_info) - 1:
                        self.log_updated.emit(f"  处理进度: {i+1}/{len(file_info)} - {orig_name} -> {new_filename}")

                    # 更新进度 (40% - 80%)
                    progress = 40 + int((i + 1) / len(file_info) * 40)
                    self.progress_updated.emit(progress)

                except Exception as e:
                    self.log_updated.emit(f"错误: 处理文件 {file_path} 时出错: {e}")
                    continue
            
            # 将临时文件移动到最终位置
            self.log_updated.emit("正在保存文件...")

            # 如果是原地修改，先删除所有原始文件
            if in_place:
                self.log_updated.emit("正在删除原始文件...")
                for _, original_path, _ in temp_files:
                    if os.path.exists(original_path):
                        os.remove(original_path)

            # 保存新文件
            for i, (temp_file, original_path, new_filename) in enumerate(temp_files):
                if in_place:
                    # 原地修改：使用新的文件名
                    final_path = os.path.join(os.path.dirname(original_path), new_filename)
                else:
                    # 输出到新目录：使用新的文件名
                    final_path = os.path.join(self.output_dir, new_filename)

                shutil.move(temp_file, final_path)

                if i % 50 == 0 or i == len(temp_files) - 1:
                    self.log_updated.emit(f"  保存: {new_filename} ({i+1}/{len(temp_files)})")

                # 更新进度 (80% - 100%)
                progress = 80 + int((i + 1) / len(temp_files) * 20)
                self.progress_updated.emit(progress)
        
        success_msg = f"完成！处理了 {len(temp_files)} 个文件"
        if self.create_backup and in_place:
            success_msg += f"\n原始文件已备份到: {backup_dir}"
        
        self.finished.emit(True, success_msg)


class DicomRenumberGUI(QMainWindow):
    """DICOM重新编号GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.input_dir = ""
        self.output_dir = ""
        self.worker = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("DICOM文件重新编号工具")
        self.setGeometry(100, 100, 800, 600)
        
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制面板
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # 右侧日志面板
        right_panel = self.create_log_panel()
        splitter.addWidget(right_panel)
        
        splitter.setSizes([350, 450])
        
        self.statusBar().showMessage("就绪")
        
    def create_control_panel(self):
        """创建控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        
        # 目录选择组
        dir_group = QGroupBox("目录选择")
        dir_layout = QVBoxLayout(dir_group)
        
        # 输入目录
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入目录:"))
        self.input_dir_edit = QLineEdit()
        self.input_dir_edit.setPlaceholderText("选择包含DICOM文件的目录...")
        input_layout.addWidget(self.input_dir_edit)
        self.select_input_btn = QPushButton("浏览")
        self.select_input_btn.clicked.connect(self.select_input_dir)
        input_layout.addWidget(self.select_input_btn)
        dir_layout.addLayout(input_layout)
        
        # 输出目录
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("留空则原地修改...")
        output_layout.addWidget(self.output_dir_edit)
        self.select_output_btn = QPushButton("浏览")
        self.select_output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.select_output_btn)
        dir_layout.addLayout(output_layout)
        
        layout.addWidget(dir_group)
        
        # 选项组
        options_group = QGroupBox("选项")
        options_layout = QVBoxLayout(options_group)
        
        self.backup_checkbox = QCheckBox("创建备份（仅原地修改时）")
        self.backup_checkbox.setChecked(True)
        options_layout.addWidget(self.backup_checkbox)
        
        layout.addWidget(options_group)
        
        # 控制组
        control_group = QGroupBox("操作")
        control_layout = QVBoxLayout(control_group)
        
        self.process_btn = QPushButton("开始处理")
        self.process_btn.clicked.connect(self.start_processing)
        self.process_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        control_layout.addWidget(self.process_btn)
        
        self.progress_bar = QProgressBar()
        control_layout.addWidget(self.progress_bar)
        
        layout.addWidget(control_group)
        
        # 说明
        info_text = QLabel(
            "此工具解决3D Slicer中DICOM文件排序问题：\n"
            "• 按解剖位置重新排序文件\n"
            "• 重新编号Instance Number为连续数字\n"
            "• 修正Slice Location和Image Position"
        )
        info_text.setStyleSheet("color: #666; font-size: 10px; margin: 10px;")
        info_text.setWordWrap(True)
        layout.addWidget(info_text)
        
        layout.addStretch()
        return panel
        
    def create_log_panel(self):
        """创建日志面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        
        layout.addWidget(QLabel("处理日志:"))
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        clear_btn = QPushButton("清除日志")
        clear_btn.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_btn)
        
        return panel
        
    def select_input_dir(self):
        """选择输入目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择DICOM文件目录")
        if dir_path:
            self.input_dir = dir_path
            self.input_dir_edit.setText(dir_path)
            self.log(f"输入目录: {dir_path}")
            
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_dir_edit.setText(dir_path)
            self.log(f"输出目录: {dir_path}")
            
    def start_processing(self):
        """开始处理"""
        if not self.input_dir:
            QMessageBox.warning(self, "警告", "请先选择输入目录！")
            return
            
        # 获取输出目录
        output_dir = self.output_dir_edit.text().strip()
        if not output_dir:
            output_dir = self.input_dir
            
        # 禁用按钮
        self.process_btn.setEnabled(False)
        self.process_btn.setText("处理中...")
        self.progress_bar.setValue(0)
        
        # 启动工作线程
        self.worker = RenumberWorker(
            self.input_dir,
            output_dir,
            self.backup_checkbox.isChecked()
        )
        
        self.worker.progress_updated.connect(self.progress_bar.setValue)
        self.worker.log_updated.connect(self.log)
        self.worker.finished.connect(self.on_finished)
        
        self.worker.start()
        self.log("开始处理...")
        
    def on_finished(self, success, message):
        """处理完成"""
        self.process_btn.setEnabled(True)
        self.process_btn.setText("开始处理")
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.log("处理完成！")
        else:
            QMessageBox.warning(self, "错误", message)
            self.log("处理失败")
            
        self.statusBar().showMessage("处理完成")
        
    def log(self, message):
        """添加日志消息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.ensureCursorVisible()


def main():
    app = QApplication(sys.argv)
    app.setStyle('Fusion')
    
    window = DicomRenumberGUI()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
