#!/usr/bin/env python3
"""
CT区域选框分割工具测试运行脚本

运行所有测试并生成报告
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def main():
    """主函数"""
    print("CT区域选框分割工具 - 测试运行器")
    print("=" * 50)
    
    try:
        from tests.test_cropper import run_tests
        
        # 运行测试
        success = run_tests()
        
        if success:
            print("\n🎉 所有测试通过！工具功能正常。")
            return 0
        else:
            print("\n⚠️  部分测试失败，请检查代码。")
            return 1
            
    except Exception as e:
        logger.error(f"运行测试失败: {e}")
        print(f"测试运行失败: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
