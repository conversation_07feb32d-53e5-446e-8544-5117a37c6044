#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复DICOM文件几何信息工具
解决3D Slicer中"Reference image does not contain geometry information"警告

作者: AI Assistant
日期: 2025-01-17
"""

import os
import sys
import pydicom
import shutil
from pathlib import Path
import tempfile
from typing import List


def get_dicom_files(directory: str) -> List[str]:
    """获取目录中的所有DICOM文件"""
    dicom_files = []
    directory = Path(directory)
    
    for file_path in directory.iterdir():
        if file_path.is_file():
            try:
                pydicom.dcmread(str(file_path), stop_before_pixels=True)
                dicom_files.append(str(file_path))
            except:
                continue
    
    return dicom_files


def check_geometry_info(dicom_files: List[str]):
    """检查DICOM文件的几何信息完整性"""
    print("检查几何信息完整性...")
    
    missing_info = {
        'ImagePositionPatient': 0,
        'ImageOrientationPatient': 0,
        'PixelSpacing': 0,
        'SliceThickness': 0,
        'SpacingBetweenSlices': 0,
        'SliceLocation': 0
    }
    
    for file_path in dicom_files[:10]:  # 检查前10个文件
        try:
            ds = pydicom.dcmread(file_path, stop_before_pixels=True)
            filename = os.path.basename(file_path)
            
            # 检查各种几何信息
            if not hasattr(ds, 'ImagePositionPatient') or ds.ImagePositionPatient is None:
                missing_info['ImagePositionPatient'] += 1
            elif len(ds.ImagePositionPatient) < 3:
                missing_info['ImagePositionPatient'] += 1
                
            if not hasattr(ds, 'ImageOrientationPatient') or ds.ImageOrientationPatient is None:
                missing_info['ImageOrientationPatient'] += 1
            elif len(ds.ImageOrientationPatient) < 6:
                missing_info['ImageOrientationPatient'] += 1
                
            if not hasattr(ds, 'PixelSpacing') or ds.PixelSpacing is None:
                missing_info['PixelSpacing'] += 1
                
            if not hasattr(ds, 'SliceThickness') or ds.SliceThickness is None:
                missing_info['SliceThickness'] += 1
                
            if not hasattr(ds, 'SpacingBetweenSlices') or ds.SpacingBetweenSlices is None:
                missing_info['SpacingBetweenSlices'] += 1
                
            if not hasattr(ds, 'SliceLocation') or ds.SliceLocation is None:
                missing_info['SliceLocation'] += 1
                
        except Exception as e:
            print(f"无法读取文件 {file_path}: {e}")
    
    print("几何信息缺失统计（前10个文件）:")
    for key, count in missing_info.items():
        if count > 0:
            print(f"  {key}: {count}/10 个文件缺失")
        else:
            print(f"  {key}: ✓ 完整")
    
    return missing_info


def fix_dicom_geometry(input_dir: str):
    """修复DICOM文件的几何信息"""
    
    print(f"正在处理目录: {input_dir}")
    
    # 获取所有DICOM文件
    dicom_files = get_dicom_files(input_dir)
    if not dicom_files:
        print("未找到DICOM文件！")
        return
    
    print(f"找到 {len(dicom_files)} 个DICOM文件")
    
    # 检查几何信息
    missing_info = check_geometry_info(dicom_files)
    
    # 如果所有信息都完整，则不需要修复
    if all(count == 0 for count in missing_info.values()):
        print("✅ 所有几何信息都完整，无需修复！")
        return
    
    # 创建备份
    backup_dir = Path(input_dir).parent / f"{Path(input_dir).name}_backup_geometry"
    if backup_dir.exists():
        print(f"删除旧备份: {backup_dir}")
        shutil.rmtree(backup_dir)
    
    print(f"创建备份到: {backup_dir}")
    shutil.copytree(input_dir, backup_dir)
    
    # 按文件名排序（假设文件名反映正确顺序）
    dicom_files.sort(key=lambda x: os.path.basename(x))
    
    # 使用临时目录处理文件
    with tempfile.TemporaryDirectory() as temp_dir:
        print("正在修复几何信息...")
        
        temp_files = []
        for i, file_path in enumerate(dicom_files):
            try:
                # 读取DICOM文件
                ds = pydicom.dcmread(file_path)
                
                # 修复几何信息
                # 1. Image Position Patient (图像位置)
                if not hasattr(ds, 'ImagePositionPatient') or ds.ImagePositionPatient is None or len(ds.ImagePositionPatient) < 3:
                    ds.ImagePositionPatient = [0.0, 0.0, float(i)]
                    print(f"  修复 {os.path.basename(file_path)}: 添加ImagePositionPatient")
                else:
                    # 只更新Z坐标，保持X,Y不变
                    ds.ImagePositionPatient[2] = float(i)
                
                # 2. Image Orientation Patient (图像方向)
                if not hasattr(ds, 'ImageOrientationPatient') or ds.ImageOrientationPatient is None or len(ds.ImageOrientationPatient) < 6:
                    # 标准轴向图像方向：行方向(1,0,0)，列方向(0,1,0)
                    ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
                    print(f"  修复 {os.path.basename(file_path)}: 添加ImageOrientationPatient")
                
                # 3. Pixel Spacing (像素间距)
                if not hasattr(ds, 'PixelSpacing') or ds.PixelSpacing is None:
                    ds.PixelSpacing = [1.0, 1.0]  # 默认1mm x 1mm
                    print(f"  修复 {os.path.basename(file_path)}: 添加PixelSpacing")
                
                # 4. Slice Thickness (切片厚度)
                if not hasattr(ds, 'SliceThickness') or ds.SliceThickness is None:
                    ds.SliceThickness = 1.0  # 默认1mm
                    print(f"  修复 {os.path.basename(file_path)}: 添加SliceThickness")
                
                # 5. Spacing Between Slices (切片间距)
                if not hasattr(ds, 'SpacingBetweenSlices') or ds.SpacingBetweenSlices is None:
                    ds.SpacingBetweenSlices = 1.0  # 默认1mm
                    print(f"  修复 {os.path.basename(file_path)}: 添加SpacingBetweenSlices")
                
                # 6. Slice Location (切片位置)
                if not hasattr(ds, 'SliceLocation') or ds.SliceLocation is None:
                    ds.SliceLocation = float(i)
                    print(f"  修复 {os.path.basename(file_path)}: 添加SliceLocation")
                
                # 保存到临时文件
                filename = os.path.basename(file_path)
                temp_file = os.path.join(temp_dir, filename)
                ds.save_as(temp_file, write_like_original=False)
                temp_files.append((temp_file, file_path))
                
                if (i + 1) % 50 == 0 or i == len(dicom_files) - 1:
                    print(f"  处理进度: {i+1}/{len(dicom_files)}")
                
            except Exception as e:
                print(f"错误: 处理文件 {file_path} 时出错: {e}")
                continue
        
        # 保存修复后的文件
        print("正在保存修复后的文件...")
        for temp_file, original_path in temp_files:
            shutil.move(temp_file, original_path)
    
    print(f"\n✅ 几何信息修复完成！")
    print(f"处理了 {len(temp_files)} 个文件")
    print(f"原始文件已备份到: {backup_dir}")
    
    # 验证修复结果
    print("\n验证修复结果...")
    fixed_missing_info = check_geometry_info(dicom_files)
    
    improvements = []
    for key in missing_info:
        if missing_info[key] > fixed_missing_info[key]:
            improvements.append(f"{key}: {missing_info[key]} -> {fixed_missing_info[key]}")
    
    if improvements:
        print("修复改进:")
        for improvement in improvements:
            print(f"  {improvement}")
    
    if all(count == 0 for count in fixed_missing_info.values()):
        print("✅ 所有几何信息现在都完整了！")
        print("现在可以重新导入3D Slicer，应该不会再出现几何信息警告。")
    else:
        print("⚠️ 仍有部分几何信息缺失，可能需要进一步检查。")


def main():
    if len(sys.argv) != 2:
        print("用法: python fix_dicom_geometry.py <DICOM目录路径>")
        print("示例: python fix_dicom_geometry.py \"D:\\path\\to\\dicom\\folder\"")
        sys.exit(1)
    
    input_dir = sys.argv[1]
    
    if not os.path.exists(input_dir):
        print(f"错误: 目录不存在: {input_dir}")
        sys.exit(1)
    
    if not os.path.isdir(input_dir):
        print(f"错误: 不是目录: {input_dir}")
        sys.exit(1)
    
    print("=" * 60)
    print("DICOM几何信息修复工具")
    print("=" * 60)
    print("此工具将修复以下几何信息:")
    print("• ImagePositionPatient (图像位置)")
    print("• ImageOrientationPatient (图像方向)")
    print("• PixelSpacing (像素间距)")
    print("• SliceThickness (切片厚度)")
    print("• SpacingBetweenSlices (切片间距)")
    print("• SliceLocation (切片位置)")
    print("=" * 60)
    
    confirm = input(f"确认要修复目录 '{input_dir}' 的几何信息吗？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作取消")
        sys.exit(0)
    
    try:
        fix_dicom_geometry(input_dir)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
