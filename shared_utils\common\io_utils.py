import numpy as np
import SimpleITK as sitk
from typing import Tuple, Dict, Any, List, Optional, Union
import logging
import os
from pathlib import Path
import re
import unicodedata

def sanitize_filename(filename: str) -> str:
    """
    清理文件名，移除或替换可能导致问题的字符

    Args:
        filename: 原始文件名

    Returns:
        清理后的安全文件名
    """
    # 规范化Unicode字符
    filename = unicodedata.normalize('NFKD', filename)

    # 移除或替换特殊字符
    filename = re.sub(r'[<>:"/\\|?*]', '_', filename)

    # 替换空格为下划线
    filename = filename.replace(' ', '_')

    # 移除连续的下划线
    filename = re.sub(r'_+', '_', filename)

    # 移除开头和结尾的下划线和点
    filename = filename.strip('_.')

    # 确保文件名不为空
    if not filename:
        filename = 'output'

    return filename

def create_safe_filepath(directory: str, filename: str) -> str:
    """
    创建安全的文件路径

    Args:
        directory: 目录路径
        filename: 文件名

    Returns:
        安全的完整文件路径
    """
    safe_filename = sanitize_filename(filename)
    return os.path.join(directory, safe_filename)

def load_nifti(filepath: str) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    加载NIFTI图像文件
    
    Args:
        filepath: NIFTI文件路径
        
    Returns:
        图像数组和元数据字典
    """
    try:
        sitk_image = sitk.ReadImage(filepath)
        image_array = sitk.GetArrayFromImage(sitk_image)
        
        # 提取元数据
        metadata = {
            'spacing': sitk_image.GetSpacing(),
            'origin': sitk_image.GetOrigin(),
            'direction': sitk_image.GetDirection(),
            'size': sitk_image.GetSize()
        }
        
        return image_array, metadata
        
    except Exception as e:
        logging.error(f"加载NIFTI文件失败 {filepath}: {e}")
        raise

def get_dicom_series_info(directory: str) -> Dict[str, Dict[str, Any]]:
    """
    获取DICOM目录中所有序列的信息

    Args:
        directory: 包含DICOM文件的目录路径

    Returns:
        序列信息字典，键为序列ID，值为序列详细信息
    """
    try:
        if not os.path.isdir(directory):
            raise ValueError(f"DICOM目录不存在: {directory}")

        # 使用SimpleITK获取所有序列ID
        reader = sitk.ImageSeriesReader()
        series_IDs = sitk.ImageSeriesReader.GetGDCMSeriesIDs(directory)

        if not series_IDs:
            raise ValueError(f"在目录 {directory} 中未找到有效的DICOM系列")

        series_info = {}

        for series_id in series_IDs:
            try:
                # 获取该序列的文件名
                dicom_names = reader.GetGDCMSeriesFileNames(directory, series_id)

                if not dicom_names:
                    continue

                # 读取第一个文件获取元数据
                import pydicom
                first_file = dicom_names[0]
                ds = pydicom.dcmread(first_file)

                # 提取序列信息
                series_info[series_id] = {
                    'file_count': len(dicom_names),
                    'series_description': getattr(ds, 'SeriesDescription', ''),
                    'modality': getattr(ds, 'Modality', ''),
                    'series_number': getattr(ds, 'SeriesNumber', ''),
                    'study_description': getattr(ds, 'StudyDescription', ''),
                    'patient_name': getattr(ds, 'PatientName', ''),
                    'patient_id': getattr(ds, 'PatientID', ''),
                    'file_names': dicom_names
                }

            except Exception as e:
                logging.warning(f"读取序列 {series_id} 信息失败: {e}")
                continue

        return series_info

    except Exception as e:
        logging.error(f"获取DICOM序列信息失败 {directory}: {e}")
        raise


def load_dicom_series(directory: str, series_id: str = None) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    加载DICOM系列图像

    Args:
        directory: 包含DICOM文件的目录路径
        series_id: 指定要加载的序列ID，如果为None则使用第一个序列

    Returns:
        图像数组和元数据字典
    """
    try:
        # 检查目录是否存在
        if not os.path.isdir(directory):
            raise ValueError(f"DICOM目录不存在: {directory}")

        # 尝试查找DICOM系列
        reader = sitk.ImageSeriesReader()
        series_IDs = sitk.ImageSeriesReader.GetGDCMSeriesIDs(directory)

        if not series_IDs:
            raise ValueError(f"在目录 {directory} 中未找到有效的DICOM系列")

        # 选择要使用的序列ID
        if series_id is not None:
            if series_id not in series_IDs:
                raise ValueError(f"指定的序列ID {series_id} 不存在")
            series_ID = series_id
        else:
            # 如果没有指定序列ID，使用第一个
            series_ID = series_IDs[0]
            if len(series_IDs) > 1:
                logging.warning(f"在目录中发现多个DICOM系列，将使用第一个系列 (共{len(series_IDs)}个)")

        # 获取该系列的文件名
        dicom_names = reader.GetGDCMSeriesFileNames(directory, series_ID)
        
        if not dicom_names:
            raise ValueError(f"无法获取DICOM系列 {series_ID} 的文件名")
            
        logging.info(f"找到 {len(dicom_names)} 个DICOM文件")
        
        # 设置读取器并执行
        reader.SetFileNames(dicom_names)
        try:
            sitk_image = reader.Execute()
        except Exception as e:
            logging.error(f"读取DICOM文件时出错: {e}")
            # 尝试逐个加载文件，跳过有问题的文件
            valid_files = []
            for file in dicom_names:
                try:
                    sitk.ReadImage(file)
                    valid_files.append(file)
                except:
                    logging.warning(f"跳过无效的DICOM文件: {file}")
            
            if not valid_files:
                raise ValueError("所有DICOM文件都无法读取")
                
            reader.SetFileNames(valid_files)
            sitk_image = reader.Execute()
            
        # 获取图像数组
        image_array = sitk.GetArrayFromImage(sitk_image)
        
        # 提取元数据
        metadata = {
            'spacing': sitk_image.GetSpacing(),
            'origin': sitk_image.GetOrigin(),
            'direction': sitk_image.GetDirection(),
            'size': sitk_image.GetSize()
        }
        
        # 尝试提取一些DICOM特有的元数据
        try:
            # 从第一个文件中读取元数据
            first_file = dicom_names[0]
            reader = sitk.ImageFileReader()
            reader.SetFileName(first_file)
            reader.LoadPrivateTagsOn()
            reader.ReadImageInformation()
            
            # 提取常见的DICOM标签
            tags = {
                'patient_id': '0010|0020',
                'patient_name': '0010|0010',
                'study_date': '0008|0020',
                'modality': '0008|0060',
                'study_description': '0008|1030',
                'series_description': '0008|103E'
            }
            
            for key, tag in tags.items():
                try:
                    if reader.HasMetaDataKey(tag):
                        metadata[key] = reader.GetMetaData(tag)
                except:
                    pass
                    
        except Exception as e:
            logging.warning(f"提取DICOM元数据时出错: {e}")
        
        logging.info(f"成功加载DICOM系列: {len(dicom_names)}个文件, 形状={image_array.shape}")
        return image_array, metadata
        
    except Exception as e:
        logging.error(f"加载DICOM系列失败 {directory}: {e}")
        raise

def load_image(path: str, series_id: str = None) -> Tuple[np.ndarray, Dict[str, Any]]:
    """
    根据路径自动加载图像，支持NIFTI文件和DICOM目录

    Args:
        path: 图像文件路径或DICOM目录路径
        series_id: 指定要加载的DICOM序列ID，仅对DICOM目录有效

    Returns:
        图像数组和元数据字典
    """
    path_obj = Path(path)

    # 检查是否为目录，如果是则尝试作为DICOM系列加载
    if path_obj.is_dir():
        return load_dicom_series(str(path_obj), series_id)

    # 检查文件扩展名
    suffix = path_obj.suffix.lower()
    if suffix in ['.nii', '.gz'] or path_obj.name.endswith('.nii.gz'):
        return load_nifti(str(path_obj))
    elif suffix in ['.dcm', '.dicom']:
        # 单个DICOM文件，尝试查找同系列的其他文件
        parent_dir = path_obj.parent
        return load_dicom_series(str(parent_dir), series_id)
    else:
        # 尝试使用SimpleITK自动识别格式
        try:
            sitk_image = sitk.ReadImage(str(path_obj))
            image_array = sitk.GetArrayFromImage(sitk_image)

            # 提取元数据
            metadata = {
                'spacing': sitk_image.GetSpacing(),
                'origin': sitk_image.GetOrigin(),
                'direction': sitk_image.GetDirection(),
                'size': sitk_image.GetSize()
            }

            return image_array, metadata
        except Exception as e:
            logging.error(f"无法识别的图像格式 {path}: {e}")
            raise ValueError(f"不支持的图像格式: {suffix}")

def save_nifti(image_array: np.ndarray, filepath: str,
               metadata: Dict[str, Any] = None):
    """
    保存图像为NIFTI格式

    Args:
        image_array: 图像数组
        filepath: 输出文件路径
        metadata: 图像元数据
    """
    import tempfile
    import shutil

    try:
        # 确保数据类型为uint8，而不是布尔型
        if image_array.dtype == bool:
            image_array = image_array.astype(np.uint8)

        # 验证图像数据
        if image_array.size == 0:
            raise ValueError("图像数组为空")

        # 确保图像数据是连续的
        if not image_array.flags['C_CONTIGUOUS']:
            image_array = np.ascontiguousarray(image_array)

        sitk_image = sitk.GetImageFromArray(image_array)

        if metadata:
            if 'spacing' in metadata:
                try:
                    sitk_image.SetSpacing(metadata['spacing'])
                except Exception as e:
                    logging.warning(f"设置spacing失败: {e}")
            if 'origin' in metadata:
                try:
                    sitk_image.SetOrigin(metadata['origin'])
                except Exception as e:
                    logging.warning(f"设置origin失败: {e}")
            if 'direction' in metadata:
                try:
                    sitk_image.SetDirection(metadata['direction'])
                except Exception as e:
                    logging.warning(f"设置direction失败: {e}")

        # 处理中文路径问题：使用临时文件
        filepath = str(filepath)  # 确保是字符串

        # 检查路径是否包含非ASCII字符
        try:
            filepath.encode('ascii')
            # 路径只包含ASCII字符，直接保存
            sitk.WriteImage(sitk_image, filepath)
        except UnicodeEncodeError:
            # 路径包含非ASCII字符，使用临时文件方法
            logging.info(f"检测到非ASCII字符路径，使用临时文件方法: {filepath}")

            # 确保目标目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)

            # 创建临时文件
            with tempfile.NamedTemporaryFile(suffix='.nii.gz', delete=False) as temp_file:
                temp_path = temp_file.name

            try:
                # 保存到临时文件
                sitk.WriteImage(sitk_image, temp_path)

                # 移动到目标位置
                shutil.move(temp_path, filepath)

            except Exception as e:
                # 清理临时文件
                if os.path.exists(temp_path):
                    os.unlink(temp_path)
                raise e

        logging.info(f"成功保存NIFTI文件: {filepath}")

    except Exception as e:
        logging.error(f"保存NIFTI文件失败 {filepath}: {e}")
        logging.error(f"图像形状: {image_array.shape}, 数据类型: {image_array.dtype}")
        if metadata:
            logging.error(f"元数据: {metadata}")
        raise

def save_dicom_series(image_array: np.ndarray, directory: str, 
                     metadata: Dict[str, Any] = None,
                     base_metadata: Optional[Dict[str, str]] = None):
    """
    保存图像为DICOM系列
    
    Args:
        image_array: 图像数组
        directory: 输出目录路径
        metadata: 图像元数据
        base_metadata: 基础DICOM元数据，如PatientID等
    """
    try:
        # 创建输出目录
        os.makedirs(directory, exist_ok=True)
        
        # 确保数据类型适合DICOM
        if np.issubdtype(image_array.dtype, np.bool_):
            # 布尔掩码转换为uint8
            image_array = image_array.astype(np.uint8)
        elif image_array.dtype == np.uint8:
            # 已经是uint8，保持不变
            pass
        elif np.max(image_array) <= 255 and np.min(image_array) >= 0:
            # 值范围适合uint8
            image_array = image_array.astype(np.uint8)
        elif np.issubdtype(image_array.dtype, np.integer):
            # 整型数据，转换为int16
            image_array = image_array.astype(np.int16)
        else:
            # 浮点数据，缩放到合适的范围并转换为int16
            if np.min(image_array) < -32768 or np.max(image_array) > 32767:
                logging.warning("DICOM保存: 图像值超出int16范围，将进行缩放")
                # 缩放到-32000到32000范围
                min_val = np.min(image_array)
                max_val = np.max(image_array)
                scale = 64000 / (max_val - min_val) if max_val > min_val else 1
                image_array = ((image_array - min_val) * scale - 32000).astype(np.int16)
            else:
                image_array = image_array.astype(np.int16)
        
        # 创建SimpleITK图像
        sitk_image = sitk.GetImageFromArray(image_array)
        
        # 设置元数据
        if metadata:
            if 'spacing' in metadata:
                sitk_image.SetSpacing(metadata['spacing'])
            if 'origin' in metadata:
                sitk_image.SetOrigin(metadata['origin'])
            if 'direction' in metadata:
                sitk_image.SetDirection(metadata['direction'])
        
        # 设置基础DICOM元数据
        writer = sitk.ImageFileWriter()
        writer.KeepOriginalImageUIDOn()
        
        # 生成稳定的UID
        import hashlib
        import time
        
        # 使用当前时间和图像形状生成一个唯一的种子
        seed = f"{time.time()}_{image_array.shape}"
        hash_obj = hashlib.md5(seed.encode())
        hash_hex = hash_obj.hexdigest()
        
        # 使用哈希值生成UID
        study_uid = f"1.2.826.0.1.3680043.2.1125.{hash_hex[:8]}"
        series_uid = f"1.2.826.0.1.3680043.2.1125.{hash_hex[8:16]}"
        
        # 设置默认DICOM元数据
        default_metadata = {
            "0008|0060": "CT",  # Modality
            "0010|0020": "LUNG_CT_SEG",  # PatientID
            "0010|0010": "ANONYMOUS",  # PatientName
            "0020|000D": study_uid,  # StudyInstanceUID
            "0020|000E": series_uid,  # SeriesInstanceUID
            "0008|0064": "WSD",  # Conversion Type
            "0008|0008": "DERIVED\\SECONDARY",  # Image Type
            "0028|0100": "16",  # Bits Allocated
            "0028|0101": "16",  # Bits Stored
            "0028|0102": "15",  # High Bit
            "0028|0103": "1",   # Pixel Representation (1=signed)
        }
        
        # 使用提供的元数据覆盖默认值
        if base_metadata:
            default_metadata.update(base_metadata)
        
        # 应用元数据到图像
        for key, value in default_metadata.items():
            sitk_image.SetMetaData(key, value)
        
        # 保存每个切片为单独的DICOM文件
        num_slices = image_array.shape[0]
        logging.info(f"开始保存 {num_slices} 个DICOM切片到 {directory}")
        
        # 计算合适的切片厚度
        slice_thickness = metadata.get('spacing', (1, 1, 1))[2] if metadata else 1
        
        for i in range(num_slices):
            # 提取单个切片
            slice_idx = i
            
            # 设置实例UID和切片位置
            instance_uid = f"1.2.826.0.1.3680043.2.1125.{hash_hex[16:24]}.{i+1}"
            sitk_image.SetMetaData("0008|0018", instance_uid)  # SOPInstanceUID
            sitk_image.SetMetaData("0020|0013", str(i+1))  # InstanceNumber
            sitk_image.SetMetaData("0020|0032", f"0\\0\\{i*slice_thickness}")  # ImagePositionPatient
            sitk_image.SetMetaData("0020|1041", str(i*slice_thickness))  # SliceLocation
            
            # 保存DICOM文件
            output_file = os.path.join(directory, f"slice_{i+1:04d}.dcm")
            writer.SetFileName(output_file)
            
            try:
                # 提取单个切片并保存
                if image_array.ndim == 3:
                    slice_image = sitk.GetImageFromArray(image_array[i:i+1])
                    # 复制元数据
                    for key in sitk_image.GetMetaDataKeys():
                        slice_image.SetMetaData(key, sitk_image.GetMetaData(key))
                    # 设置尺寸信息
                    slice_image.SetSpacing(sitk_image.GetSpacing())
                    slice_image.SetOrigin(sitk_image.GetOrigin())
                    slice_image.SetDirection(sitk_image.GetDirection())
                    writer.Execute(slice_image)
                else:
                    # 使用原始方法
                    writer.Execute(sitk_image[:, :, i:i+1])
            except Exception as e:
                logging.error(f"保存切片 {i+1} 时出错: {e}")
                # 继续保存其他切片
        
        logging.info(f"成功保存DICOM系列到目录: {directory}，共 {num_slices} 个切片")
        
    except Exception as e:
        logging.error(f"保存DICOM系列失败 {directory}: {e}")
        raise

def save_image(image_array: np.ndarray, output_path: str, 
              metadata: Dict[str, Any] = None, 
              format_type: str = "nifti"):
    """
    根据指定格式保存图像
    
    Args:
        image_array: 图像数组
        output_path: 输出路径（文件或目录）
        metadata: 图像元数据
        format_type: 输出格式，可选 "nifti" 或 "dicom"
    """
    if format_type.lower() == "nifti":
        # 确保路径有.nii.gz扩展名
        if not output_path.lower().endswith(('.nii', '.nii.gz')):
            output_path = output_path + '.nii.gz'
        save_nifti(image_array, output_path, metadata)
    elif format_type.lower() == "dicom":
        # 如果是文件路径，转换为目录路径
        if os.path.splitext(output_path)[1]:
            directory = os.path.splitext(output_path)[0]
        else:
            directory = output_path
        save_dicom_series(image_array, directory, metadata)
    else:
        raise ValueError(f"不支持的输出格式: {format_type}，支持的格式有: nifti, dicom")

def validate_ct_image(image_array: np.ndarray) -> bool:
    """
    验证CT图像的有效性
    
    Args:
        image_array: 图像数组
        
    Returns:
        是否为有效的CT图像
    """
    # 检查维度
    if len(image_array.shape) != 3:
        return False
    
    # 检查Hounsfield单位范围
    if image_array.min() < -2000 or image_array.max() > 4000:
        logging.warning("图像强度值超出典型CT范围")
    
    # 检查图像大小
    if any(dim < 50 for dim in image_array.shape):
        logging.warning("图像尺寸可能过小")
    
    return True