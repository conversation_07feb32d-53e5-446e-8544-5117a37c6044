# CT区域选框分割工具使用说明

## 概述

CT区域选框分割工具是一个专门用于CT影像区域选框分割的应用程序。它允许用户在CT影像的某一层面划定选框，然后自动在所有层面的相同位置进行区域分割，分割后的文件保持原有的DICOM格式和元数据信息。

## 主要特性

- ✅ **DICOM格式支持**: 完全支持DICOM格式的输入和输出
- ✅ **元数据保持**: 保持原有的UID、层厚、像素间距等重要信息
- ✅ **交互式选择**: 支持鼠标拖拽和精确数值设置两种区域选择方式
- ✅ **多层面处理**: 自动对所有层面执行相同的裁剪操作
- ✅ **实时预览**: 实时显示裁剪区域和结果预览
- ✅ **窗宽窗位调整**: 支持自动和手动窗宽窗位调整
- ✅ **批量处理**: 支持编程接口进行批量处理

## 安装要求

### 系统要求
- Windows 10/11 或 Linux
- Python 3.7 或更高版本
- 至少 4GB 内存（推荐 8GB 或更多）

### 依赖包
```bash
pip install -r requirements.txt
```

主要依赖包：
- PyQt5 >= 5.15.0
- pydicom >= 2.3.0
- SimpleITK >= 2.2.0
- numpy >= 1.21.0
- matplotlib >= 3.5.0
- scipy >= 1.7.0

## 快速开始

### 1. 启动图形界面

```bash
cd ct_region_cropper
python run_gui.py
```

### 2. 基本操作流程

1. **加载DICOM文件**
   - 点击"加载DICOM文件夹"按钮
   - 选择包含DICOM文件的文件夹
   - 等待加载完成

2. **选择裁剪区域**
   - 方法一：在图像上拖拽鼠标选择区域
   - 方法二：在右侧面板中输入精确的坐标和尺寸
   - 可使用预设按钮快速设置常用区域

3. **执行裁剪**
   - 点击"执行裁剪"按钮
   - 等待处理完成

4. **保存结果**
   - 点击"保存裁剪结果"按钮
   - 选择输出文件夹
   - 等待保存完成

## 详细功能说明

### 图像显示区域

- **切片导航**: 使用滑块或数值框切换不同切片
- **窗宽窗位**: 调整图像显示的对比度和亮度
- **自动窗宽窗位**: 自动计算最佳显示参数
- **十字线**: 显示图像中心参考线
- **裁剪覆盖层**: 实时显示选定的裁剪区域

### 区域选择面板

- **坐标设置**: 精确设置裁剪区域的左上角坐标
- **尺寸设置**: 设置裁剪区域的宽度和高度
- **约束选项**: 
  - 保持宽高比：确保裁剪区域为正方形
  - 居中区域：自动将区域居中放置
- **预设区域**: 快速设置1/4、1/2或3/4大小的居中区域

### 文件操作

- **加载**: 支持包含多个DICOM文件的文件夹
- **保存**: 保存为标准DICOM格式，保持原有元数据
- **重置**: 清除所有数据，重新开始

## 编程接口使用

### 基本示例

```python
from core.region_cropper import CTRegionCropper

# 创建分割器
cropper = CTRegionCropper()

# 加载DICOM系列
cropper.load_dicom_series("path/to/dicom/folder")

# 设置裁剪区域 (x, y, width, height)
cropper.set_crop_region(100, 100, 200, 200)

# 执行裁剪
cropper.crop_all_slices()

# 保存结果
cropper.save_cropped_series("path/to/output/folder")
```

### 批量处理示例

```python
import os
from core.region_cropper import CTRegionCropper

cropper = CTRegionCropper()
input_folders = ["folder1", "folder2", "folder3"]
crop_region = (100, 100, 200, 200)

for i, folder in enumerate(input_folders):
    cropper.load_dicom_series(folder)
    cropper.set_crop_region(*crop_region)
    cropper.crop_all_slices()
    cropper.save_cropped_series(f"output_{i}")
    cropper.reset()
```

## 注意事项

### DICOM文件要求
- 文件必须是标准的DICOM格式
- 建议文件包含完整的几何信息（位置、方向等）
- 支持单系列和多系列DICOM文件

### 裁剪区域限制
- 裁剪区域不能超出图像边界
- 最小裁剪尺寸为5x5像素
- 坐标必须为非负整数

### 性能考虑
- 大型DICOM系列（>1000切片）可能需要较长处理时间
- 建议在处理大文件时关闭其他占用内存的程序
- 输出文件大小取决于裁剪区域大小

## 故障排除

### 常见问题

1. **加载DICOM文件失败**
   - 检查文件夹是否包含有效的DICOM文件
   - 确认文件没有损坏
   - 检查文件权限

2. **裁剪区域设置失败**
   - 确认区域在图像边界内
   - 检查坐标和尺寸是否为正数
   - 验证图像是否已正确加载

3. **保存结果失败**
   - 检查输出文件夹权限
   - 确认磁盘空间充足
   - 验证输出路径有效

4. **程序运行缓慢**
   - 检查系统内存使用情况
   - 尝试处理较小的DICOM系列
   - 关闭不必要的程序

### 日志文件

程序运行时会生成日志文件 `ct_region_cropper.log`，包含详细的操作记录和错误信息，有助于问题诊断。

## 技术支持

如果遇到问题或需要技术支持，请：

1. 查看日志文件中的错误信息
2. 运行测试脚本验证安装：`python run_tests.py`
3. 检查依赖包是否正确安装
4. 确认DICOM文件格式正确

## 版本信息

- 当前版本：v1.0.0
- 发布日期：2025年7月
- 兼容性：Python 3.7+, Windows/Linux

## 许可证

本工具仅供学习和研究使用，请遵守相关法律法规和医疗数据处理规范。
