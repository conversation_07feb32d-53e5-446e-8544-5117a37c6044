import os
import sys
import pydicom
from PIL import Image
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QFileDialog, QTextEdit, QProgressBar, QGroupBox,
                             QGridLayout, QMessageBox, QListWidget, QSplitter,
                             QFrame, QCheckBox, QSpinBox, QRadioButton, QButtonGroup)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont, QPixmap
import datetime
import re
import numpy as np
import uuid
import time


class ConversionWorker(QThread):
    """转换工作线程"""
    progress_updated = pyqtSignal(int)
    log_updated = pyqtSignal(str)
    conversion_finished = pyqtSignal(bool, str)

    def __init__(self, input_files, output_dir, patient_name, study_description, multi_frame_mode=False):
        super().__init__()
        self.input_files = input_files
        self.output_dir = output_dir
        self.patient_name = patient_name
        self.study_description = study_description
        self.multi_frame_mode = multi_frame_mode
        
    def run(self):
        """执行转换任务"""
        try:
            if self.multi_frame_mode:
                self._convert_multi_frame()
            else:
                self._convert_single_frame()
        except Exception as e:
            self.conversion_finished.emit(False, f"转换过程出错: {str(e)}")

    def _convert_single_frame(self):
        """单帧模式：每个JPG文件转换为一个DICOM文件"""
        total_files = len(self.input_files)
        success_count = 0

        # 首先对文件进行排序，确保正确的顺序
        sorted_files = self._sort_files_by_name(self.input_files)

        for i, input_file in enumerate(sorted_files):
            try:
                # 验证输入文件
                if not os.path.exists(input_file):
                    raise FileNotFoundError(f"输入文件不存在: {input_file}")

                # 解析文件名获取序列和图片信息
                filename = os.path.basename(input_file)
                name_without_ext = os.path.splitext(filename)[0]

                # 改进的文件名解析逻辑
                series_num, instance_num = self._parse_filename(name_without_ext, i)

                # 生成输出文件名
                output_filename = f"{name_without_ext}.dcm"
                output_filepath = os.path.join(self.output_dir, output_filename)

                # 检查输出目录是否可写
                if not os.access(self.output_dir, os.W_OK):
                    raise PermissionError(f"无法写入输出目录: {self.output_dir}")

                self.log_updated.emit(f"正在转换: {filename}")

                # 执行转换，传递排序后的索引作为切片位置
                self.jpg_to_dicom_single(input_file, output_filepath,
                                self.patient_name, self.study_description,
                                series_num, instance_num, slice_position=i)

                success_count += 1
                self.log_updated.emit(f"✓ 转换成功: {filename} -> {output_filename}")

            except Exception as e:
                error_msg = f"✗ 转换失败: {filename} - {str(e)}"
                self.log_updated.emit(error_msg)
                # 记录详细错误信息用于调试
                import traceback
                self.log_updated.emit(f"  详细错误: {traceback.format_exc().splitlines()[-1]}")

            # 更新进度
            progress = int((i + 1) / total_files * 100)
            self.progress_updated.emit(progress)

        # 转换完成
        if success_count == total_files:
            self.conversion_finished.emit(True, f"所有 {total_files} 个文件转换成功！")
        else:
            self.conversion_finished.emit(False,
                f"转换完成：成功 {success_count}/{total_files} 个文件")

    def _sort_files_by_name(self, file_list):
        """按文件名数字顺序排序文件"""
        def extract_number(filepath):
            filename = os.path.basename(filepath)
            name_without_ext = os.path.splitext(filename)[0]
            # 尝试提取文件名中的数字
            if name_without_ext.isdigit():
                return int(name_without_ext)
            else:
                # 如果不是纯数字，尝试提取数字部分
                import re
                numbers = re.findall(r'\d+', name_without_ext)
                return int(numbers[0]) if numbers else 0

        return sorted(file_list, key=extract_number)

    def _parse_filename(self, name_without_ext, default_instance):
        """解析文件名获取序列号和实例号"""
        # 改进的解析策略：优先保证序列的连续性

        if len(name_without_ext) == 8 and name_without_ext.isdigit():
            # 对于8位数字文件名，检查文件数量来决定解析策略

            if len(self.input_files) > 50:
                # 大量文件，很可能是同一序列的连续切片
                # 使用固定序列号，实例号使用排序后的索引
                series_num = 1
                instance_num = default_instance + 1  # 使用排序后的索引
            else:
                # 少量文件，但仍然优先保证连续性
                # 检查文件名模式
                file_numbers = []
                for file_path in self.input_files:
                    fname = os.path.splitext(os.path.basename(file_path))[0]
                    if len(fname) == 8 and fname.isdigit():
                        file_numbers.append(int(fname))

                # 如果文件号连续或接近连续，视为同一序列
                if file_numbers:
                    file_numbers.sort()
                    max_gap = max([file_numbers[i+1] - file_numbers[i] for i in range(len(file_numbers)-1)] if len(file_numbers) > 1 else [1])

                    if max_gap <= 10:  # 文件号间隔不超过10，视为同一序列
                        series_num = 1
                        instance_num = default_instance + 1
                    else:
                        # 使用原来的解析方式
                        series_num = int(name_without_ext[3])  # 第4位数字作为序列号
                        instance_num = default_instance + 1  # 但实例号仍使用连续编号
                else:
                    series_num = 1
                    instance_num = default_instance + 1
        else:
            # 非标准命名，使用默认值
            series_num = 1
            instance_num = default_instance + 1

        return series_num, instance_num

    def _convert_multi_frame(self):
        """多帧模式：按序列号分组，每个序列生成一个多帧DICOM文件"""
        # 首先对文件进行排序
        sorted_files = self._sort_files_by_name(self.input_files)

        # 按序列号分组文件
        series_groups = {}

        for i, input_file in enumerate(sorted_files):
            filename = os.path.basename(input_file)
            name_without_ext = os.path.splitext(filename)[0]

            # 使用改进的文件名解析逻辑
            series_num, instance_num = self._parse_filename(name_without_ext, i)

            if series_num not in series_groups:
                series_groups[series_num] = []
            series_groups[series_num].append((input_file, instance_num))

        # 按实例号排序每个序列的文件
        for series_num in series_groups:
            series_groups[series_num].sort(key=lambda x: x[1])

        total_series = len(series_groups)
        success_count = 0

        for i, (series_num, files_with_instance) in enumerate(series_groups.items()):
            try:
                # 生成输出文件名
                output_filename = f"series_{series_num:03d}_multiframe.dcm"
                output_filepath = os.path.join(self.output_dir, output_filename)

                # 提取文件路径
                files = [f[0] for f in files_with_instance]

                # 验证所有输入文件
                for file_path in files:
                    if not os.path.exists(file_path):
                        raise FileNotFoundError(f"输入文件不存在: {file_path}")

                # 检查输出目录是否可写
                if not os.access(self.output_dir, os.W_OK):
                    raise PermissionError(f"无法写入输出目录: {self.output_dir}")

                self.log_updated.emit(f"正在转换多帧序列: 序列{series_num} ({len(files)}张图片)")

                # 执行多帧转换
                self.jpg_to_dicom_multi_frame(files, output_filepath,
                                            self.patient_name, self.study_description,
                                            series_num)

                success_count += 1
                self.log_updated.emit(f"✓ 多帧转换成功: 序列{series_num} -> {output_filename}")

            except Exception as e:
                error_msg = f"✗ 多帧转换失败: 序列{series_num} - {str(e)}"
                self.log_updated.emit(error_msg)
                # 记录详细错误信息用于调试
                import traceback
                self.log_updated.emit(f"  详细错误: {traceback.format_exc().splitlines()[-1]}")

            # 更新进度
            progress = int((i + 1) / total_series * 100)
            self.progress_updated.emit(progress)

        # 转换完成
        if success_count == total_series:
            self.conversion_finished.emit(True, f"所有 {total_series} 个序列转换成功！")
        else:
            self.conversion_finished.emit(False,
                f"转换完成：成功 {success_count}/{total_series} 个序列")
    
    def jpg_to_dicom_single(self, input_jpg_path, output_dcm_path, patient_name,
                            study_description, series_number, instance_number, slice_position=None):
        """将JPG图像转换为DICOM格式"""
        # 读取JPG图像
        img = Image.open(input_jpg_path)

        # 转换为灰度图像并获取numpy数组
        if img.mode != 'L':
            img = img.convert("L")

        # 转换为numpy数组，确保数据类型正确
        pixel_array = np.array(img, dtype=np.uint8)

        # 生成唯一的UID - 确保同一批次转换的文件使用相同的Study和Series UID
        timestamp = datetime.datetime.now()

        if not hasattr(self, '_study_uid') or not hasattr(self, '_series_uids'):
            # 第一次调用时初始化UID
            timestamp_str = timestamp.strftime("%Y%m%d%H%M%S")
            base_uid = "1.2.826.0.1.3680043.8.498"

            self._study_uid = f"{base_uid}.{timestamp_str}"
            self._series_uids = {}
            self._timestamp_str = timestamp_str
            self._base_timestamp = timestamp

        # 为每个序列号生成唯一的Series UID
        if series_number not in self._series_uids:
            self._series_uids[series_number] = f"{self._study_uid}.{series_number}"

        study_instance_uid = self._study_uid
        series_instance_uid = self._series_uids[series_number]

        # SOP Instance UID需要对每个实例都是唯一的
        microseconds = timestamp.microsecond
        sop_instance_uid = f"{series_instance_uid}.{instance_number}.{microseconds}"

        # 创建文件元信息
        file_meta = pydicom.dataset.FileMetaDataset()
        file_meta.FileMetaInformationGroupLength = 188
        file_meta.FileMetaInformationVersion = b'\x00\x01'
        file_meta.MediaStorageSOPClassUID = '1.2.840.10008.*******.1.7'  # Secondary Capture Image Storage
        file_meta.MediaStorageSOPInstanceUID = sop_instance_uid
        file_meta.ImplementationClassUID = '1.2.826.0.1.3680043.8.498.1'
        file_meta.ImplementationVersionName = 'JPG2DICOM_v2.0'
        file_meta.TransferSyntaxUID = '1.2.840.10008.1.2'  # Implicit VR Little Endian

        # 创建DICOM数据集
        ds = pydicom.dataset.FileDataset(output_dcm_path, {}, file_meta=file_meta, preamble=b"\0" * 128)

        # 患者信息 - 同一批次使用相同的患者ID
        ds.PatientName = patient_name[:64] if patient_name else "Anonymous"
        ds.PatientID = f"PID_{self._timestamp_str}"
        ds.PatientBirthDate = ""  # 空值，符合DICOM标准
        ds.PatientSex = ""  # 空值，符合DICOM标准

        # 检查信息 - 同一批次使用相同的Study信息
        ds.StudyInstanceUID = study_instance_uid
        ds.StudyDescription = study_description if study_description else "JPG to DICOM Conversion"
        ds.StudyID = f"STU_{self._timestamp_str}"
        ds.AccessionNumber = f"ACC_{self._timestamp_str}"

        # 序列信息
        ds.SeriesInstanceUID = series_instance_uid
        ds.SeriesNumber = series_number
        ds.SeriesDescription = f"Converted Series {series_number}"

        # 实例信息
        ds.SOPClassUID = '1.2.840.10008.*******.1.7'  # Secondary Capture Image Storage
        ds.SOPInstanceUID = sop_instance_uid
        ds.InstanceNumber = instance_number

        # 设备和模态信息
        ds.Modality = "OT"  # Other
        ds.ConversionType = "WSD"  # Workstation
        ds.SecondaryCaptureDeviceManufacturer = "JPG2DICOM"
        ds.SecondaryCaptureDeviceManufacturerModelName = "JPG2DICOM_GUI_v2.0"
        ds.SecondaryCaptureDeviceSoftwareVersions = "2.0"

        # 添加日期时间信息
        ds.StudyDate = timestamp.strftime('%Y%m%d')
        ds.StudyTime = timestamp.strftime('%H%M%S.%f')[:-3]  # 包含毫秒
        ds.SeriesDate = timestamp.strftime('%Y%m%d')
        ds.SeriesTime = timestamp.strftime('%H%M%S.%f')[:-3]
        ds.ContentDate = timestamp.strftime('%Y%m%d')
        ds.ContentTime = timestamp.strftime('%H%M%S.%f')[:-3]
        ds.AcquisitionDate = timestamp.strftime('%Y%m%d')
        ds.AcquisitionTime = timestamp.strftime('%H%M%S.%f')[:-3]

        # 图像相关参数
        ds.Rows, ds.Columns = pixel_array.shape
        ds.SamplesPerPixel = 1
        ds.BitsAllocated = 8
        ds.BitsStored = 8
        ds.HighBit = 7
        ds.PixelRepresentation = 0
        ds.PhotometricInterpretation = "MONOCHROME2"

        # 像素间距和位置信息
        ds.PixelSpacing = [1.0, 1.0]  # 默认像素间距

        # 使用切片位置参数来计算正确的Z坐标
        if slice_position is not None:
            z_position = float(slice_position)
        else:
            z_position = float(instance_number - 1)

        ds.ImagePositionPatient = [0.0, 0.0, z_position]
        ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
        ds.SliceThickness = 1.0
        ds.SliceLocation = z_position

        # 添加切片间距信息，确保3D Slicer能正确识别
        ds.SpacingBetweenSlices = 1.0

        # 正确设置像素数据
        ds.PixelData = pixel_array.tobytes()

        # 设置传输语法
        ds.is_little_endian = True
        ds.is_implicit_VR = True

        # 保存DICOM文件
        ds.save_as(output_dcm_path, write_like_original=False)

        # 验证生成的DICOM文件（如果验证失败不要中断转换）
        try:
            self._validate_dicom_file(output_dcm_path)
        except Exception as e:
            # 验证失败不应该中断转换过程
            print(f"DICOM验证警告: {e}")

    def jpg_to_dicom_multi_frame(self, input_jpg_paths, output_dcm_path, patient_name,
                                study_description, series_number):
        """将多个JPG图像转换为一个多帧DICOM文件"""
        if not input_jpg_paths:
            raise ValueError("输入文件列表为空")

        # 读取第一张图片获取基本信息
        first_img = Image.open(input_jpg_paths[0])
        width, height = first_img.size

        # 读取所有图片并转换为灰度numpy数组
        pixel_arrays = []
        for jpg_path in input_jpg_paths:
            img = Image.open(jpg_path)
            # 确保所有图片尺寸一致
            if img.size != (width, height):
                img = img.resize((width, height), Image.Resampling.LANCZOS)

            # 转换为灰度并获取numpy数组
            if img.mode != 'L':
                img = img.convert("L")
            pixel_array = np.array(img, dtype=np.uint8)
            pixel_arrays.append(pixel_array)

        # 生成唯一的UID
        timestamp = datetime.datetime.now()
        timestamp_str = timestamp.strftime("%Y%m%d%H%M%S")
        microseconds = timestamp.microsecond

        # 生成基础UID前缀
        base_uid = "1.2.826.0.1.3680043.8.498"
        study_instance_uid = f"{base_uid}.{timestamp_str}"
        series_instance_uid = f"{base_uid}.{timestamp_str}.{series_number}"
        sop_instance_uid = f"{base_uid}.{timestamp_str}.{series_number}.1.{microseconds}"

        # 创建文件元信息 - 使用多帧专用的SOP Class
        file_meta = pydicom.dataset.FileMetaDataset()
        file_meta.FileMetaInformationGroupLength = 188
        file_meta.FileMetaInformationVersion = b'\x00\x01'
        file_meta.MediaStorageSOPClassUID = '1.2.840.10008.*******.1.7'  # Secondary Capture Image Storage
        file_meta.MediaStorageSOPInstanceUID = sop_instance_uid
        file_meta.ImplementationClassUID = '1.2.826.0.1.3680043.8.498.1'
        file_meta.ImplementationVersionName = 'JPG2DICOM_MULTI_v2.0'
        file_meta.TransferSyntaxUID = '1.2.840.10008.1.2'  # Implicit VR Little Endian

        # 创建DICOM数据集
        ds = pydicom.dataset.FileDataset(output_dcm_path, {}, file_meta=file_meta, preamble=b"\0" * 128)

        # 患者信息
        ds.PatientName = patient_name[:64] if patient_name else "Anonymous"
        ds.PatientID = f"PID_{timestamp_str}_MF"
        ds.PatientBirthDate = ""
        ds.PatientSex = ""

        # 检查信息
        ds.StudyInstanceUID = study_instance_uid
        ds.StudyDescription = study_description if study_description else "Multi-frame JPG to DICOM Conversion"
        ds.StudyID = f"STU_{timestamp_str}"
        ds.AccessionNumber = f"ACC_{timestamp_str}"

        # 序列信息
        ds.SeriesInstanceUID = series_instance_uid
        ds.SeriesNumber = series_number
        ds.SeriesDescription = f"Multi-frame Series {series_number}"

        # 实例信息
        ds.SOPClassUID = '1.2.840.10008.*******.1.7'  # Secondary Capture Image Storage
        ds.SOPInstanceUID = sop_instance_uid
        ds.InstanceNumber = 1

        # 设备和模态信息
        ds.Modality = "OT"  # Other
        ds.ConversionType = "WSD"  # Workstation
        ds.SecondaryCaptureDeviceManufacturer = "JPG2DICOM"
        ds.SecondaryCaptureDeviceManufacturerModelName = "JPG2DICOM_MULTI_v2.0"
        ds.SecondaryCaptureDeviceSoftwareVersions = "2.0"

        # 添加日期时间信息
        ds.StudyDate = timestamp.strftime('%Y%m%d')
        ds.StudyTime = timestamp.strftime('%H%M%S.%f')[:-3]
        ds.SeriesDate = timestamp.strftime('%Y%m%d')
        ds.SeriesTime = timestamp.strftime('%H%M%S.%f')[:-3]
        ds.ContentDate = timestamp.strftime('%Y%m%d')
        ds.ContentTime = timestamp.strftime('%H%M%S.%f')[:-3]
        ds.AcquisitionDate = timestamp.strftime('%Y%m%d')
        ds.AcquisitionTime = timestamp.strftime('%H%M%S.%f')[:-3]

        # 多帧图像相关参数
        num_frames = len(pixel_arrays)
        ds.NumberOfFrames = num_frames
        ds.Rows, ds.Columns = height, width
        ds.SamplesPerPixel = 1
        ds.BitsAllocated = 8
        ds.BitsStored = 8
        ds.HighBit = 7
        ds.PixelRepresentation = 0
        ds.PhotometricInterpretation = "MONOCHROME2"

        # 像素间距和位置信息
        ds.PixelSpacing = [1.0, 1.0]
        ds.SliceThickness = 1.0

        # 多帧功能组序列 (Enhanced Multi-frame相关)
        # 为了简化，我们使用基本的多帧格式

        # 合并所有帧的像素数据
        combined_pixel_data = b''
        for pixel_array in pixel_arrays:
            combined_pixel_data += pixel_array.tobytes()

        ds.PixelData = combined_pixel_data

        # 设置传输语法
        ds.is_little_endian = True
        ds.is_implicit_VR = True

        # 保存DICOM文件
        ds.save_as(output_dcm_path, write_like_original=False)

        # 验证生成的DICOM文件（如果验证失败不要中断转换）
        try:
            self._validate_dicom_file(output_dcm_path)
        except Exception as e:
            # 验证失败不应该中断转换过程
            print(f"DICOM验证警告: {e}")

    def _validate_dicom_file(self, dcm_path):
        """验证生成的DICOM文件是否有效"""
        try:
            # 尝试读取DICOM文件
            ds = pydicom.dcmread(dcm_path, force=True)

            # 检查必要的标签
            required_tags = [
                'PatientName', 'PatientID', 'StudyInstanceUID', 'SeriesInstanceUID',
                'SOPInstanceUID', 'SOPClassUID', 'Modality', 'Rows', 'Columns',
                'BitsAllocated', 'BitsStored', 'SamplesPerPixel', 'PhotometricInterpretation'
            ]

            missing_tags = []
            for tag in required_tags:
                if not hasattr(ds, tag) or getattr(ds, tag) is None:
                    missing_tags.append(tag)

            if missing_tags:
                raise ValueError(f"缺少必要的DICOM标签: {', '.join(missing_tags)}")

            # 验证像素数据
            if not hasattr(ds, 'PixelData') or ds.PixelData is None:
                raise ValueError("缺少像素数据")

            # 验证图像尺寸
            expected_size = ds.Rows * ds.Columns * (ds.BitsAllocated // 8)
            if hasattr(ds, 'NumberOfFrames'):
                expected_size *= ds.NumberOfFrames

            if len(ds.PixelData) != expected_size:
                raise ValueError(f"像素数据大小不匹配: 期望 {expected_size}, 实际 {len(ds.PixelData)}")

        except Exception as e:
            raise ValueError(f"DICOM文件验证失败: {str(e)}")


class JPGToDicomGUI(QMainWindow):
    """JPG转DICOM GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.input_files = []
        self.output_dir = ""
        self.conversion_worker = None
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("JPG转DICOM工具")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板 - 控制区域
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板 - 日志区域
        right_panel = self.create_log_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([400, 500])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
    def create_control_panel(self):
        """创建控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        
        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        
        # 输入文件选择
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入文件:"))
        self.select_files_btn = QPushButton("选择JPG文件")
        self.select_files_btn.clicked.connect(self.select_input_files)
        input_layout.addWidget(self.select_files_btn)
        file_layout.addLayout(input_layout)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(150)
        file_layout.addWidget(QLabel("已选择的文件:"))
        file_layout.addWidget(self.file_list)
        
        # 输出目录选择
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("选择输出目录...")
        output_layout.addWidget(self.output_dir_edit)
        self.select_output_btn = QPushButton("浏览")
        self.select_output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.select_output_btn)
        file_layout.addLayout(output_layout)
        
        layout.addWidget(file_group)
        
        # DICOM参数组
        param_group = QGroupBox("DICOM参数")
        param_layout = QGridLayout(param_group)

        param_layout.addWidget(QLabel("患者姓名:"), 0, 0)
        self.patient_name_edit = QLineEdit("Anonymous")
        param_layout.addWidget(self.patient_name_edit, 0, 1)

        param_layout.addWidget(QLabel("检查描述:"), 1, 0)
        self.study_desc_edit = QLineEdit("JPG to DICOM Conversion")
        param_layout.addWidget(self.study_desc_edit, 1, 1)

        layout.addWidget(param_group)

        # 转换模式组
        mode_group = QGroupBox("转换模式")
        mode_layout = QVBoxLayout(mode_group)

        self.single_frame_radio = QRadioButton("单帧模式 (每个JPG文件生成一个DICOM文件)")
        self.single_frame_radio.setChecked(True)
        mode_layout.addWidget(self.single_frame_radio)

        self.multi_frame_radio = QRadioButton("多帧模式 (同序列的JPG文件合并为一个多帧DICOM文件)")
        mode_layout.addWidget(self.multi_frame_radio)

        # 添加模式说明
        mode_info = QLabel("• 单帧模式：00010001.jpg → 00010001.dcm\n"
                          "• 多帧模式：00010001.jpg + 00010002.jpg → series_001_multiframe.dcm")
        mode_info.setStyleSheet("color: #666; font-size: 10px; margin: 5px;")
        mode_layout.addWidget(mode_info)

        layout.addWidget(mode_group)
        
        # 转换控制组
        control_group = QGroupBox("转换控制")
        control_layout = QVBoxLayout(control_group)
        
        # 转换按钮
        self.convert_btn = QPushButton("开始转换")
        self.convert_btn.clicked.connect(self.start_conversion)
        self.convert_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        control_layout.addWidget(self.convert_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        control_layout.addWidget(self.progress_bar)
        
        layout.addWidget(control_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
        
    def create_log_panel(self):
        """创建日志面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        
        layout.addWidget(QLabel("转换日志:"))
        
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)
        
        # 清除日志按钮
        clear_btn = QPushButton("清除日志")
        clear_btn.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_btn)
        
        return panel
        
    def select_input_files(self):
        """选择输入文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择JPG文件", "", 
            "图像文件 (*.jpg *.jpeg *.png *.bmp);;所有文件 (*.*)"
        )
        
        if files:
            self.input_files = files
            self.file_list.clear()
            for file in files:
                self.file_list.addItem(os.path.basename(file))
            self.log(f"已选择 {len(files)} 个文件")
            
    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_dir_edit.setText(dir_path)
            self.log(f"输出目录: {dir_path}")
            
    def start_conversion(self):
        """开始转换"""
        # 验证输入
        if not self.input_files:
            QMessageBox.warning(self, "警告", "请先选择输入文件！")
            return
            
        if not self.output_dir:
            QMessageBox.warning(self, "警告", "请先选择输出目录！")
            return
            
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
        # 禁用转换按钮
        self.convert_btn.setEnabled(False)
        self.convert_btn.setText("转换中...")
        
        # 重置进度条
        self.progress_bar.setValue(0)
        
        # 启动转换线程
        multi_frame_mode = self.multi_frame_radio.isChecked()
        self.conversion_worker = ConversionWorker(
            self.input_files,
            self.output_dir,
            self.patient_name_edit.text(),
            self.study_desc_edit.text(),
            multi_frame_mode
        )
        
        self.conversion_worker.progress_updated.connect(self.progress_bar.setValue)
        self.conversion_worker.log_updated.connect(self.log)
        self.conversion_worker.conversion_finished.connect(self.on_conversion_finished)
        
        self.conversion_worker.start()
        self.log("开始转换...")
        
    def on_conversion_finished(self, success, message):
        """转换完成处理"""
        self.convert_btn.setEnabled(True)
        self.convert_btn.setText("开始转换")
        
        if success:
            QMessageBox.information(self, "成功", message)
            self.log("转换完成！")
        else:
            QMessageBox.warning(self, "警告", message)
            self.log("转换完成，但有错误发生")
            
        self.statusBar().showMessage("转换完成")
        
    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.ensureCursorVisible()


def main():
    app = QApplication(sys.argv)
    
    # 设置应用程序样式
    app.setStyle('Fusion')
    
    window = JPGToDicomGUI()
    window.show()
    
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
