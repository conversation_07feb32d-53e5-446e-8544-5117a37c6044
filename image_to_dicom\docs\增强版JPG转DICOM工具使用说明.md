# 增强版JPG转DICOM工具使用说明

## 概述

增强版JPG转DICOM工具基于真实DICOM metadata（来自GE Discovery CT750 HD设备）开发，能够最大化还原DICOM文件的完整信息，包括设备信息、扫描参数、几何信息等。

## 主要特性

### ✅ 真实DICOM Metadata还原
- **设备信息**: GE MEDICAL SYSTEMS Discovery CT750 HD
- **扫描参数**: KVP=120, 曝光时间=600ms, 管电流=150mA等
- **重建参数**: 标准卷积核, 像素间距0.878906mm
- **几何信息**: 完整的图像位置、方向、切片厚度等

### ✅ HU值映射系统
- **多种映射策略**: 线性映射、基于组织类型映射、窗宽窗位映射、查找表映射
- **真实HU值**: 将JPG灰度值映射到医学标准的HU值（Hounsfield Unit）
- **组织特异性**: 支持胸部、腹部等不同部位的组织密度映射
- **窗宽窗位**: 支持肺窗、纵隔窗、骨窗等标准CT窗设置

### ✅ 坐标系统修复
- 自动修复图像位置和方向信息
- 解决3D Slicer中的几何信息警告
- 确保正确的切片间距和位置

### ✅ 模板系统
- 内置GE CT750默认模板
- 支持从真实DICOM文件创建自定义模板
- 模板可保存和重复使用

## 文件结构

```
2DICOM/
├── enhanced_jpg_to_dicom.py          # 核心转换器
├── enhanced_jpg_to_dicom_gui.py      # GUI界面
├── dicom_metadata_analyzer.py        # Metadata分析器
├── hu_value_mapper.py                # HU值映射器
├── ge_ct750_template.json            # 默认GE CT750模板
├── hu_mapping_configs.json           # HU值映射配置
└── 增强版JPG转DICOM工具使用说明.md   # 本文档
```

## 使用方法

### 方法1: GUI界面（推荐）

```bash
cd 2DICOM
python enhanced_jpg_to_dicom_gui.py
```

GUI界面包含三个标签页：
1. **JPG转DICOM**: 主要转换功能
2. **HU值映射**: 配置HU值映射策略和参数
3. **模板管理**: 管理DICOM metadata模板

#### 转换步骤：
1. 选择JPG文件（支持批量选择）
2. 选择输出目录
3. 设置患者信息（姓名、ID、检查描述等）
4. 点击"开始转换"

### 方法2: 命令行

```bash
cd 2DICOM

# 单文件转换
python enhanced_jpg_to_dicom.py input.jpg output.dcm --patient-name "患者姓名"

# 批量转换
python enhanced_jpg_to_dicom.py input_dir/ output_dir/ --patient-name "患者姓名"

# 使用自定义模板
python enhanced_jpg_to_dicom.py input_dir/ output_dir/ --template custom_template.json

# 从DICOM目录创建模板
python enhanced_jpg_to_dicom.py --create-template /path/to/dicom/dir
```

### 方法3: Python代码调用

```python
from enhanced_jpg_to_dicom import EnhancedJpgToDicom

# 创建转换器
converter = EnhancedJpgToDicom()

# 单文件转换
converter.convert_jpg_to_dicom(
    "input.jpg", "output.dcm",
    patient_name="患者姓名",
    patient_id="PID001",
    study_description="胸部CT",
    series_number=1,
    instance_number=1,
    slice_position=0.0
)

# 批量转换
success_files = converter.convert_directory(
    "jpg_dir/", "dicom_dir/",
    patient_name="患者姓名",
    study_description="胸部CT"
)
```

## 模板管理

### 使用默认模板
工具内置基于真实GE CT750 HD设备的默认模板，包含完整的设备信息和扫描参数。

### 创建自定义模板
1. 准备包含真实DICOM文件的目录
2. 使用GUI的"从DICOM目录创建模板"功能
3. 或使用命令行：`python enhanced_jpg_to_dicom.py --create-template /path/to/dicom/dir`

### 模板内容
模板包含以下信息：
- **device_info**: 设备制造商、型号、工作站名称等
- **acquisition_params**: KVP、曝光时间、管电流、滤波器等
- **image_params**: 图像类型、位深度、窗宽窗位等
- **geometry_template**: 图像方向、位置、像素间距等
- **series_info**: 序列描述、检查描述等

## HU值映射详细说明

### HU值（Hounsfield Unit）简介

HU值是CT图像中表示组织密度的标准单位，以水的密度为基准（0 HU）：

| 组织类型 | HU值范围 | 说明 |
|---------|----------|------|
| 空气 | -1000 HU | 最低密度 |
| 肺组织 | -900 到 -500 HU | 含气组织 |
| 脂肪 | -120 到 -60 HU | 低密度软组织 |
| 水 | 0 HU | 基准密度 |
| 软组织 | 20 到 60 HU | 肌肉、器官等 |
| 血液 | 40 到 60 HU | 液体组织 |
| 骨骼 | 150 到 1000+ HU | 高密度组织 |

### 映射策略详解

#### 1. 基于组织类型映射 (tissue)
根据不同解剖部位的组织分布特点进行映射：

**胸部映射 (chest)**:
- 灰度值 0-50 → HU值 -1000 到 -500 (空气和肺组织)
- 灰度值 51-100 → HU值 -500 到 0 (肺组织到软组织)
- 灰度值 101-180 → HU值 0 到 80 (软组织)
- 灰度值 181-255 → HU值 80 到 1000 (骨骼)

**腹部映射 (abdomen)**:
- 灰度值 0-30 → HU值 -200 到 -50 (气体和脂肪)
- 灰度值 31-200 → HU值 -50 到 100 (软组织和器官)
- 灰度值 201-255 → HU值 100 到 1000 (骨骼和钙化)

#### 2. 线性映射 (linear)
将灰度值线性映射到指定的HU值范围：
```
HU = (灰度值 - 0) × (最大HU - 最小HU) / 255 + 最小HU
```

#### 3. 基于窗宽窗位映射 (windowing)
根据CT显示窗口设置进行映射：

| 窗口类型 | 窗位 (Center) | 窗宽 (Width) | 适用场景 |
|---------|---------------|--------------|----------|
| 肺窗 | -600 HU | 1600 HU | 观察肺实质 |
| 纵隔窗 | 40 HU | 350 HU | 观察软组织 |
| 骨窗 | 300 HU | 1500 HU | 观察骨骼结构 |

#### 4. 查找表映射 (lookup)
使用预定义的灰度值到HU值的对应表，支持非线性映射。

### HU值映射配置

在GUI的"HU值映射"标签页中：

1. **启用HU值映射**: 勾选以启用功能
2. **选择映射策略**: 从下拉菜单选择合适的策略
3. **配置参数**: 根据选择的策略设置相应参数
4. **预设按钮**: 快速应用常用的窗宽窗位设置

### 命令行HU值映射

```python
from enhanced_jpg_to_dicom import EnhancedJpgToDicom
from hu_value_mapper import MappingStrategy

# 创建启用HU映射的转换器
converter = EnhancedJpgToDicom(
    enable_hu_mapping=True,
    hu_mapping_strategy=MappingStrategy.TISSUE_BASED
)

# 转换时指定HU映射参数
converter.convert_jpg_to_dicom(
    "input.jpg", "output.dcm",
    patient_name="患者姓名",
    hu_mapping_params={"tissue_type": "chest"}
)
```

## 输出DICOM文件特性

转换后的DICOM文件具有以下特性：

### 完整的Metadata
- ✅ 患者信息（姓名、ID、年龄、性别）
- ✅ 检查信息（日期、时间、描述、Accession Number）
- ✅ 设备信息（制造商、型号、软件版本）
- ✅ 扫描参数（KVP、曝光时间、管电流等）
- ✅ 图像参数（尺寸、位深度、窗宽窗位）
- ✅ HU值信息（RescaleIntercept、RescaleSlope、RescaleType）

### 正确的几何信息
- ✅ ImagePositionPatient（图像位置）
- ✅ ImageOrientationPatient（图像方向）
- ✅ PixelSpacing（像素间距）
- ✅ SliceThickness（切片厚度）
- ✅ SpacingBetweenSlices（切片间距）
- ✅ SliceLocation（切片位置）

### 兼容性
- ✅ 3D Slicer完全兼容
- ✅ ImageJ/FIJI支持
- ✅ DICOM Viewer软件支持
- ✅ PACS系统兼容

## 与原版工具对比

| 特性 | 原版工具 | 增强版工具 |
|------|----------|------------|
| 基本转换 | ✅ | ✅ |
| HU值映射 | ❌ | ✅ 多种策略 |
| 真实设备信息 | ❌ | ✅ GE CT750 HD |
| 完整扫描参数 | ❌ | ✅ KVP, 曝光时间等 |
| 几何信息修复 | ❌ | ✅ 自动修复 |
| 坐标系统修复 | ❌ | ✅ 解决方向问题 |
| 模板系统 | ❌ | ✅ 可自定义 |
| 批量处理 | ✅ | ✅ 改进版 |
| GUI界面 | ✅ | ✅ 增强版 |

## 测试验证

运行测试脚本验证工具功能：

```bash
cd 2DICOM
python test_enhanced_converter.py
```

测试内容包括：
- 创建测试JPG文件
- 执行批量转换
- 验证DICOM文件有效性
- 检查metadata完整性
- 测试模板功能
- 验证几何信息

## 注意事项

1. **文件命名**: 建议使用数字命名（如00000001.jpg），工具会自动排序
2. **图像格式**: 支持JPG、PNG、BMP等格式，会自动转换为灰度
3. **输出格式**: 生成标准DICOM格式，8位无符号整数
4. **内存使用**: 大批量转换时注意内存使用情况
5. **权限要求**: 确保对输出目录有写入权限

## 故障排除

### 常见问题

**Q: 转换后的DICOM文件在3D Slicer中显示方向错误？**
A: 工具已集成坐标系统修复功能，会自动处理方向问题。

**Q: 如何使用自己的DICOM设备信息？**
A: 使用"从DICOM目录创建模板"功能，从真实DICOM文件中提取设备信息。

**Q: 转换速度较慢？**
A: 大批量转换时，工具会应用几何信息修复，这需要一些时间。可以考虑分批处理。

**Q: 生成的DICOM文件很大？**
A: 工具保留了完整的metadata信息，文件会比简单转换稍大，这是正常的。

### 错误信息

如果遇到错误，请检查：
1. 输入文件是否存在且可读
2. 输出目录是否有写入权限
3. 图像文件是否损坏
4. Python环境是否安装了所需依赖

## 依赖要求

```
pydicom>=2.3.0
Pillow>=9.0.0
numpy>=1.21.0
PyQt5>=5.15.0 (仅GUI需要)
```

安装依赖：
```bash
pip install pydicom Pillow numpy PyQt5
```

## 更新日志

### v1.0 (2025-01-17)
- 基于真实GE CT750 HD metadata开发
- 集成坐标系统修复功能
- 实现模板系统
- 添加增强版GUI界面
- 完整的测试验证

---

**开发者**: AI Assistant  
**日期**: 2025-01-17  
**版本**: 1.0
