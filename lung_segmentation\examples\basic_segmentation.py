#!/usr/bin/env python3
"""
基础肺部CT分割示例
"""

import logging
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from lung_ct_segmenter.core.segmenter import LungCTSegmenter

def main():
    # 配置日志
    logging.basicConfig(level=logging.INFO, 
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    # 输入和输出路径
    input_ct_path = "path/to/your/ct_scan.nii.gz"
    output_dir = "path/to/output/directory"
    
    try:
        # 创建分割器 - 使用Lungmask R231模型
        segmenter = LungCTSegmenter(model_type="lungmask", 
                                   model_variant="R231",
                                   gaussian_sigma=2.0,
                                   morphological_radius=1)
        
        # 配置分割参数
        segmenter.smooth_results = True
        segmenter.shrink_masks = True
        
        # 执行分割
        print(f"开始分割CT图像: {input_ct_path}")
        results = segmenter.segment_from_file(input_ct_path, output_dir)
        
        print(f"分割完成！结果保存在: {output_dir}")
        print(f"分割的结构: {list(results.keys())}")
        
        # 使用肺叶分割模型
        print("\n使用肺叶分割模型...")
        lobe_segmenter = LungCTSegmenter(model_type="lungmask", 
                                        model_variant="LTRCLobes",
                                        gaussian_sigma=1.5,
                                        morphological_radius=2)
        lobe_results = lobe_segmenter.segment_from_file(input_ct_path, 
                                                       output_dir + "_lobes")
        
        print(f"肺叶分割完成！分割的结构: {list(lobe_results.keys())}")
        
    except Exception as e:
        print(f"分割过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())