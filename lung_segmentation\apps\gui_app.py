import sys
import os
import logging
import numpy as np
import SimpleITK as sitk
from pathlib import Path
import traceback
os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
                             QLabel, QLineEdit, QPushButton, QFileDialog, QComboBox,
                             QCheckBox, QSpinBox, QMessageBox, QGroupBox, QRadioButton,
                             QButtonGroup, QTabWidget, QTextEdit, QSplitter, QSlider, QProgressBar,
                             QDoubleSpinBox, QListWidget, QBoxLayout, QDialog, QDialogButtonBox,
                             QTableWidget, QTableWidgetItem, QHeaderView)
from PyQt5.QtCore import Qt, QThread, pyqtSignal

# 忽略PyQt5的弃用警告
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

import matplotlib
matplotlib.use('Qt5Agg')
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体显示中文
matplotlib.rcParams['axes.unicode_minus'] = False  # 正常显示负号
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.pyplot as plt

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))
# 导入lung_ct_segmenter库
from lung_segmentation.core.segmenter import LungCTSegmenter

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('lung_ct_segmenter.log')
    ]
)
logger = logging.getLogger(__name__)

# 全局异常处理函数
def global_exception_handler(exctype, value, tb):
    error_msg = ''.join(traceback.format_exception(exctype, value, tb))
    logger.error(f"未捕获的异常: {error_msg}")
    QMessageBox.critical(None, "程序错误", 
                        f"程序发生未处理的异常:\n{str(value)}\n\n详细信息已记录到日志文件。")
    # 调用原始的异常处理器
    sys.__excepthook__(exctype, value, tb)

# 设置全局异常处理器
sys.excepthook = global_exception_handler

class SegmentationThread(QThread):
    """分割线程，用于在后台执行分割操作"""
    finished = pyqtSignal(dict)  # 完成信号，传递分割结果
    error = pyqtSignal(str)      # 错误信号，传递错误消息
    progress = pyqtSignal(str)   # 进度信号，传递进度消息
    percent_progress = pyqtSignal(int)  # 百分比进度信号
    
    def __init__(self, segmenter, input_path, output_path, batch_mode=False, params=None):
        super().__init__()
        self.segmenter = segmenter
        self.input_path = input_path
        self.output_path = output_path
        self.batch_mode = batch_mode
        self.params = params if params else {}
        
        # 设置进度回调
        self.segmenter.set_progress_callback(self._progress_callback)
        
    def _progress_callback(self, message, percent=None):
        """处理来自分割器的进度更新"""
        self.progress.emit(message)
        if percent is not None:
            self.percent_progress.emit(percent)
    
    def run(self):
        """执行分割操作"""
        try:
            logging.info(f"开始分割任务: {'批处理模式' if self.batch_mode else '单文件模式'}")
            self.progress.emit("开始分割...")
            
            # 应用参数
            for key, value in self.params.items():
                setattr(self.segmenter, key, value)
                
            # 执行分割
            if self.batch_mode:
                self.segmenter.batch_segment(self.input_path, self.output_path)
                self.finished.emit({})  # 批处理模式没有返回结果
            else:
                results = self.segmenter.segment_from_file(self.input_path, self.output_path)
                self.finished.emit(results)
                
            logging.info("分割任务完成")
            
        except Exception as e:
            error_msg = f"分割过程中发生错误: {str(e)}"
            logging.error(error_msg)
            logging.error(traceback.format_exc())
            self.error.emit(error_msg)
            self.finished.emit({})  # 发送空结果


class ImageViewer(QWidget):
    """图像查看器组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.figure = Figure(figsize=(5, 4), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)

        # 设置图像属性
        self.image = None
        self.mask = None
        self.slice_index = 0
        self.total_slices = 0
        
        # 窗宽窗位设置
        self.window_width = 1400  # 默认窗宽
        self.window_center = -300  # 默认窗位
        
        # 添加鼠标事件监听
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        self.canvas.mpl_connect('scroll_event', self.on_mouse_scroll)  # 添加滚轮事件监听
        
        # 设置焦点策略，使控件可以接收键盘事件
        self.setFocusPolicy(Qt.StrongFocus)
        
        # 创建布局
        layout = QVBoxLayout()
        layout.addWidget(self.canvas, 1)  # 添加权重，使图像区域可以伸展
        
        # 添加控制面板
        control_panel = QWidget()
        control_layout = QVBoxLayout(control_panel)
        control_layout.setContentsMargins(0, 5, 0, 0)  # 减少边距
        control_layout.setObjectName("control_layout")  # 设置对象名称
        
        # 切片滑条
        slice_layout = QHBoxLayout()
        slice_layout.addWidget(QLabel("切片:"))
        self.slice_label = QLabel("0/0")
        self.slice_label.setMinimumWidth(50)
        slice_layout.addWidget(self.slice_label)
        self.slice_slider = QSlider(Qt.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.valueChanged.connect(self.on_slice_changed)
        slice_layout.addWidget(self.slice_slider)
        control_layout.addLayout(slice_layout)
        
        # 窗宽滑条
        window_width_layout = QHBoxLayout()
        window_width_layout.addWidget(QLabel("窗宽:"))
        self.window_width_label = QLabel(f"{self.window_width}")
        self.window_width_label.setMinimumWidth(50)
        window_width_layout.addWidget(self.window_width_label)
        self.window_width_slider = QSlider(Qt.Horizontal)
        self.window_width_slider.setMinimum(1)
        self.window_width_slider.setMaximum(4000)
        self.window_width_slider.setValue(self.window_width)
        self.window_width_slider.valueChanged.connect(self.on_window_width_changed)
        window_width_layout.addWidget(self.window_width_slider)
        control_layout.addLayout(window_width_layout)
        
        # 窗位滑条
        window_center_layout = QHBoxLayout()
        window_center_layout.addWidget(QLabel("窗位:"))
        self.window_center_label = QLabel(f"{self.window_center}")
        self.window_center_label.setMinimumWidth(50)
        window_center_layout.addWidget(self.window_center_label)
        self.window_center_slider = QSlider(Qt.Horizontal)
        self.window_center_slider.setMinimum(-1000)
        self.window_center_slider.setMaximum(1000)
        self.window_center_slider.setValue(self.window_center)
        self.window_center_slider.valueChanged.connect(self.on_window_center_changed)
        window_center_layout.addWidget(self.window_center_slider)
        control_layout.addLayout(window_center_layout)
        
        # 预设窗宽窗位按钮和CT值显示放在同一行
        bottom_layout = QHBoxLayout()
        
        # 预设窗宽窗位按钮
        preset_layout = QHBoxLayout()
        preset_layout.addWidget(QLabel("预设:"))
        
        # 肺窗按钮
        lung_btn = QPushButton("肺窗")
        lung_btn.setToolTip("窗宽: 1400, 窗位: -500")
        lung_btn.clicked.connect(lambda: self.set_window_preset(1400, -500))
        preset_layout.addWidget(lung_btn)
        
        # 纵隔窗按钮
        mediastinum_btn = QPushButton("纵隔窗")
        mediastinum_btn.setToolTip("窗宽: 350, 窗位: 40")
        mediastinum_btn.clicked.connect(lambda: self.set_window_preset(350, 40))
        preset_layout.addWidget(mediastinum_btn)
        
        # 骨窗按钮
        bone_btn = QPushButton("骨窗")
        bone_btn.setToolTip("窗宽: 1800, 窗位: 400")
        bone_btn.clicked.connect(lambda: self.set_window_preset(1800, 400))
        preset_layout.addWidget(bone_btn)
        
        bottom_layout.addLayout(preset_layout)
        bottom_layout.addStretch(1)  # 添加弹性空间
        
        # CT值显示
        ct_value_layout = QHBoxLayout()
        ct_value_layout.addWidget(QLabel("CT值:"))
        self.ct_value_label = QLabel("N/A")
        self.ct_value_label.setMinimumWidth(100)  # 确保足够宽以显示CT值
        ct_value_layout.addWidget(self.ct_value_label)
        
        bottom_layout.addLayout(ct_value_layout)
        control_layout.addLayout(bottom_layout)
        
        # 添加控制面板到主布局，高度固定
        control_panel.setMaximumHeight(150)
        layout.addWidget(control_panel)
        
        self.setLayout(layout)
        
        # 添加一个定时器，用于延迟更新显示，避免频繁更新导致的性能问题
        from PyQt5.QtCore import QTimer
        self.update_timer = QTimer()
        self.update_timer.setSingleShot(True)
        self.update_timer.timeout.connect(self._delayed_update_display)
        self.update_pending = False

    def set_image(self, image, mask=None):
        """设置要显示的图像和掩码"""
        try:
            # 清除之前的图像，释放内存
            if self.image is not None:
                del self.image
                
            if self.mask is not None:
                del self.mask
                
            # 设置新图像和掩码
            self.image = image
            self.mask = mask
            
            if image is not None:
                # 检查图像维度
                if len(image.shape) != 3:
                    logger.error(f"图像维度错误: {image.shape}, 需要3D图像")
                    QMessageBox.warning(None, "警告", f"图像维度错误: {image.shape}, 需要3D图像")
                    return
                    
                self.total_slices = image.shape[0]
                self.slice_index = self.total_slices // 2
                
                # 更新切片滑条
                self.slice_slider.setMaximum(self.total_slices - 1)
                self.slice_slider.setValue(self.slice_index)
                self.slice_label.setText(f"{self.slice_index + 1}/{self.total_slices}")
                
                # 自动设置合适的窗宽窗位
                self._auto_adjust_window()
                
                # 更新显示
                self.update_display()
            else:
                # 清空显示
                self.ax.clear()
                self.ax.set_title("无图像")
                self.ax.axis('off')
                self.canvas.draw()
                
                # 重置UI
                self.slice_slider.setMaximum(0)
                self.slice_label.setText("0/0")
                self.ct_value_label.setText("N/A")
                
        except Exception as e:
            logger.error(f"设置图像时出错: {e}")
            logger.error(traceback.format_exc())
            QMessageBox.warning(None, "错误", f"设置图像时出错: {str(e)}")

    def update_display(self):
        """更新显示，使用定时器延迟更新，避免频繁更新导致的性能问题"""
        if self.update_pending:
            # 已经有一个更新在等待中，不需要再次设置定时器
            return
            
        self.update_pending = True
        self.update_timer.start(50)  # 50毫秒后更新
        
    def _delayed_update_display(self):
        """实际执行更新显示的函数"""
        self.update_pending = False
        
        try:
            if self.image is None:
                return
    
            self.ax.clear()
            
            # 应用窗宽窗位
            display_image = self._apply_window(self.image[self.slice_index])
            self.ax.imshow(display_image, cmap='gray')
    
            if self.mask is not None:
                try:
                    mask_slice = self.mask[self.slice_index]
                    if mask_slice.max() > 0:
                        # 增加透明度和使用更鲜明的颜色映射
                        colored_mask = np.zeros((*mask_slice.shape, 3), dtype=np.float32)
                        # 为掩码添加红色
                        colored_mask[mask_slice > 0, 0] = 1.0  # 红色通道
                        self.ax.imshow(colored_mask, alpha=0.7)  # 增加透明度
                except Exception as e:
                    logger.error(f"显示掩码时出错: {e}")
    
            # 设置标题，显示切片信息和窗宽窗位
            self.ax.set_title(f"切片: {self.slice_index + 1}/{self.total_slices} | 窗宽: {self.window_width} | 窗位: {self.window_center}")
            
            # 关闭坐标轴，使图像更干净
            self.ax.axis('off')
            
            # 更新画布
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"更新显示时出错: {e}")
            logger.error(traceback.format_exc())

    def _apply_window(self, image):
        """应用窗宽窗位到图像"""
        try:
            # 计算窗宽窗位的范围
            window_min = self.window_center - self.window_width / 2
            window_max = self.window_center + self.window_width / 2
            
            # 裁剪图像到窗宽窗位范围
            windowed = np.clip(image, window_min, window_max)
            
            # 归一化到[0,1]范围用于显示
            if window_max > window_min:
                windowed = (windowed - window_min) / (window_max - window_min)
            else:
                windowed = np.zeros_like(image)
                
            return windowed
        except Exception as e:
            logger.error(f"应用窗宽窗位时出错: {e}")
            return np.zeros_like(image)

    def _auto_adjust_window(self):
        """自动调整窗宽窗位"""
        if self.image is None:
            return
            
        try:
            # 计算图像的平均值和标准差
            mean_val = np.mean(self.image)
            std_val = np.std(self.image)
            
            # 根据图像特性设置窗宽窗位
            # 肺窗默认值
            if mean_val < -300:  # 可能是肺部CT
                self.window_width = 1400
                self.window_center = -500
            else:  # 软组织窗
                self.window_width = 400
                self.window_center = 40
            
            # 更新滑条
            self.window_width_slider.setValue(self.window_width)
            self.window_center_slider.setValue(self.window_center)
            self.window_width_label.setText(f"{self.window_width}")
            self.window_center_label.setText(f"{self.window_center}")
        except Exception as e:
            logger.error(f"自动调整窗宽窗位时出错: {e}")

    def on_slice_changed(self, value):
        """切片滑条值改变时的回调"""
        if self.image is not None and 0 <= value < self.total_slices:
            self.slice_index = value
            self.slice_label.setText(f"{self.slice_index + 1}/{self.total_slices}")
            self.update_display()

    def on_window_width_changed(self, value):
        """窗宽滑条值改变时的回调"""
        self.window_width = value
        self.window_width_label.setText(f"{value}")
        self.update_display()

    def on_window_center_changed(self, value):
        """窗位滑条值改变时的回调"""
        self.window_center = value
        self.window_center_label.setText(f"{value}")
        self.update_display()

    def on_mouse_move(self, event):
        """鼠标移动事件回调"""
        try:
            if event.inaxes and self.image is not None:
                # 获取鼠标位置的图像坐标
                x, y = int(event.xdata + 0.5), int(event.ydata + 0.5)
                
                # 检查坐标是否在图像范围内
                if 0 <= y < self.image.shape[1] and 0 <= x < self.image.shape[2]:
                    # 获取该位置的CT值
                    ct_value = self.image[self.slice_index, y, x]
                    # 显示坐标和CT值
                    self.ct_value_label.setText(f"位置: ({x},{y}) | CT值: {ct_value:.1f} HU")
                    
                    # 如果有掩码，显示掩码值
                    if self.mask is not None:
                        mask_value = self.mask[self.slice_index, y, x]
                        if mask_value > 0:
                            self.ct_value_label.setText(f"位置: ({x},{y}) | CT值: {ct_value:.1f} HU | 掩码: {mask_value}")
                else:
                    self.ct_value_label.setText("超出范围")
            else:
                self.ct_value_label.setText("N/A")
        except Exception as e:
            logger.error(f"鼠标移动事件处理出错: {e}")
            self.ct_value_label.setText("错误")

    def on_mouse_scroll(self, event):
        """鼠标滚轮事件回调"""
        try:
            if self.image is not None:
                # 处理不同版本matplotlib的滚轮事件
                # 有些版本用step，有些用button
                if hasattr(event, 'button'):
                    # 使用button属性
                    if event.button == 'up' or (isinstance(event.button, int) and event.button == 4):
                        # 向上滚动，显示上一个切片
                        self.prev_slice()
                    elif event.button == 'down' or (isinstance(event.button, int) and event.button == 5):
                        # 向下滚动，显示下一个切片
                        self.next_slice()
                elif hasattr(event, 'step'):
                    # 使用step属性
                    if event.step > 0:
                        # 向上滚动，显示上一个切片
                        self.prev_slice()
                    else:
                        # 向下滚动，显示下一个切片
                        self.next_slice()
                
                # 如果鼠标在图像上，更新CT值显示
                if event.inaxes:
                    x, y = int(event.xdata + 0.5), int(event.ydata + 0.5)
                    if 0 <= y < self.image.shape[1] and 0 <= x < self.image.shape[2]:
                        ct_value = self.image[self.slice_index, y, x]
                        self.ct_value_label.setText(f"位置: ({x},{y}) | CT值: {ct_value:.1f} HU")
        except Exception as e:
            logger.error(f"鼠标滚轮事件处理出错: {e}")

    def next_slice(self):
        """显示下一个切片"""
        if self.image is not None and self.slice_index < self.total_slices - 1:
            self.slice_index += 1
            self.slice_slider.setValue(self.slice_index)
            self.update_display()

    def prev_slice(self):
        """显示上一个切片"""
        if self.image is not None and self.slice_index > 0:
            self.slice_index -= 1
            self.slice_slider.setValue(self.slice_index)
            self.update_display()

    def set_window_preset(self, width, center):
        """设置窗宽窗位预设"""
        self.window_width = width
        self.window_center = center
        self.window_width_slider.setValue(self.window_width)
        self.window_center_slider.setValue(self.window_center)
        self.window_width_label.setText(f"{self.window_width}")
        self.window_center_label.setText(f"{self.window_center}")
        self.update_display()

    def keyPressEvent(self, event):
        """处理键盘按键事件"""
        try:
            if self.image is None:
                return super().keyPressEvent(event)
                
            key = event.key()
            
            if key == Qt.Key_Up or key == Qt.Key_Left:
                # 上箭头或左箭头，显示上一个切片
                self.prev_slice()
            elif key == Qt.Key_Down or key == Qt.Key_Right:
                # 下箭头或右箭头，显示下一个切片
                self.next_slice()
            elif key == Qt.Key_PageUp:
                # 翻页键上，向前跳转多个切片
                new_index = max(0, self.slice_index - 10)
                self.slice_slider.setValue(new_index)
            elif key == Qt.Key_PageDown:
                # 翻页键下，向后跳转多个切片
                new_index = min(self.total_slices - 1, self.slice_index + 10)
                self.slice_slider.setValue(new_index)
            elif key == Qt.Key_Home:
                # Home键，跳转到第一个切片
                self.slice_slider.setValue(0)
            elif key == Qt.Key_End:
                # End键，跳转到最后一个切片
                self.slice_slider.setValue(self.total_slices - 1)
            else:
                super().keyPressEvent(event)
        except Exception as e:
            logger.error(f"键盘事件处理出错: {e}")
            super().keyPressEvent(event)

    def set_mask(self, mask):
        """设置掩码"""
        try:
            if mask is None:
                self.mask = None
                self.update_display()
                return
                
            # 检查掩码维度
            if len(mask.shape) != 3:
                logging.error(f"掩码维度错误: {mask.shape}")
                raise ValueError(f"掩码必须是3D数组，当前形状: {mask.shape}")
                
            # 确保掩码是布尔型或uint8类型
            if mask.dtype != bool and mask.dtype != np.uint8:
                mask = mask.astype(np.uint8)
                
            # 设置掩码
            self.mask = mask
            
            # 更新显示
            self.update_display()
            
            # 记录日志
            logging.info(f"已设置掩码，形状: {mask.shape}")
            
        except Exception as e:
            logging.error(f"设置掩码时出错: {str(e)}")
            logging.error(traceback.format_exc())
            QMessageBox.critical(None, "错误", f"设置掩码时出错: {str(e)}")
            self.mask = None

    def set_cropped_image(self, cropped_image):
        """设置裁剪后的图像"""
        try:
            if cropped_image is None:
                return
                
            # 检查图像维度
            if len(cropped_image.shape) != 3:
                logging.error(f"图像维度错误: {cropped_image.shape}")
                raise ValueError(f"裁剪图像必须是3D数组，当前形状: {cropped_image.shape}")
                
            # 设置新图像，但保持当前切片索引不变
            current_index = self.slice_index if self.image is not None else 0
            self.image = cropped_image
            self.total_slices = cropped_image.shape[0]
            
            # 确保切片索引在有效范围内
            self.slice_index = min(current_index, self.total_slices - 1)
            
            # 更新切片滑条
            self.slice_slider.setMaximum(self.total_slices - 1)
            self.slice_slider.setValue(self.slice_index)
            self.slice_label.setText(f"{self.slice_index + 1}/{self.total_slices}")
            
            # 清除掩码
            self.mask = None
            
            # 自动设置合适的窗宽窗位
            self._auto_adjust_window()
            
            # 更新显示
            self.update_display()
            
            # 记录日志
            logging.info(f"已设置裁剪图像，形状: {cropped_image.shape}")
            
        except Exception as e:
            logging.error(f"设置裁剪图像时出错: {str(e)}")
            logging.error(traceback.format_exc())
            QMessageBox.critical(None, "错误", f"设置裁剪图像时出错: {str(e)}")


class DicomSeriesSelectionDialog(QDialog):
    """DICOM序列选择对话框"""

    def __init__(self, series_info, parent=None):
        super().__init__(parent)
        self.series_info = series_info
        self.selected_series_id = None
        self.setup_ui()

    def setup_ui(self):
        """设置用户界面"""
        self.setWindowTitle("选择DICOM序列")
        self.setModal(True)
        self.resize(600, 400)

        layout = QVBoxLayout(self)

        # 说明标签
        info_label = QLabel("检测到多个DICOM序列，请选择要处理的序列：")
        info_label.setStyleSheet("font-weight: bold; margin-bottom: 10px;")
        layout.addWidget(info_label)

        # 创建表格显示序列信息
        self.table = QTableWidget()
        self.table.setColumnCount(4)
        self.table.setHorizontalHeaderLabels(["序列ID", "序列描述", "图像数量", "模态"])

        # 设置表格属性
        self.table.setSelectionBehavior(QTableWidget.SelectRows)
        self.table.setSelectionMode(QTableWidget.SingleSelection)
        self.table.setAlternatingRowColors(True)

        # 填充表格数据
        self.table.setRowCount(len(self.series_info))
        for row, (series_id, info) in enumerate(self.series_info.items()):
            # 序列ID
            self.table.setItem(row, 0, QTableWidgetItem(str(series_id)))

            # 序列描述
            description = info.get('series_description', '未知')
            if not description or description.strip() == '':
                description = f"序列 {row + 1}"
            self.table.setItem(row, 1, QTableWidgetItem(description))

            # 图像数量
            file_count = info.get('file_count', 0)
            self.table.setItem(row, 2, QTableWidgetItem(str(file_count)))

            # 模态
            modality = info.get('modality', '未知')
            self.table.setItem(row, 3, QTableWidgetItem(modality))

        # 调整列宽
        header = self.table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.Stretch)
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)

        layout.addWidget(self.table)

        # 按钮
        button_box = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)

        # 默认选择第一行
        if self.table.rowCount() > 0:
            self.table.selectRow(0)

    def accept(self):
        """确认选择"""
        current_row = self.table.currentRow()
        if current_row >= 0:
            series_id_item = self.table.item(current_row, 0)
            if series_id_item:
                self.selected_series_id = series_id_item.text()
        super().accept()

    def get_selected_series_id(self):
        """获取选择的序列ID"""
        return self.selected_series_id


class LungCTSegmentationGUI(QMainWindow):
    """肺部CT分割GUI主窗口"""

    def __init__(self):
        super().__init__()
        self.setWindowTitle("肺部CT分割工具")
        self.setGeometry(100, 100, 1200, 800)

        self.segmenter = None
        self.current_image = None
        self.current_image_path = None
        self.current_masks = {}
        self.current_results = {}

        self.init_ui()

    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)

        # 左侧控制面板
        control_panel = QWidget()
        control_layout = QVBoxLayout(control_panel)
        control_layout.setObjectName("control_layout")  # 设置对象名称

        # 模型设置
        model_group = QGroupBox("模型设置")
        model_layout = QVBoxLayout()

        # 模型类型
        model_type_layout = QHBoxLayout()
        model_type_layout.addWidget(QLabel("模型类型:"))
        self.model_type_combo = QComboBox()
        self.model_type_combo.addItems(["lungmask", "totalsegmentator"])
        self.model_type_combo.currentIndexChanged.connect(self.on_model_type_changed)
        model_type_layout.addWidget(self.model_type_combo)
        model_layout.addLayout(model_type_layout)

        # 模型变体
        model_variant_layout = QHBoxLayout()
        model_variant_layout.addWidget(QLabel("模型变体:"))
        self.model_variant_combo = QComboBox()
        # 初始化为lungmask的变体选项
        self.model_variant_combo.addItems(["R231", "LTRCLobes", "R231CovidWeb"])
        model_variant_layout.addWidget(self.model_variant_combo)
        model_layout.addLayout(model_variant_layout)

        # 初始化模型按钮
        self.init_model_btn = QPushButton("初始化模型")
        self.init_model_btn.clicked.connect(self.init_segmenter)
        model_layout.addWidget(self.init_model_btn)

        model_group.setLayout(model_layout)
        control_layout.addWidget(model_group)

        # 输入/输出设置
        io_group = QGroupBox("输入/输出设置")
        io_layout = QVBoxLayout()

        # 处理模式选择
        mode_layout = QHBoxLayout()
        self.single_mode_radio = QRadioButton("单文件处理")
        self.batch_mode_radio = QRadioButton("批量处理")
        self.single_mode_radio.setChecked(True)
        mode_layout.addWidget(self.single_mode_radio)
        mode_layout.addWidget(self.batch_mode_radio)
        io_layout.addLayout(mode_layout)

        # 输入路径
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入路径:"))
        self.input_path_edit = QLineEdit()
        input_layout.addWidget(self.input_path_edit)
        self.browse_input_btn = QPushButton("浏览...")
        self.browse_input_btn.clicked.connect(self.browse_input)
        input_layout.addWidget(self.browse_input_btn)
        
        # 添加DICOM目录选择按钮
        self.dicom_dir_btn = QPushButton("选择DICOM目录")
        self.dicom_dir_btn.clicked.connect(self._select_dicom_directory)
        self.dicom_dir_btn.setToolTip("直接选择包含DICOM文件的目录")
        input_layout.addWidget(self.dicom_dir_btn)
        
        io_layout.addLayout(input_layout)

        # 输出路径
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出路径:"))
        self.output_path_edit = QLineEdit()
        output_layout.addWidget(self.output_path_edit)
        self.browse_output_btn = QPushButton("浏览...")
        self.browse_output_btn.clicked.connect(self.browse_output)
        output_layout.addWidget(self.browse_output_btn)
        io_layout.addLayout(output_layout)

        # 输出格式选择
        output_format_layout = QHBoxLayout()
        output_format_layout.addWidget(QLabel("输出格式:"))
        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["NIFTI", "DICOM"])
        self.output_format_combo.setToolTip("选择结果保存格式：NIFTI (.nii.gz) 或 DICOM 系列")
        output_format_layout.addWidget(self.output_format_combo)
        io_layout.addLayout(output_format_layout)

        io_group.setLayout(io_layout)
        control_layout.addWidget(io_group)

        # 分割选项
        options_group = QGroupBox("分割选项")
        options_layout = QVBoxLayout()

        # 包含气道
        self.enable_airways_check = QCheckBox("包含气道分割")
        self.enable_airways_check.setChecked(True)
        options_layout.addWidget(self.enable_airways_check)

        # 包含肺叶
        self.enable_lobes_check = QCheckBox("包含肺叶分割")
        self.enable_lobes_check.setChecked(True)
        options_layout.addWidget(self.enable_lobes_check)
        
        # 包含血管
        self.enable_vessels_check = QCheckBox("包含血管分割")
        self.enable_vessels_check.setChecked(False)
        options_layout.addWidget(self.enable_vessels_check)

        # 平滑结果
        smooth_layout = QHBoxLayout()
        self.smooth_results_check = QCheckBox("平滑分割结果")
        self.smooth_results_check.setChecked(True)
        smooth_layout.addWidget(self.smooth_results_check)
        
        # 高斯平滑参数
        smooth_layout.addWidget(QLabel("高斯sigma:"))
        self.gaussian_sigma_spin = QDoubleSpinBox()
        self.gaussian_sigma_spin.setRange(0.1, 10.0)
        self.gaussian_sigma_spin.setSingleStep(0.1)
        self.gaussian_sigma_spin.setValue(2.0)
        self.gaussian_sigma_spin.setToolTip("高斯平滑的sigma值，值越大平滑效果越强")
        smooth_layout.addWidget(self.gaussian_sigma_spin)
        options_layout.addLayout(smooth_layout)

        # 收缩掩码
        shrink_layout = QHBoxLayout()
        self.shrink_masks_check = QCheckBox("收缩掩码边界")
        self.shrink_masks_check.setChecked(True)
        shrink_layout.addWidget(self.shrink_masks_check)
        
        # 形态学半径参数
        shrink_layout.addWidget(QLabel("形态学半径:"))
        self.morphological_radius_spin = QSpinBox()
        self.morphological_radius_spin.setRange(1, 5)
        self.morphological_radius_spin.setValue(1)
        self.morphological_radius_spin.setToolTip("形态学操作的半径，值越大收缩效果越强")
        shrink_layout.addWidget(self.morphological_radius_spin)
        options_layout.addLayout(shrink_layout)
        
        # 添加说明标签，说明肺部裁剪功能总是启用
        crop_info_label = QLabel("注意: 肺部裁剪功能已默认启用，将自动生成分割掩膜和裁剪后的肺部图像")
        crop_info_label.setStyleSheet("color: blue;")
        crop_info_label.setWordWrap(True)
        options_layout.addWidget(crop_info_label)

        options_group.setLayout(options_layout)
        control_layout.addWidget(options_group)

        # 运行按钮
        self.run_btn = QPushButton("运行分割")
        self.run_btn.clicked.connect(self.run_segmentation)
        control_layout.addWidget(self.run_btn)

        # 日志区域
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        control_layout.addWidget(log_group)

        # 添加控制面板到主布局
        main_layout.addWidget(control_panel, 1)

        # 右侧图像查看区域
        viewer_panel = QWidget()
        viewer_layout = QVBoxLayout(viewer_panel)

        # 图像查看器
        self.image_viewer = ImageViewer()
        viewer_layout.addWidget(self.image_viewer, 1)

        # 结果选择
        result_selection_layout = QHBoxLayout()
        result_selection_layout.addWidget(QLabel("显示结果:"))
        self.result_combo = QComboBox()
        self.result_combo.addItem("原始图像")
        self.result_combo.currentIndexChanged.connect(self.update_displayed_result)
        result_selection_layout.addWidget(self.result_combo)
        viewer_layout.addLayout(result_selection_layout)

        # 添加查看器面板到主布局
        main_layout.addWidget(viewer_panel, 2)
        
        # 添加状态栏和进度条
        self.statusBar().showMessage("就绪")
        
        self.status_label = QLabel("就绪")
        self.statusBar().addPermanentWidget(self.status_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setRange(0, 100)
        self.progress_bar.setTextVisible(True)
        self.progress_bar.setFixedWidth(200)
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)

    def init_segmenter(self):
        """初始化分割器"""
        try:
            model_type = self.model_type_combo.currentText()
            model_variant = self.model_variant_combo.currentText()

            self.log(f"初始化分割器: 模型类型={model_type}, 模型变体={model_variant}")

            self.segmenter = LungCTSegmenter(
                model_type=model_type,
                model_variant=model_variant
            )
            
            # 设置输出格式
            output_format = self.output_format_combo.currentText().lower()
            self.segmenter.set_output_format(output_format)
            
            # 根据模型类型和变体自动设置分割选项
            if model_type == "lungmask":
                if model_variant == "LTRCLobes":
                    # LTRCLobes支持肺叶分割
                    self.enable_lobes_check.setChecked(True)
                else:
                    # 其他Lungmask变体不支持肺叶分割
                    self.enable_lobes_check.setChecked(False)
                
                # Lungmask不支持气道和血管分割
                self.enable_airways_check.setChecked(False)
                self.enable_vessels_check.setChecked(False)
                
                # 禁用不支持的选项
                self.enable_airways_check.setEnabled(False)
                self.enable_vessels_check.setEnabled(False)
                self.enable_lobes_check.setEnabled(model_variant == "LTRCLobes")
                
            elif model_type == "totalsegmentator":
                # TotalSegmentator支持更多分割选项
                if model_variant == "lung_vessels":
                    self.enable_vessels_check.setChecked(True)
                
                # 启用所有选项
                self.enable_airways_check.setEnabled(True)
                self.enable_vessels_check.setEnabled(True)
                self.enable_lobes_check.setEnabled(True)

            self.log("分割器初始化成功")
            QMessageBox.information(self, "成功", "分割器初始化成功")

        except Exception as e:
            error_msg = f"初始化分割器失败: {str(e)}"
            self.log(error_msg, level="error")
            QMessageBox.critical(self, "错误", error_msg)

    def browse_input(self):
        """浏览输入路径"""
        if self.batch_mode_radio.isChecked():
            # 批处理模式
            options = QFileDialog.Options()
            options |= QFileDialog.DontUseNativeDialog  # 使用Qt对话框以支持多选
            
            # 创建自定义对话框，支持选择多个文件或目录
            dialog = QFileDialog(self, "选择输入文件或目录")
            dialog.setOptions(options)
            dialog.setFileMode(QFileDialog.Directory)  # 默认为目录选择
            
            # 添加下拉框选择模式
            layout = dialog.layout()
            if layout is not None:
                mode_widget = QWidget()
                mode_layout = QHBoxLayout(mode_widget)
                mode_layout.setContentsMargins(0, 0, 0, 0)
                
                mode_label = QLabel("选择模式:")
                mode_layout.addWidget(mode_label)
                
                mode_combo = QComboBox()
                mode_combo.addItems(["选择目录", "选择多个NIFTI文件", "选择多个DICOM目录", "扫描目录内容"])
                mode_layout.addWidget(mode_combo)
                
                # 根据选择的模式更改文件对话框的行为
                def change_file_mode():
                    mode = mode_combo.currentText()
                    if mode == "选择目录":
                        dialog.setFileMode(QFileDialog.Directory)
                    elif mode == "选择多个NIFTI文件":
                        dialog.setFileMode(QFileDialog.ExistingFiles)
                        dialog.setNameFilter("NIFTI文件 (*.nii *.nii.gz)")
                    elif mode == "选择多个DICOM目录":
                        dialog.setFileMode(QFileDialog.DirectoryOnly)
                        # 创建一个自定义按钮用于选择多个目录
                        if not hasattr(dialog, 'selected_dirs'):
                            dialog.selected_dirs = []
                            
                            # 添加自定义按钮和列表显示
                            custom_widget = QWidget()
                            custom_layout = QVBoxLayout(custom_widget)
                            
                            select_btn = QPushButton("添加当前目录")
                            select_btn.clicked.connect(lambda: self._add_selected_dir(dialog))
                            custom_layout.addWidget(select_btn)
                            
                            dialog.dir_list = QListWidget()
                            custom_layout.addWidget(dialog.dir_list)
                            
                            clear_btn = QPushButton("清除选择")
                            clear_btn.clicked.connect(lambda: self._clear_selected_dirs(dialog))
                            custom_layout.addWidget(clear_btn)
                            
                            # 将自定义控件添加到对话框
                            if isinstance(layout, QBoxLayout):
                                layout.addWidget(custom_widget)
                    elif mode == "扫描目录内容":
                        dialog.setFileMode(QFileDialog.Directory)
                
                mode_combo.currentTextChanged.connect(change_file_mode)
                
                # 将模式选择添加到对话框
                if isinstance(layout, QBoxLayout):
                    layout.insertWidget(1, mode_widget)
                else:
                    # 如果不是QBoxLayout，尝试添加到底部
                    try:
                        layout.addWidget(mode_widget, layout.rowCount(), 0, 1, layout.columnCount())
                    except:
                        pass  # 如果无法添加，忽略
                
            # 显示对话框
            if dialog.exec_():
                selected_files = dialog.selectedFiles()
                
                if not selected_files:
                    return
                
                mode = mode_combo.currentText() if 'mode_combo' in locals() else "选择目录"
                
                # 根据不同的模式处理选择的文件
                if mode == "选择多个NIFTI文件":
                    # 直接使用选择的多个NIFTI文件
                    self.batch_files = selected_files
                    self.input_path_edit.setText(f"已选择 {len(selected_files)} 个NIFTI文件")
                    self.display_batch_files()
                    
                elif mode == "选择多个DICOM目录":
                    # 使用已添加的多个DICOM目录
                    if hasattr(dialog, 'selected_dirs') and dialog.selected_dirs:
                        self.batch_files = dialog.selected_dirs.copy()
                        self.input_path_edit.setText(f"已选择 {len(self.batch_files)} 个DICOM目录")
                        self.display_batch_files()
                    else:
                        # 如果没有通过自定义按钮添加目录，则使用当前选择的目录
                        self.batch_files = selected_files
                        self.input_path_edit.setText(f"已选择 {len(selected_files)} 个DICOM目录")
                        self.display_batch_files()
                    
                elif mode == "扫描目录内容":
                    # 扫描选择的目录内的所有NIFTI文件和DICOM目录
                    if len(selected_files) == 1:
                        dir_path = selected_files[0]
                        self.scan_directory_for_medical_files(dir_path)
                        self.input_path_edit.setText(dir_path)
                        self.display_batch_files()
                    
                else:  # 选择目录
                    # 传统模式：使用选择的目录作为批处理输入
                    dir_path = selected_files[0]
                    self.input_path_edit.setText(dir_path)
                    self.batch_files = [dir_path]
        else:
            # 单文件模式
            # 创建一个自定义对话框，包含文件选择和DICOM目录按钮
            options = QFileDialog.Options()
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择医学影像文件", "", 
                "医学影像文件 (*.nii *.nii.gz *.dcm);;NIFTI文件 (*.nii *.nii.gz);;DICOM文件 (*.dcm);;所有文件 (*.*)",
                options=options
            )
            
            if file_path:
                self.input_path_edit.setText(file_path)
                self.load_and_display_image(file_path)
            else:
                # 如果用户取消了文件选择，提供选择DICOM目录的选项
                reply = QMessageBox.question(
                    self, "选择DICOM目录", 
                    "是否要选择DICOM目录？", 
                    QMessageBox.Yes | QMessageBox.No, QMessageBox.No
                )
                
                if reply == QMessageBox.Yes:
                    self._select_dicom_directory()
                    
    def _add_selected_dir(self, dialog):
        """添加当前选择的目录到多选列表"""
        selected_files = dialog.selectedFiles()
        if selected_files:
            dir_path = selected_files[0]
            if dir_path not in dialog.selected_dirs:
                dialog.selected_dirs.append(dir_path)
                dialog.dir_list.addItem(dir_path)
                self.log(f"已添加DICOM目录: {dir_path}")
    
    def _clear_selected_dirs(self, dialog):
        """清除已选择的目录列表"""
        dialog.selected_dirs = []
        dialog.dir_list.clear()
        self.log("已清除DICOM目录选择")

    def scan_directory_for_medical_files(self, directory):
        """扫描目录中的医学影像文件"""
        import os
        import glob
        
        self.batch_files = []
        
        # 查找所有NIFTI文件
        nifti_files = glob.glob(os.path.join(directory, "*.nii"))
        nifti_files.extend(glob.glob(os.path.join(directory, "*.nii.gz")))
        self.batch_files.extend(nifti_files)
        
        # 查找可能的DICOM目录 - 更强大的检测方法
        for item in os.listdir(directory):
            item_path = os.path.join(directory, item)
            if os.path.isdir(item_path):
                # 检查目录中是否有DICOM文件
                dicom_files = glob.glob(os.path.join(item_path, "*.dcm"))
                if dicom_files:
                    self.batch_files.append(item_path)
                    self.log(f"找到DICOM目录: {item_path} (包含 {len(dicom_files)} 个DICOM文件)")
                else:
                    # 检查是否有子目录包含DICOM文件
                    dicom_subdirs = []
                    for root, dirs, files in os.walk(item_path):
                        dicom_count = sum(1 for f in files if f.lower().endswith('.dcm'))
                        if dicom_count > 0:
                            dicom_subdirs.append((root, dicom_count))
                            self.log(f"在子目录中找到DICOM文件: {root} (包含 {dicom_count} 个DICOM文件)")
                    
                    # 如果找到了多个包含DICOM的子目录
                    if len(dicom_subdirs) > 0:
                        # 如果只有一个子目录包含DICOM，直接添加它
                        if len(dicom_subdirs) == 1:
                            self.batch_files.append(dicom_subdirs[0][0])
                        else:
                            # 如果有多个子目录包含DICOM，添加具有最多DICOM文件的那个
                            dicom_subdirs.sort(key=lambda x: x[1], reverse=True)
                            self.batch_files.append(dicom_subdirs[0][0])
                            self.log(f"选择了包含最多DICOM文件的子目录: {dicom_subdirs[0][0]} ({dicom_subdirs[0][1]} 个文件)")
        
        # 如果当前目录自身包含DICOM文件，也添加到列表中
        dicom_files_in_dir = glob.glob(os.path.join(directory, "*.dcm"))
        if dicom_files_in_dir:
            self.batch_files.append(directory)
            self.log(f"当前目录包含DICOM文件: {directory} (包含 {len(dicom_files_in_dir)} 个DICOM文件)")
        
        self.log(f"扫描到 {len(self.batch_files)} 个医学影像文件/目录")
        
        # 如果没有找到任何文件，显示警告
        if not self.batch_files:
            self.log("警告: 未找到任何NIFTI文件或DICOM目录", level="warning")
            QMessageBox.warning(self, "警告", "在选择的目录中未找到任何NIFTI文件或DICOM目录")

    def display_batch_files(self):
        """显示批处理文件列表"""
        if not hasattr(self, 'batch_files') or not self.batch_files:
            return
            
        # 如果文件列表区域不存在，创建一个
        if not hasattr(self, 'batch_files_list'):
            # 创建一个新的分组框
            batch_group = QGroupBox("批处理文件列表")
            batch_layout = QVBoxLayout()
            
            # 创建文件列表控件
            self.batch_files_list = QListWidget()
            self.batch_files_list.setSelectionMode(QListWidget.ExtendedSelection)
            batch_layout.addWidget(self.batch_files_list)
            
            # 添加操作按钮
            buttons_layout = QHBoxLayout()
            
            remove_btn = QPushButton("移除选中")
            remove_btn.clicked.connect(self.remove_selected_batch_files)
            buttons_layout.addWidget(remove_btn)
            
            clear_btn = QPushButton("清空列表")
            clear_btn.clicked.connect(self.clear_batch_files)
            buttons_layout.addWidget(clear_btn)
            
            batch_layout.addLayout(buttons_layout)
            
            # 设置分组框布局
            batch_group.setLayout(batch_layout)
            
            # 将分组框添加到控制面板
            control_layout = self.findChild(QVBoxLayout, "control_layout")
            if control_layout:
                # 在日志区域之前插入批处理文件列表
                for i in range(control_layout.count()):
                    widget = control_layout.itemAt(i).widget()
                    if widget and isinstance(widget, QGroupBox) and widget.title() == "日志":
                        control_layout.insertWidget(i, batch_group)
                        break
                else:
                    # 如果找不到日志区域，添加到末尾
                    control_layout.addWidget(batch_group)
        
        # 更新文件列表
        self.batch_files_list.clear()
        for file_path in self.batch_files:
            self.batch_files_list.addItem(file_path)
            
    def remove_selected_batch_files(self):
        """移除选中的批处理文件"""
        if not hasattr(self, 'batch_files_list'):
            return
            
        selected_items = self.batch_files_list.selectedItems()
        if not selected_items:
            return
            
        # 从批处理文件列表中移除选中的项
        for item in selected_items:
            file_path = item.text()
            if file_path in self.batch_files:
                self.batch_files.remove(file_path)
                
        # 更新显示
        self.display_batch_files()
        self.input_path_edit.setText(f"已选择 {len(self.batch_files)} 个文件/目录")
        
    def clear_batch_files(self):
        """清空批处理文件列表"""
        if hasattr(self, 'batch_files'):
            self.batch_files = []
            self.display_batch_files()
            self.input_path_edit.setText("")

    def _select_dicom_directory(self, parent_dialog=None):
        """选择DICOM目录"""
        if parent_dialog is not None and hasattr(parent_dialog, 'close'):
            parent_dialog.close()
            
        dir_path = QFileDialog.getExistingDirectory(self, "选择DICOM目录")
        if dir_path:
            self.input_path_edit.setText(dir_path)
            self.load_and_display_image(dir_path)

    def browse_output(self):
        """浏览输出路径"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_path_edit.setText(dir_path)

    def load_and_display_image(self, file_path, series_id=None):
        """加载并显示图像"""
        try:
            # 禁用UI组件，防止用户在加载过程中操作
            self.set_ui_enabled(False)

            self.log(f"加载图像: {file_path}")

            # 清除当前图像，释放内存
            if hasattr(self, 'current_image') and self.current_image is not None:
                del self.current_image
                self.current_image = None

            # 使用通用加载函数，支持NIFTI和DICOM
            from shared_utils.common.io_utils import load_image, get_dicom_series_info

            # 检查文件或目录是否存在
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"文件或目录不存在: {file_path}")

            # 如果是DICOM目录且没有指定序列ID，检查是否有多个序列
            if os.path.isdir(file_path) and series_id is None:
                try:
                    series_info = get_dicom_series_info(file_path)
                    if len(series_info) > 1:
                        # 弹出序列选择对话框
                        dialog = DicomSeriesSelectionDialog(series_info, self)
                        if dialog.exec_() == QDialog.Accepted:
                            series_id = dialog.get_selected_series_id()
                            if series_id:
                                self.log(f"用户选择序列: {series_id}")
                            else:
                                self.log("用户取消了序列选择")
                                self.set_ui_enabled(True)
                                return
                        else:
                            self.log("用户取消了序列选择")
                            self.set_ui_enabled(True)
                            return
                    else:
                        self.log(f"检测到单个DICOM序列，自动加载")
                except Exception as e:
                    self.log(f"检查DICOM序列时出错: {e}", level="warning")

            # 显示加载进度
            self.log("正在加载图像数据，请稍候...")
            QApplication.processEvents()  # 更新UI

            # 加载图像
            array, metadata = load_image(file_path, series_id)
            
            # 验证图像
            if array is None or array.size == 0:
                raise ValueError("加载的图像为空")
                
            if len(array.shape) != 3:
                raise ValueError(f"需要3D图像，但加载的图像维度为: {len(array.shape)}D")

            # 存储当前图像
            self.current_image = array
            self.current_image_path = file_path

            # 显示图像
            self.image_viewer.set_image(array)
            self.log(f"图像加载成功: 形状={array.shape}, 类型={array.dtype}, 范围=[{np.min(array):.1f}, {np.max(array):.1f}]")
            
            # 如果是DICOM，显示一些DICOM特有的元数据
            if 'patient_id' in metadata or 'patient_name' in metadata:
                patient_info = []
                if 'patient_id' in metadata and metadata['patient_id']:
                    patient_info.append(f"患者ID: {metadata['patient_id']}")
                if 'patient_name' in metadata and metadata['patient_name']:
                    patient_info.append(f"患者姓名: {metadata['patient_name']}")
                if 'study_date' in metadata and metadata['study_date']:
                    patient_info.append(f"检查日期: {metadata['study_date']}")
                if 'modality' in metadata and metadata['modality']:
                    patient_info.append(f"模态: {metadata['modality']}")
                if 'study_description' in metadata and metadata['study_description']:
                    patient_info.append(f"检查描述: {metadata['study_description']}")
                
                if patient_info:
                    self.log("DICOM元数据: " + ", ".join(patient_info))
                    
            # 显示图像尺寸和间距信息
            if 'spacing' in metadata:
                spacing = metadata['spacing']
                self.log(f"体素间距: [{spacing[0]:.2f}, {spacing[1]:.2f}, {spacing[2]:.2f}] mm")
                
            # 尝试进行垃圾回收，释放内存
            import gc
            gc.collect()

        except Exception as e:
            error_msg = f"加载图像失败: {str(e)}"
            self.log(error_msg, level="error")
            self.log(traceback.format_exc(), level="error")
            QMessageBox.critical(self, "错误", error_msg)
        finally:
            # 恢复UI组件
            self.set_ui_enabled(True)
            
    def set_ui_enabled(self, enabled):
        """启用或禁用UI组件"""
        # 禁用或启用主要控件
        self.browse_input_btn.setEnabled(enabled)
        self.browse_output_btn.setEnabled(enabled)
        if hasattr(self, 'run_btn'):
            self.run_btn.setEnabled(enabled)
        
        # 更新UI
        QApplication.processEvents()

    def run_segmentation(self):
        """运行分割"""
        try:
            # 检查是否已初始化分割器
            if self.segmenter is None:
                QMessageBox.warning(self, "警告", "请先初始化分割器！")
                return

            # 获取输出目录
            output_dir = self.output_path_edit.text()
            if not output_dir:
                QMessageBox.warning(self, "警告", "请选择输出目录！")
                return

            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)

            # 禁用UI
            self.set_ui_enabled(False)
            
            # 显示进度条
            self.progress_bar.setValue(0)
            self.progress_bar.setVisible(True)
            self.status_label.setText("准备分割...")
            
            # 获取分割参数
            params = {
                'model_type': self.model_type_combo.currentText().lower(),
                'model_variant': self.model_variant_combo.currentText(),
                'gaussian_sigma': float(self.gaussian_sigma_spin.value()),
                'morphological_radius': int(self.morphological_radius_spin.value()),
                'smooth_results': self.smooth_results_check.isChecked(),
                'shrink_masks': self.shrink_masks_check.isChecked(),
                'enable_lobes': self.enable_lobes_check.isChecked() and self.enable_lobes_check.isEnabled(),
                'enable_airways': self.enable_airways_check.isChecked() and self.enable_airways_check.isEnabled(),
                'enable_vessels': self.enable_vessels_check.isChecked() and self.enable_vessels_check.isEnabled(),
                'output_format': self.output_format_combo.currentText().lower()
            }
            
            batch_mode = self.batch_mode_radio.isChecked()
            
            if batch_mode:
                # 批处理模式
                if hasattr(self, 'batch_files') and self.batch_files:
                    # 使用批处理文件列表
                    input_files = self.batch_files
                    
                    # 创建批量分割线程
                    self.segmentation_thread = BatchSegmentationThread(
                        segmenter=self.segmenter,
                        input_files=input_files,
                        output_dir=output_dir,
                        params=params
                    )
                    
                    # 连接信号
                    self.segmentation_thread.progress.connect(self.update_status)
                    self.segmentation_thread.percent_progress.connect(self.update_progress)
                    self.segmentation_thread.file_progress.connect(self.update_file_progress)
                    self.segmentation_thread.error.connect(self.handle_segmentation_error)
                    self.segmentation_thread.finished.connect(self.handle_batch_segmentation_finished)
                    
                    # 启动线程
                    self.segmentation_thread.start()
                else:
                    # 传统方式：使用输入路径作为批处理目录
                    input_dir = self.input_path_edit.text()
                    if not input_dir or not os.path.isdir(input_dir):
                        QMessageBox.warning(self, "警告", "请选择有效的输入目录或添加批处理文件！")
                        self.set_ui_enabled(True)
                        self.progress_bar.setVisible(False)
                        return
                    
                    self.segmentation_thread = SegmentationThread(
                        segmenter=self.segmenter,
                        input_path=input_dir,
                        output_path=output_dir,
                        batch_mode=True,
                        params=params
                    )
                    
                    # 连接信号
                    self.segmentation_thread.progress.connect(self.update_status)
                    self.segmentation_thread.percent_progress.connect(self.update_progress)
                    self.segmentation_thread.error.connect(self.handle_segmentation_error)
                    self.segmentation_thread.finished.connect(self.handle_segmentation_finished)
                    
                    # 启动线程
                    self.segmentation_thread.start()
            else:
                # 单文件模式
                input_path = self.current_image_path
                if not input_path:
                    QMessageBox.warning(self, "警告", "请先加载CT图像！")
                    self.set_ui_enabled(True)
                    self.progress_bar.setVisible(False)
                    return
                
                # 检查是否为文件或DICOM目录
                if not (os.path.isfile(input_path) or 
                        (os.path.isdir(input_path) and 
                         any(f.endswith('.dcm') for f in os.listdir(input_path)))):
                    QMessageBox.warning(self, "警告", "无效的输入路径！")
                    self.set_ui_enabled(True)
                    self.progress_bar.setVisible(False)
                    return
                
                # 构建输出路径
                base_name = os.path.basename(input_path)
                if os.path.isdir(input_path):
                    # DICOM目录
                    output_path = os.path.join(output_dir, f"{base_name}_segmented")
                else:
                    # NIFTI文件
                    base_name = os.path.splitext(base_name)[0]
                    if base_name.endswith('.nii'):
                        base_name = os.path.splitext(base_name)[0]
                    output_path = os.path.join(output_dir, f"{base_name}_segmented")
                
                self.segmentation_thread = SegmentationThread(
                    segmenter=self.segmenter,
                    input_path=input_path,
                    output_path=output_path,
                    batch_mode=False,
                    params=params
                )
                
                # 连接信号
                self.segmentation_thread.progress.connect(self.update_status)
                self.segmentation_thread.percent_progress.connect(self.update_progress)
                self.segmentation_thread.error.connect(self.handle_segmentation_error)
                self.segmentation_thread.finished.connect(self.handle_segmentation_finished)
                
                # 启动线程
                self.segmentation_thread.start()
            
        except Exception as e:
            logging.error(f"启动分割时出错: {str(e)}")
            logging.error(traceback.format_exc())
            QMessageBox.critical(self, "错误", f"启动分割时出错: {str(e)}")
            self.set_ui_enabled(True)
            self.progress_bar.setVisible(False)
            
    def update_file_progress(self, current_file, total_files, file_name):
        """更新当前处理的文件进度"""
        self.status_label.setText(f"正在处理 {current_file}/{total_files}: {file_name}")
        
    def handle_batch_segmentation_finished(self, results):
        """处理批量分割完成"""
        # 启用UI
        self.set_ui_enabled(True)
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 更新状态
        self.status_label.setText("批量分割完成")
        
        # 显示完成消息
        successful = results.get('successful', 0)
        failed = results.get('failed', 0)
        total = successful + failed
        
        QMessageBox.information(
            self, 
            "批量分割完成", 
            f"批量分割任务完成！\n成功: {successful}/{total}\n失败: {failed}/{total}"
        )

    def update_status(self, message):
        """更新状态栏消息"""
        self.status_label.setText(message)
        logging.info(message)
        
    def update_progress(self, percent):
        """更新进度条"""
        self.progress_bar.setValue(percent)
        
    def handle_segmentation_error(self, error_msg):
        """处理分割错误"""
        QMessageBox.critical(self, "分割错误", error_msg)
        self.set_ui_enabled(True)
        self.progress_bar.setVisible(False)
        self.status_label.setText("分割失败")
        
    def handle_segmentation_finished(self, results):
        """处理分割完成"""
        # 启用UI
        self.set_ui_enabled(True)
        
        # 隐藏进度条
        self.progress_bar.setVisible(False)
        
        # 更新状态
        self.status_label.setText("分割完成")
        
        # 如果是单文件模式且有结果，则显示结果
        if not self.batch_mode_radio.isChecked() and results:
            try:
                # 存储所有结果
                self.current_results = results
                
                # 更新当前掩码字典
                self.current_masks = {}
                for name, data in results.items():
                    if isinstance(data, np.ndarray) and data.ndim == 3:
                        # 只将掩码类型的数据添加到掩码字典
                        if name != "cropped_lung" and (data.dtype == bool or data.dtype == np.uint8):
                            self.current_masks[name] = data
                
                # 更新结果下拉菜单
                self.result_combo.clear()
                self.result_combo.addItem("原始图像")
                
                # 添加掩码选项
                for name in self.current_masks.keys():
                    self.result_combo.addItem(name)
                
                # 添加裁剪后的肺部图像选项
                if "cropped_lung" in results:
                    self.result_combo.addItem("cropped_lung")
                
                # 显示分割结果
                if "lung_mask" in results:
                    self.display_segmentation_results(results)
                    QMessageBox.information(self, "成功", "肺部分割完成！结果已显示并保存。")
                else:
                    QMessageBox.information(self, "成功", "分割完成！结果已保存。")
            except Exception as e:
                logging.error(f"显示分割结果时出错: {str(e)}")
                logging.error(traceback.format_exc())
                QMessageBox.warning(self, "警告", f"无法显示分割结果: {str(e)}")
        else:
            # 批处理模式
            QMessageBox.information(self, "成功", "批量分割任务完成！")
            
    def display_segmentation_results(self, results):
        """显示分割结果"""
        if not results or "lung_mask" not in results:
            return
            
        # 设置分割结果
        self.image_viewer.set_mask(results["lung_mask"])
        
        # 记录日志
        self.log("已显示分割结果")

    def log(self, message, level="info"):
        """添加日志消息"""
        # 获取当前时间
        from datetime import datetime
        timestamp = datetime.now().strftime("%H:%M:%S")

        # 设置不同级别的颜色
        color = "black"
        if level == "error":
            color = "red"
        elif level == "warning":
            color = "orange"

        # 添加带颜色的消息
        self.log_text.append(f'<span style="color:{color}">[{timestamp}] {message}</span>')

        # 滚动到底部
        self.log_text.verticalScrollBar().setValue(self.log_text.verticalScrollBar().maximum())

        # 同时输出到控制台
        print(f"[{level.upper()}] {message}")

    def update_displayed_result(self):
        """更新显示的分割结果"""
        if self.current_image_path is None:
            return
            
        selected = self.result_combo.currentText()
        
        if selected == "原始图像":
            # 只显示原始图像，不显示掩码
            self.image_viewer.set_image(self.current_image)
            self.image_viewer.set_mask(None)
            self.log("显示原始图像")
        elif selected == "cropped_lung" and "cropped_lung" in self.current_results:
            # 显示裁剪后的肺部图像
            self.image_viewer.set_cropped_image(self.current_results["cropped_lung"])
            self.log("显示裁剪后的肺部图像")
        elif selected in self.current_masks:
            # 显示选中的掩码
            self.image_viewer.set_image(self.current_image)
            self.image_viewer.set_mask(self.current_masks[selected])
            self.log(f"显示掩码: {selected}")
        else:
            self.log(f"未找到掩码或结果: {selected}", level="warning")

    def on_model_type_changed(self):
        """模型类型改变时的回调"""
        # 当模型类型改变时，清空模型变体选项
        self.model_variant_combo.clear()
        
        # 根据选择的模型类型添加相应的变体选项
        model_type = self.model_type_combo.currentText()
        if model_type == "lungmask":
            # Lungmask模型的变体
            self.model_variant_combo.addItems(["R231", "LTRCLobes", "R231CovidWeb"])
            self.model_variant_combo.setToolTip("R231: 基础肺部分割\nLTRCLobes: 肺叶分割\nR231CovidWeb: 针对COVID-19优化")
        else:  # totalsegmentator
            # TotalSegmentator模型的任务类型
            self.model_variant_combo.addItems(["lung_basic", "lung_vessels", "total"])
            self.model_variant_combo.setToolTip("lung_basic: 基础肺部分割\nlung_vessels: 包含肺血管分割\ntotal: 全身多器官分割")
        
        # 记录日志
        self.log(f"已切换到{model_type}模型，请选择合适的变体")
        
        # 如果已经初始化了分割器，提醒用户需要重新初始化
        if self.segmenter is not None:
            self.log("模型类型已更改，请重新初始化模型", level="warning")


class BatchSegmentationThread(QThread):
    """批量分割线程，用于处理多个文件"""
    finished = pyqtSignal(dict)    # 完成信号，传递结果统计
    error = pyqtSignal(str)        # 错误信号，传递错误消息
    progress = pyqtSignal(str)     # 进度信号，传递进度消息
    percent_progress = pyqtSignal(int)  # 百分比进度信号
    file_progress = pyqtSignal(int, int, str)  # 文件进度信号 (当前文件索引, 总文件数, 文件名)
    
    def __init__(self, segmenter, input_files, output_dir, params=None):
        super().__init__()
        self.segmenter = segmenter
        self.input_files = input_files
        self.output_dir = output_dir
        self.params = params if params else {}
        
        # 设置进度回调
        self.segmenter.set_progress_callback(self._progress_callback)
        
        # 文件处理计数
        self.current_file_index = 0
        self.total_files = len(input_files)
        
    def _progress_callback(self, message, percent=None):
        """处理来自分割器的进度更新"""
        self.progress.emit(message)
        if percent is not None:
            # 将单个文件的进度转换为整体进度
            file_weight = 1.0 / self.total_files
            overall_percent = int((self.current_file_index + percent / 100) * file_weight * 100)
            self.percent_progress.emit(overall_percent)
    
    def run(self):
        """执行批量分割操作"""
        try:
            logging.info(f"开始批量分割任务，共 {len(self.input_files)} 个文件")
            self.progress.emit(f"开始批量分割，共 {len(self.input_files)} 个文件...")
            
            # 应用参数
            for key, value in self.params.items():
                setattr(self.segmenter, key, value)
                
            # 处理结果统计
            results_stats = {
                'successful': 0,
                'failed': 0
            }
            
            # 遍历处理每个文件
            for i, input_path in enumerate(self.input_files):
                self.current_file_index = i
                file_name = os.path.basename(input_path)
                
                try:
                    # 更新文件进度
                    self.file_progress.emit(i + 1, self.total_files, file_name)
                    self.progress.emit(f"处理文件 {i + 1}/{self.total_files}: {file_name}")
                    
                    # 检查输入路径是否存在
                    if not os.path.exists(input_path):
                        raise FileNotFoundError(f"文件或目录不存在: {input_path}")
                    
                    # 检查DICOM目录是否有效
                    if os.path.isdir(input_path):
                        # 检查目录中是否有DICOM文件
                        dicom_files = []
                        for root, dirs, files in os.walk(input_path):
                            dicom_files.extend([os.path.join(root, f) for f in files if f.lower().endswith('.dcm')])
                        
                        if not dicom_files:
                            raise ValueError(f"目录中未找到DICOM文件: {input_path}")
                        
                        logging.info(f"找到 {len(dicom_files)} 个DICOM文件在 {input_path}")
                        self.progress.emit(f"找到 {len(dicom_files)} 个DICOM文件在 {file_name}")
                    
                    # 构建输出路径
                    if os.path.isdir(input_path):
                        # DICOM目录
                        output_path = os.path.join(self.output_dir, f"{file_name}_segmented")
                    else:
                        # NIFTI文件
                        base_name = os.path.splitext(file_name)[0]
                        if base_name.endswith('.nii'):
                            base_name = os.path.splitext(base_name)[0]
                        output_path = os.path.join(self.output_dir, f"{base_name}_segmented")
                    
                    # 确保输出目录存在
                    os.makedirs(os.path.dirname(output_path), exist_ok=True)
                    
                    # 执行分割
                    self.progress.emit(f"正在分割 {file_name}...")
                    results = self.segmenter.segment_from_file(input_path, output_path)
                    
                    # 检查结果
                    if results and isinstance(results, dict):
                        if "lung_mask" in results and np.count_nonzero(results["lung_mask"]) > 0:
                            self.progress.emit(f"成功分割 {file_name}，肺部像素: {np.count_nonzero(results['lung_mask'])}")
                        else:
                            self.progress.emit(f"警告: {file_name} 的肺部掩码可能为空")
                    
                    # 更新统计
                    results_stats['successful'] += 1
                    logging.info(f"文件 {file_name} 处理成功")
                    
                except Exception as e:
                    # 记录错误但继续处理下一个文件
                    results_stats['failed'] += 1
                    error_str = str(e)

                    # 提供更友好的错误信息
                    if "nifti library failed to write image" in error_str:
                        error_msg = f"处理文件 {file_name} 时出错: NIfTI文件保存失败，可能是由于文件路径包含特殊字符。建议使用英文路径或检查磁盘空间。"
                    elif "SimpleITK" in error_str and "WriteImage" in error_str:
                        error_msg = f"处理文件 {file_name} 时出错: 图像保存失败，请检查输出路径权限和磁盘空间。"
                    else:
                        error_msg = f"处理文件 {file_name} 时出错: {error_str}"

                    logging.error(error_msg)
                    logging.error(traceback.format_exc())
                    self.error.emit(error_msg)
                    self.progress.emit(f"处理 {file_name} 失败: {str(e)}")
                
                # 更新总体进度
                overall_percent = int((i + 1) / self.total_files * 100)
                self.percent_progress.emit(overall_percent)
            
            logging.info(f"批量分割任务完成，成功: {results_stats['successful']}, 失败: {results_stats['failed']}")
            self.progress.emit(f"批量分割完成，成功: {results_stats['successful']}/{self.total_files}")
            
            # 发送完成信号
            self.finished.emit(results_stats)
            
        except Exception as e:
            error_msg = f"批量分割过程中发生错误: {str(e)}"
            logging.error(error_msg)
            logging.error(traceback.format_exc())
            self.error.emit(error_msg)
            self.finished.emit({'successful': 0, 'failed': 0})  # 发送空结果


def main():
    app = QApplication(sys.argv)
    window = LungCTSegmentationGUI()
    window.show()
    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
