#!/usr/bin/env python3
"""
CT区域选框分割工具快速测试脚本

快速验证核心功能是否正常工作
"""

import sys
import os
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

def test_imports():
    """测试模块导入"""
    print("测试模块导入...")
    try:
        from core.region_cropper import CTRegionCropper
        from core.dicom_handler import DICOMHandler
        from core.metadata_manager import MetadataManager
        from utils.image_utils import ImageUtils
        from utils.file_utils import FileUtils
        print("✅ 所有核心模块导入成功")
        return True
    except Exception as e:
        print(f"❌ 模块导入失败: {e}")
        return False

def test_basic_functionality():
    """测试基本功能"""
    print("\n测试基本功能...")
    try:
        from core.region_cropper import CTRegionCropper
        
        # 创建分割器
        cropper = CTRegionCropper()
        
        # 创建模拟图像数据
        test_image = np.random.randint(-1000, 1000, (10, 256, 256), dtype=np.int16)
        cropper.image_array = test_image
        
        # 测试设置裁剪区域
        success = cropper.set_crop_region(50, 50, 100, 100)
        if not success:
            print("❌ 设置裁剪区域失败")
            return False
        
        # 测试裁剪操作
        success = cropper.crop_all_slices()
        if not success:
            print("❌ 裁剪操作失败")
            return False
        
        # 验证结果
        if cropper.cropped_array is None:
            print("❌ 裁剪结果为空")
            return False
        
        expected_shape = (10, 100, 100)
        if cropper.cropped_array.shape != expected_shape:
            print(f"❌ 裁剪结果形状错误: {cropper.cropped_array.shape} != {expected_shape}")
            return False
        
        print("✅ 基本功能测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        return False

def test_image_utils():
    """测试图像工具"""
    print("\n测试图像工具...")
    try:
        from utils.image_utils import ImageUtils
        
        # 创建测试图像
        test_image = np.random.randint(-1000, 1000, (256, 256), dtype=np.int16)
        
        # 测试图像标准化
        normalized = ImageUtils.normalize_image(test_image)
        if normalized.dtype != np.uint8:
            print("❌ 图像标准化类型错误")
            return False
        
        # 测试窗宽窗位计算
        window_center, window_width = ImageUtils.get_optimal_window(test_image)
        if not isinstance(window_center, float) or not isinstance(window_width, float):
            print("❌ 窗宽窗位计算类型错误")
            return False
        
        # 测试图像裁剪
        cropped = ImageUtils.crop_image(test_image, 50, 50, 100, 100)
        if cropped.shape != (100, 100):
            print(f"❌ 图像裁剪结果形状错误: {cropped.shape}")
            return False
        
        print("✅ 图像工具测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 图像工具测试失败: {e}")
        return False

def test_file_utils():
    """测试文件工具"""
    print("\n测试文件工具...")
    try:
        from utils.file_utils import FileUtils
        import tempfile
        
        # 测试路径验证
        temp_dir = tempfile.mkdtemp()
        is_valid, error_msg = FileUtils.validate_path(temp_dir, must_exist=True, must_be_dir=True)
        if not is_valid:
            print(f"❌ 路径验证失败: {error_msg}")
            return False
        
        # 测试文件大小格式化
        size_str = FileUtils.format_file_size(1024)
        if size_str != "1.0 KB":
            print(f"❌ 文件大小格式化错误: {size_str}")
            return False
        
        print("✅ 文件工具测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 文件工具测试失败: {e}")
        return False

def test_gui_imports():
    """测试GUI模块导入"""
    print("\n测试GUI模块导入...")
    try:
        from gui.main_window import CTRegionCropperApp
        from gui.image_viewer import ImageViewer
        from gui.region_selector import RegionSelector
        print("✅ GUI模块导入成功")
        return True
    except ImportError as e:
        print(f"⚠️  GUI模块导入失败 (可能缺少PyQt5): {e}")
        return False
    except Exception as e:
        print(f"❌ GUI模块导入失败: {e}")
        return False

def main():
    """主函数"""
    print("🏥 CT区域选框分割工具 - 快速功能测试")
    print("=" * 50)
    
    tests = [
        ("模块导入", test_imports),
        ("基本功能", test_basic_functionality),
        ("图像工具", test_image_utils),
        ("文件工具", test_file_utils),
        ("GUI模块", test_gui_imports),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔍 {test_name}测试:")
        if test_func():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！CT区域选框分割工具功能正常。")
        print("\n📋 下一步:")
        print("1. 运行 'python run_gui.py' 启动图形界面")
        print("2. 运行 'python run_tests.py' 执行完整测试")
        print("3. 查看 '使用说明.md' 了解详细使用方法")
        return True
    else:
        print("⚠️  部分测试失败，请检查依赖包安装和代码。")
        print("\n🔧 建议:")
        print("1. 运行 'pip install -r requirements.txt' 安装依赖")
        print("2. 检查Python版本 (需要3.7+)")
        print("3. 查看错误信息并修复问题")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
