#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
清理并修复DICOM文件夹
解决文件名混乱问题，统一为4位数命名

作者: AI Assistant
日期: 2025-01-17
"""

import os
import sys
import pydicom
import shutil
from pathlib import Path
import tempfile
from typing import List, <PERSON><PERSON>


def get_dicom_files(directory: str) -> List[str]:
    """获取目录中的所有DICOM文件"""
    dicom_files = []
    directory = Path(directory)
    
    for file_path in directory.iterdir():
        if file_path.is_file():
            try:
                pydicom.dcmread(str(file_path), stop_before_pixels=True)
                dicom_files.append(str(file_path))
            except:
                continue
    
    return dicom_files


def extract_instance_info(dicom_files: List[str]) -> List[Tuple[str, int, float, str]]:
    """提取DICOM文件的实例信息"""
    file_info = []
    
    for file_path in dicom_files:
        try:
            ds = pydicom.dcmread(file_path, stop_before_pixels=True)
            
            instance_number = getattr(ds, 'InstanceNumber', len(file_info) + 1)
            
            # 尝试多种方式获取切片位置
            slice_location = None
            
            if hasattr(ds, 'SliceLocation') and ds.SliceLocation is not None:
                slice_location = float(ds.SliceLocation)
            elif hasattr(ds, 'ImagePositionPatient') and ds.ImagePositionPatient is not None:
                if len(ds.ImagePositionPatient) >= 3:
                    slice_location = float(ds.ImagePositionPatient[2])
            
            # 如果没有位置信息，使用Instance Number
            if slice_location is None:
                slice_location = float(instance_number)
            
            original_filename = os.path.basename(file_path)
            file_info.append((file_path, int(instance_number), slice_location, original_filename))
            
        except Exception as e:
            print(f"错误: 无法读取文件 {file_path}: {e}")
            continue
    
    return file_info


def clean_and_fix_dicom_folder(input_dir: str):
    """清理并修复DICOM文件夹"""
    
    print(f"正在处理目录: {input_dir}")
    
    # 获取所有DICOM文件
    dicom_files = get_dicom_files(input_dir)
    if not dicom_files:
        print("未找到DICOM文件！")
        return
    
    print(f"找到 {len(dicom_files)} 个DICOM文件")
    
    # 提取实例信息
    file_info = extract_instance_info(dicom_files)
    if not file_info:
        print("无法读取任何DICOM文件信息！")
        return
    
    # 按Instance Number排序（因为缺少位置信息）
    file_info.sort(key=lambda x: (x[2], x[1]))
    
    print(f"排序后的文件信息:")
    for i in range(min(10, len(file_info))):
        old_name = file_info[i][3]
        new_name = f"{i+1:04d}.dcm"
        print(f"  位置{i+1}: {old_name} -> {new_name} (Instance: {file_info[i][1]})")
    if len(file_info) > 10:
        print(f"  ... 还有 {len(file_info)-10} 个文件")
    
    # 创建备份
    backup_dir = Path(input_dir).parent / f"{Path(input_dir).name}_backup_clean"
    if backup_dir.exists():
        print(f"删除旧备份: {backup_dir}")
        shutil.rmtree(backup_dir)
    
    print(f"创建备份到: {backup_dir}")
    shutil.copytree(input_dir, backup_dir)
    
    # 使用临时目录处理文件
    with tempfile.TemporaryDirectory() as temp_dir:
        print("正在重新编号和重命名文件...")
        
        temp_files = []
        for i, (file_path, old_instance, slice_loc, orig_name) in enumerate(file_info):
            new_instance_number = i + 1
            
            try:
                # 读取DICOM文件
                ds = pydicom.dcmread(file_path)
                
                # 更新Instance Number
                ds.InstanceNumber = new_instance_number

                # 更新Slice Location确保连续性
                ds.SliceLocation = float(i)

                # 确保完整的几何信息
                # 1. Image Position Patient (必须有完整的X,Y,Z坐标)
                if not hasattr(ds, 'ImagePositionPatient') or ds.ImagePositionPatient is None:
                    ds.ImagePositionPatient = [0.0, 0.0, float(i)]
                else:
                    # 确保是3个元素的列表
                    if len(ds.ImagePositionPatient) < 3:
                        ds.ImagePositionPatient = [0.0, 0.0, float(i)]
                    else:
                        ds.ImagePositionPatient[2] = float(i)

                # 2. Image Orientation Patient (定义图像方向)
                if not hasattr(ds, 'ImageOrientationPatient') or ds.ImageOrientationPatient is None:
                    # 标准轴向图像方向：行方向(1,0,0)，列方向(0,1,0)
                    ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]

                # 3. Pixel Spacing (像素间距)
                if not hasattr(ds, 'PixelSpacing') or ds.PixelSpacing is None:
                    ds.PixelSpacing = [1.0, 1.0]  # 默认1mm x 1mm

                # 4. Slice Thickness (切片厚度)
                if not hasattr(ds, 'SliceThickness') or ds.SliceThickness is None:
                    ds.SliceThickness = 1.0  # 默认1mm

                # 5. Spacing Between Slices (切片间距)
                if not hasattr(ds, 'SpacingBetweenSlices') or ds.SpacingBetweenSlices is None:
                    ds.SpacingBetweenSlices = 1.0  # 默认1mm
                
                # 统一使用4位数命名
                new_filename = f"{new_instance_number:04d}.dcm"
                
                temp_file = os.path.join(temp_dir, new_filename)
                ds.save_as(temp_file, write_like_original=False)
                temp_files.append((temp_file, file_path, new_filename))
                
                if (i + 1) % 50 == 0 or i == len(file_info) - 1:
                    print(f"  处理进度: {i+1}/{len(file_info)}")
                
            except Exception as e:
                print(f"错误: 处理文件 {file_path} 时出错: {e}")
                continue
        
        # 清空原目录
        print("正在清空原目录...")
        for file_path in Path(input_dir).iterdir():
            if file_path.is_file():
                file_path.unlink()
        
        # 保存新文件
        print("正在保存新文件...")
        for i, (temp_file, _, new_filename) in enumerate(temp_files):
            final_path = os.path.join(input_dir, new_filename)
            shutil.move(temp_file, final_path)
            
            if (i + 1) % 50 == 0 or i == len(temp_files) - 1:
                print(f"  保存进度: {i+1}/{len(temp_files)}")
    
    print(f"\n✅ 处理完成！")
    print(f"处理了 {len(temp_files)} 个文件")
    print(f"文件已重命名为: 0001.dcm 到 {len(temp_files):04d}.dcm")
    print(f"原始文件已备份到: {backup_dir}")
    
    # 验证结果
    final_files = sorted([f for f in os.listdir(input_dir) if f.endswith('.dcm')])
    print(f"\n验证结果:")
    print(f"最终文件数量: {len(final_files)}")
    if final_files:
        print(f"文件名范围: {final_files[0]} 到 {final_files[-1]}")
        
        # 检查是否有重复或缺失
        expected_files = [f"{i:04d}.dcm" for i in range(1, len(final_files) + 1)]
        if final_files == expected_files:
            print("✅ 文件命名完全正确！")
        else:
            print("⚠️ 文件命名可能有问题")


def main():
    if len(sys.argv) != 2:
        print("用法: python clean_and_fix_dicom.py <DICOM目录路径>")
        print("示例: python clean_and_fix_dicom.py \"D:\\path\\to\\dicom\\folder\"")
        sys.exit(1)
    
    input_dir = sys.argv[1]
    
    if not os.path.exists(input_dir):
        print(f"错误: 目录不存在: {input_dir}")
        sys.exit(1)
    
    if not os.path.isdir(input_dir):
        print(f"错误: 不是目录: {input_dir}")
        sys.exit(1)
    
    print("=" * 60)
    print("DICOM文件夹清理和修复工具")
    print("=" * 60)
    print("此工具将:")
    print("• 清理混乱的文件名")
    print("• 统一重命名为4位数格式 (0001.dcm - XXXX.dcm)")
    print("• 重新编号Instance Number为连续数字")
    print("• 创建备份以防万一")
    print("=" * 60)
    
    confirm = input(f"确认要处理目录 '{input_dir}' 吗？(y/N): ").strip().lower()
    if confirm != 'y':
        print("操作取消")
        sys.exit(0)
    
    try:
        clean_and_fix_dicom_folder(input_dir)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
