import unittest
import numpy as np
import os
import sys
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from lung_ct_segmenter.core.segmenter import LungCTSegmenter
from lung_ct_segmenter.utils.io_utils import load_nifti, save_nifti

class TestLungCTSegmenter(unittest.TestCase):
    def setUp(self):
        """设置测试环境"""
        self.test_input_path = "test_data/test_ct.nii.gz"
        self.test_output_path = "test_data/output"
        self.model_type = "lungmask"
        self.model_variant = "R231"
        
        # 创建测试数据目录
        os.makedirs("test_data", exist_ok=True)
        
        # 创建一个简单的测试图像（如果没有真实数据）
        if not os.path.exists(self.test_input_path):
            test_image = np.zeros((64, 64, 64), dtype=np.float32)
            test_image[16:48, 16:48, 16:48] = 1.0  # 模拟一个立方体结构
            save_nifti(test_image, self.test_input_path)
    
    def test_segmenter_initialization(self):
        """测试分割器初始化"""
        segmenter = LungCTSegmenter(model_type=self.model_type, model_variant=self.model_variant)
        self.assertIsNotNone(segmenter.model)
        self.assertEqual(segmenter.model_type, self.model_type)
        self.assertEqual(segmenter.model_variant, self.model_variant)
    
    def test_segmentation_from_file(self):
        """测试从文件进行分割"""
        segmenter = LungCTSegmenter(model_type=self.model_type, model_variant=self.model_variant)
        results = segmenter.segment_from_file(self.test_input_path, self.test_output_path)
        self.assertIsInstance(results, dict)
        self.assertTrue(len(results) > 0)
        for key, mask in results.items():
            self.assertIsInstance(mask, np.ndarray)
            self.assertEqual(mask.shape, load_nifti(self.test_input_path)[0].shape)
    
    def test_batch_processing(self):
        """测试批量处理功能"""
        segmenter = LungCTSegmenter(model_type=self.model_type, model_variant=self.model_variant)
        input_dir = "test_data"
        output_dir = "test_data/batch_output"
        processed_files = segmenter.batch_process(input_dir, output_dir)
        self.assertGreaterEqual(len(processed_files), 1)
        self.assertTrue(self.test_input_path in processed_files)

if __name__ == '__main__':
    unittest.main()