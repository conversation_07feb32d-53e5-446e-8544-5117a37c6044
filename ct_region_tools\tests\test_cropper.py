"""
CT区域分割器测试模块

测试CT区域选框分割工具的各项功能
"""

import unittest
import tempfile
import shutil
import os
import sys
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.append(str(project_root))

from core.region_cropper import CTRegionCropper
from core.dicom_handler import DICOMHandler
from core.metadata_manager import MetadataManager
from utils.image_utils import ImageUtils
from utils.file_utils import FileUtils


class TestCTRegionCropper(unittest.TestCase):
    """CT区域分割器测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.cropper = CTRegionCropper()
        self.temp_dir = tempfile.mkdtemp()
        
        # 创建模拟图像数据
        self.test_image_array = np.random.randint(
            -1000, 1000, size=(10, 256, 256), dtype=np.int16
        )
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_set_crop_region_valid(self):
        """测试设置有效的裁剪区域"""
        # 模拟加载图像
        self.cropper.image_array = self.test_image_array
        
        # 测试有效区域
        success = self.cropper.set_crop_region(50, 50, 100, 100)
        self.assertTrue(success)
        self.assertEqual(self.cropper.crop_region, (50, 50, 100, 100))
    
    def test_set_crop_region_invalid(self):
        """测试设置无效的裁剪区域"""
        # 模拟加载图像
        self.cropper.image_array = self.test_image_array
        
        # 测试超出边界的区域
        success = self.cropper.set_crop_region(200, 200, 200, 200)
        self.assertFalse(success)
        
        # 测试负坐标
        success = self.cropper.set_crop_region(-10, 0, 100, 100)
        self.assertFalse(success)
        
        # 测试零尺寸
        success = self.cropper.set_crop_region(0, 0, 0, 100)
        self.assertFalse(success)
    
    def test_crop_all_slices(self):
        """测试裁剪所有切片"""
        # 模拟加载图像
        self.cropper.image_array = self.test_image_array
        
        # 设置裁剪区域
        self.cropper.set_crop_region(50, 50, 100, 100)
        
        # 执行裁剪
        success = self.cropper.crop_all_slices()
        self.assertTrue(success)
        
        # 验证裁剪结果
        self.assertIsNotNone(self.cropper.cropped_array)
        expected_shape = (10, 100, 100)
        self.assertEqual(self.cropper.cropped_array.shape, expected_shape)
    
    def test_crop_single_slice(self):
        """测试裁剪单个切片"""
        # 模拟加载图像
        self.cropper.image_array = self.test_image_array
        
        # 设置裁剪区域
        self.cropper.set_crop_region(50, 50, 100, 100)
        
        # 裁剪单个切片
        cropped_slice = self.cropper.crop_single_slice(0)
        self.assertIsNotNone(cropped_slice)
        self.assertEqual(cropped_slice.shape, (100, 100))
    
    def test_validate_crop_region(self):
        """测试裁剪区域验证"""
        # 模拟加载图像
        self.cropper.image_array = self.test_image_array
        
        # 测试有效区域
        is_valid, error_msg = self.cropper.validate_crop_region(50, 50, 100, 100)
        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")
        
        # 测试无效区域
        is_valid, error_msg = self.cropper.validate_crop_region(-10, 0, 100, 100)
        self.assertFalse(is_valid)
        self.assertNotEqual(error_msg, "")
    
    def test_get_image_info(self):
        """测试获取图像信息"""
        # 模拟加载图像
        self.cropper.image_array = self.test_image_array
        
        info = self.cropper.get_image_info()
        self.assertIn('shape', info)
        self.assertIn('dtype', info)
        self.assertEqual(info['shape'], (10, 256, 256))
    
    def test_reset(self):
        """测试重置功能"""
        # 模拟加载图像和设置区域
        self.cropper.image_array = self.test_image_array
        self.cropper.set_crop_region(50, 50, 100, 100)
        self.cropper.crop_all_slices()
        
        # 重置
        self.cropper.reset()
        
        # 验证重置结果
        self.assertIsNone(self.cropper.image_array)
        self.assertIsNone(self.cropper.crop_region)
        self.assertIsNone(self.cropper.cropped_array)


class TestImageUtils(unittest.TestCase):
    """图像工具测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.test_image = np.random.randint(-1000, 1000, size=(256, 256), dtype=np.int16)
    
    def test_normalize_image(self):
        """测试图像标准化"""
        normalized = ImageUtils.normalize_image(self.test_image)
        self.assertEqual(normalized.dtype, np.uint8)
        self.assertTrue(np.all(normalized >= 0))
        self.assertTrue(np.all(normalized <= 255))
    
    def test_normalize_image_with_window(self):
        """测试使用窗宽窗位的图像标准化"""
        window_center = 0
        window_width = 400
        normalized = ImageUtils.normalize_image(self.test_image, window_center, window_width)
        self.assertEqual(normalized.dtype, np.uint8)
    
    def test_get_optimal_window(self):
        """测试获取最佳窗宽窗位"""
        window_center, window_width = ImageUtils.get_optimal_window(self.test_image)
        self.assertIsInstance(window_center, float)
        self.assertIsInstance(window_width, float)
        self.assertGreater(window_width, 0)
    
    def test_crop_image(self):
        """测试图像裁剪"""
        cropped = ImageUtils.crop_image(self.test_image, 50, 50, 100, 100)
        self.assertEqual(cropped.shape, (100, 100))
    
    def test_add_crop_overlay(self):
        """测试添加裁剪覆盖层"""
        overlay = ImageUtils.add_crop_overlay(self.test_image, 50, 50, 100, 100)
        self.assertEqual(overlay.shape, self.test_image.shape)
    
    def test_calculate_image_statistics(self):
        """测试计算图像统计信息"""
        stats = ImageUtils.calculate_image_statistics(self.test_image)
        self.assertIn('shape', stats)
        self.assertIn('min', stats)
        self.assertIn('max', stats)
        self.assertIn('mean', stats)
        self.assertIn('std', stats)


class TestFileUtils(unittest.TestCase):
    """文件工具测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = tempfile.mkdtemp()
    
    def tearDown(self):
        """测试后清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)
    
    def test_create_directory(self):
        """测试创建目录"""
        test_dir = os.path.join(self.temp_dir, "test_dir")
        success = FileUtils.create_directory(test_dir)
        self.assertTrue(success)
        self.assertTrue(os.path.exists(test_dir))
    
    def test_validate_path_valid(self):
        """测试验证有效路径"""
        # 确保临时目录存在
        self.assertTrue(os.path.exists(self.temp_dir))
        is_valid, error_msg = FileUtils.validate_path(self.temp_dir, must_exist=True, must_be_dir=True)
        if not is_valid:
            print(f"验证失败: {error_msg}, 路径: {self.temp_dir}")
        self.assertTrue(is_valid)
        self.assertEqual(error_msg, "")
    
    def test_validate_path_invalid(self):
        """测试验证无效路径"""
        # 测试不存在的路径
        nonexistent_path = os.path.join(self.temp_dir, "nonexistent")
        is_valid, error_msg = FileUtils.validate_path(nonexistent_path, must_exist=True)
        self.assertFalse(is_valid)
        self.assertNotEqual(error_msg, "")
        
        # 测试空路径
        is_valid, error_msg = FileUtils.validate_path("")
        self.assertFalse(is_valid)
        self.assertNotEqual(error_msg, "")
    
    def test_get_unique_filename(self):
        """测试获取唯一文件名"""
        # 创建测试文件
        test_file = os.path.join(self.temp_dir, "test.txt")
        with open(test_file, 'w') as f:
            f.write("test")
        
        # 获取唯一文件名
        unique_file = FileUtils.get_unique_filename(test_file)
        self.assertNotEqual(unique_file, test_file)
        self.assertFalse(os.path.exists(unique_file))
    
    def test_format_file_size(self):
        """测试格式化文件大小"""
        self.assertEqual(FileUtils.format_file_size(0), "0 B")
        self.assertEqual(FileUtils.format_file_size(1024), "1.0 KB")
        self.assertEqual(FileUtils.format_file_size(1024*1024), "1.0 MB")


class TestMetadataManager(unittest.TestCase):
    """元数据管理器测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.metadata_manager = MetadataManager()
    
    def test_create_default_slice_metadata(self):
        """测试创建默认切片元数据"""
        slice_meta = self.metadata_manager._create_default_slice_metadata(0)
        self.assertIn('slice_index', slice_meta)
        self.assertIn('instance_number', slice_meta)
        self.assertIn('sop_instance_uid', slice_meta)
        self.assertEqual(slice_meta['slice_index'], 0)
        self.assertEqual(slice_meta['instance_number'], 1)


def create_test_suite():
    """创建测试套件"""
    suite = unittest.TestSuite()
    
    # 添加测试类
    suite.addTest(unittest.makeSuite(TestCTRegionCropper))
    suite.addTest(unittest.makeSuite(TestImageUtils))
    suite.addTest(unittest.makeSuite(TestFileUtils))
    suite.addTest(unittest.makeSuite(TestMetadataManager))
    
    return suite


def run_tests():
    """运行测试"""
    print("开始运行CT区域选框分割工具测试...")
    print("=" * 50)
    
    # 创建测试套件
    suite = create_test_suite()
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出结果摘要
    print("\n" + "=" * 50)
    print("测试结果摘要:")
    print(f"运行测试数: {result.testsRun}")
    print(f"失败数: {len(result.failures)}")
    print(f"错误数: {len(result.errors)}")
    
    if result.failures:
        print("\n失败的测试:")
        for test, traceback in result.failures:
            print(f"- {test}: {traceback}")
    
    if result.errors:
        print("\n错误的测试:")
        for test, traceback in result.errors:
            print(f"- {test}: {traceback}")
    
    if result.wasSuccessful():
        print("\n✅ 所有测试通过!")
    else:
        print("\n❌ 部分测试失败")
    
    return result.wasSuccessful()


if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
