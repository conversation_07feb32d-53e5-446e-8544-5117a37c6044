#!/usr/bin/env python3
"""
DICOM序列选择功能使用示例

本示例展示如何使用新增的DICOM序列选择功能：
1. 检测DICOM目录中的多个序列
2. 显示序列选择对话框
3. 加载用户选择的序列

使用方法：
1. 准备一个包含多个DICOM序列的目录
2. 运行此脚本
3. 在弹出的对话框中选择要处理的序列
"""

import sys
import os
from pathlib import Path

# 添加项目路径
sys.path.append(str(Path(__file__).parent))

def example_command_line_usage():
    """命令行使用示例"""
    print("=" * 60)
    print("命令行使用示例")
    print("=" * 60)
    
    # 示例：检测DICOM序列
    dicom_dir = input("请输入DICOM目录路径（或按回车跳过）: ").strip()
    
    if not dicom_dir or not os.path.exists(dicom_dir):
        print("跳过命令行示例")
        return
    
    try:
        from shared_utils.common.io_utils import get_dicom_series_info, load_dicom_series
        
        # 1. 检测所有序列
        print("🔍 检测DICOM序列...")
        series_info = get_dicom_series_info(dicom_dir)
        
        print(f"检测到 {len(series_info)} 个序列:")
        for i, (series_id, info) in enumerate(series_info.items(), 1):
            print(f"{i}. {info.get('series_description', '未知')} - {info.get('file_count', 0)} 张图像")
        
        # 2. 如果有多个序列，让用户选择
        if len(series_info) > 1:
            while True:
                try:
                    choice = input(f"请选择序列 (1-{len(series_info)}): ").strip()
                    if not choice:
                        print("取消选择")
                        return
                    
                    choice_idx = int(choice) - 1
                    if 0 <= choice_idx < len(series_info):
                        selected_series_id = list(series_info.keys())[choice_idx]
                        break
                    else:
                        print(f"请输入1到{len(series_info)}之间的数字")
                except ValueError:
                    print("请输入有效的数字")
            
            # 3. 加载选择的序列
            print(f"🔄 加载序列: {selected_series_id}")
            array, metadata = load_dicom_series(dicom_dir, selected_series_id)
            print(f"✅ 加载成功: 形状={array.shape}, 类型={array.dtype}")
        else:
            # 只有一个序列，直接加载
            print("🔄 加载唯一序列...")
            array, metadata = load_dicom_series(dicom_dir)
            print(f"✅ 加载成功: 形状={array.shape}, 类型={array.dtype}")
            
    except Exception as e:
        print(f"❌ 错误: {e}")


def example_gui_usage():
    """GUI使用示例"""
    print("=" * 60)
    print("GUI使用示例")
    print("=" * 60)
    
    try:
        from PyQt5.QtWidgets import QApplication, QFileDialog, QMessageBox
        from lung_segmentation.apps.gui_app import DicomSeriesSelectionDialog
        from shared_utils.common.io_utils import get_dicom_series_info
        
        # 创建应用程序
        app = QApplication(sys.argv)
        
        # 选择DICOM目录
        dicom_dir = QFileDialog.getExistingDirectory(None, "选择包含多个DICOM序列的目录")
        
        if not dicom_dir:
            print("用户取消了目录选择")
            return
        
        print(f"选择的目录: {dicom_dir}")
        
        # 检测序列
        try:
            series_info = get_dicom_series_info(dicom_dir)
            print(f"检测到 {len(series_info)} 个序列")
            
            if len(series_info) > 1:
                # 显示序列选择对话框
                dialog = DicomSeriesSelectionDialog(series_info)
                
                if dialog.exec_() == dialog.Accepted:
                    selected_series_id = dialog.get_selected_series_id()
                    if selected_series_id:
                        selected_info = series_info[selected_series_id]
                        QMessageBox.information(
                            None, "选择结果",
                            f"您选择了序列:\n"
                            f"序列ID: {selected_series_id}\n"
                            f"描述: {selected_info.get('series_description', '未知')}\n"
                            f"图像数量: {selected_info.get('file_count', 0)}\n"
                            f"模态: {selected_info.get('modality', '未知')}"
                        )
                        print(f"用户选择了序列: {selected_series_id}")
                    else:
                        print("未获取到选择的序列ID")
                else:
                    print("用户取消了序列选择")
            else:
                QMessageBox.information(None, "序列信息", "只检测到一个序列，将自动使用该序列")
                print("只有一个序列，自动使用")
                
        except Exception as e:
            QMessageBox.critical(None, "错误", f"检测DICOM序列时出错:\n{str(e)}")
            print(f"错误: {e}")
            
    except ImportError as e:
        print(f"GUI组件导入失败: {e}")
        print("请确保已安装PyQt5")


def main():
    """主函数"""
    print("🧪 DICOM序列选择功能使用示例")
    print("=" * 60)
    print("本示例展示如何使用新增的DICOM序列选择功能")
    print()
    
    print("选择使用方式:")
    print("1. 命令行方式")
    print("2. GUI方式")
    print("3. 退出")
    
    while True:
        choice = input("请选择 (1-3): ").strip()
        
        if choice == "1":
            example_command_line_usage()
            break
        elif choice == "2":
            example_gui_usage()
            break
        elif choice == "3":
            print("退出示例")
            break
        else:
            print("请输入1、2或3")
    
    print("\n" + "=" * 60)
    print("📋 功能总结:")
    print("✅ 自动检测DICOM目录中的所有序列")
    print("✅ 显示每个序列的详细信息（描述、图像数量、模态等）")
    print("✅ 提供用户友好的序列选择界面")
    print("✅ 支持加载指定序列进行后续处理")
    print("✅ 单序列时自动加载，多序列时弹出选择对话框")
    print()
    print("💡 在肺部CT分割GUI中，当您选择包含多个序列的DICOM目录时，")
    print("   系统会自动弹出序列选择对话框，让您选择要分割的序列。")
    print("=" * 60)


if __name__ == "__main__":
    main()
