"""
文件处理工具模块

提供文件和目录处理相关的工具函数
"""

import os
import shutil
import logging
from pathlib import Path
from typing import List, Optional, Tuple
import pydicom

logger = logging.getLogger(__name__)


class FileUtils:
    """文件处理工具类"""
    
    @staticmethod
    def find_dicom_files(directory: str, recursive: bool = False) -> List[str]:
        """
        查找目录中的DICOM文件
        
        Args:
            directory: 搜索目录
            recursive: 是否递归搜索子目录
            
        Returns:
            DICOM文件路径列表
        """
        try:
            dicom_files = []
            directory = Path(directory)
            
            if not directory.exists():
                logger.warning(f"目录不存在: {directory}")
                return dicom_files
            
            # 定义DICOM文件扩展名
            dicom_extensions = {'.dcm', '.dicom', '.dic', '.ima', ''}
            
            # 搜索文件
            if recursive:
                pattern = '**/*'
            else:
                pattern = '*'
            
            for file_path in directory.glob(pattern):
                if file_path.is_file():
                    # 检查扩展名
                    if file_path.suffix.lower() in dicom_extensions:
                        # 尝试读取文件验证是否为DICOM
                        if FileUtils.is_dicom_file(str(file_path)):
                            dicom_files.append(str(file_path))
            
            logger.info(f"在目录 {directory} 中找到 {len(dicom_files)} 个DICOM文件")
            return sorted(dicom_files)
            
        except Exception as e:
            logger.error(f"查找DICOM文件失败: {e}")
            return []
    
    @staticmethod
    def is_dicom_file(file_path: str) -> bool:
        """
        检查文件是否为DICOM格式
        
        Args:
            file_path: 文件路径
            
        Returns:
            是否为DICOM文件
        """
        try:
            # 尝试读取DICOM文件头
            pydicom.dcmread(file_path, stop_before_pixels=True)
            return True
        except:
            return False
    
    @staticmethod
    def create_directory(directory: str, exist_ok: bool = True) -> bool:
        """
        创建目录
        
        Args:
            directory: 目录路径
            exist_ok: 如果目录已存在是否报错
            
        Returns:
            创建是否成功
        """
        try:
            os.makedirs(directory, exist_ok=exist_ok)
            logger.info(f"创建目录: {directory}")
            return True
        except Exception as e:
            logger.error(f"创建目录失败 {directory}: {e}")
            return False
    
    @staticmethod
    def copy_file(src: str, dst: str, create_dirs: bool = True) -> bool:
        """
        复制文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            create_dirs: 是否自动创建目标目录
            
        Returns:
            复制是否成功
        """
        try:
            if create_dirs:
                dst_dir = os.path.dirname(dst)
                if dst_dir and not os.path.exists(dst_dir):
                    os.makedirs(dst_dir, exist_ok=True)
            
            shutil.copy2(src, dst)
            return True
        except Exception as e:
            logger.error(f"复制文件失败 {src} -> {dst}: {e}")
            return False
    
    @staticmethod
    def move_file(src: str, dst: str, create_dirs: bool = True) -> bool:
        """
        移动文件
        
        Args:
            src: 源文件路径
            dst: 目标文件路径
            create_dirs: 是否自动创建目标目录
            
        Returns:
            移动是否成功
        """
        try:
            if create_dirs:
                dst_dir = os.path.dirname(dst)
                if dst_dir and not os.path.exists(dst_dir):
                    os.makedirs(dst_dir, exist_ok=True)
            
            shutil.move(src, dst)
            return True
        except Exception as e:
            logger.error(f"移动文件失败 {src} -> {dst}: {e}")
            return False
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            删除是否成功
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                logger.info(f"删除文件: {file_path}")
            return True
        except Exception as e:
            logger.error(f"删除文件失败 {file_path}: {e}")
            return False
    
    @staticmethod
    def delete_directory(directory: str, recursive: bool = False) -> bool:
        """
        删除目录
        
        Args:
            directory: 目录路径
            recursive: 是否递归删除
            
        Returns:
            删除是否成功
        """
        try:
            if os.path.exists(directory):
                if recursive:
                    shutil.rmtree(directory)
                else:
                    os.rmdir(directory)
                logger.info(f"删除目录: {directory}")
            return True
        except Exception as e:
            logger.error(f"删除目录失败 {directory}: {e}")
            return False
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """
        获取文件大小
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件大小（字节）
        """
        try:
            return os.path.getsize(file_path)
        except Exception as e:
            logger.error(f"获取文件大小失败 {file_path}: {e}")
            return 0
    
    @staticmethod
    def get_directory_size(directory: str) -> int:
        """
        获取目录大小
        
        Args:
            directory: 目录路径
            
        Returns:
            目录大小（字节）
        """
        try:
            total_size = 0
            for dirpath, dirnames, filenames in os.walk(directory):
                for filename in filenames:
                    file_path = os.path.join(dirpath, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except:
                        continue
            return total_size
        except Exception as e:
            logger.error(f"获取目录大小失败 {directory}: {e}")
            return 0
    
    @staticmethod
    def format_file_size(size_bytes: int) -> str:
        """
        格式化文件大小显示
        
        Args:
            size_bytes: 文件大小（字节）
            
        Returns:
            格式化的大小字符串
        """
        try:
            if size_bytes == 0:
                return "0 B"
            
            size_names = ["B", "KB", "MB", "GB", "TB"]
            i = 0
            size = float(size_bytes)
            
            while size >= 1024.0 and i < len(size_names) - 1:
                size /= 1024.0
                i += 1
            
            return f"{size:.1f} {size_names[i]}"
        except:
            return f"{size_bytes} B"
    
    @staticmethod
    def validate_path(path: str, must_exist: bool = False, must_be_dir: bool = False) -> Tuple[bool, str]:
        """
        验证路径有效性
        
        Args:
            path: 路径
            must_exist: 路径必须存在
            must_be_dir: 路径必须是目录
            
        Returns:
            (是否有效, 错误信息)
        """
        try:
            if not path or not path.strip():
                return False, "路径不能为空"
            
            path = path.strip()
            
            if must_exist and not os.path.exists(path):
                return False, f"路径不存在: {path}"
            
            if must_be_dir and os.path.exists(path) and not os.path.isdir(path):
                return False, f"路径不是目录: {path}"
            
            # 检查路径是否包含非法字符（Windows系统中冒号在驱动器字母后是合法的）
            invalid_chars = '<>"|?*'
            # 对于Windows系统，跳过驱动器字母后的冒号
            check_path = path
            if len(path) >= 2 and path[1] == ':' and path[0].isalpha():
                check_path = path[2:]  # 跳过 "C:" 部分

            if any(char in check_path for char in invalid_chars):
                return False, f"路径包含非法字符: {invalid_chars}"
            
            return True, ""
            
        except Exception as e:
            return False, f"路径验证失败: {e}"
    
    @staticmethod
    def get_unique_filename(file_path: str) -> str:
        """
        获取唯一的文件名（如果文件已存在，添加数字后缀）
        
        Args:
            file_path: 原始文件路径
            
        Returns:
            唯一的文件路径
        """
        try:
            if not os.path.exists(file_path):
                return file_path
            
            directory = os.path.dirname(file_path)
            filename = os.path.basename(file_path)
            name, ext = os.path.splitext(filename)
            
            counter = 1
            while True:
                new_filename = f"{name}_{counter}{ext}"
                new_path = os.path.join(directory, new_filename)
                if not os.path.exists(new_path):
                    return new_path
                counter += 1
                
        except Exception as e:
            logger.error(f"生成唯一文件名失败: {e}")
            return file_path
    
    @staticmethod
    def backup_file(file_path: str, backup_suffix: str = ".bak") -> Optional[str]:
        """
        备份文件
        
        Args:
            file_path: 要备份的文件路径
            backup_suffix: 备份文件后缀
            
        Returns:
            备份文件路径或None
        """
        try:
            if not os.path.exists(file_path):
                logger.warning(f"要备份的文件不存在: {file_path}")
                return None
            
            backup_path = file_path + backup_suffix
            backup_path = FileUtils.get_unique_filename(backup_path)
            
            if FileUtils.copy_file(file_path, backup_path):
                logger.info(f"文件备份成功: {file_path} -> {backup_path}")
                return backup_path
            else:
                return None
                
        except Exception as e:
            logger.error(f"备份文件失败: {e}")
            return None
