"""
主窗口模块

CT区域选框分割工具的主界面
"""

import os
import sys
import logging
from pathlib import Path
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout, 
                            QHBoxLayout, QSplitter, QMenuBar, QMenu, QAction,
                            QFileDialog, QMessageBox, QStatusBar, QProgressBar,
                            QLabel, QPushButton, QGroupBox, QTextEdit)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QIcon

# 添加父目录到路径以便导入模块
sys.path.append(str(Path(__file__).parent.parent))

try:
    from .image_viewer import ImageViewer
    from .region_selector import RegionSelector
    from ..core.region_cropper import CTRegionCropper
    from ..utils.file_utils import FileUtils
except ImportError:
    # 如果相对导入失败，尝试绝对导入
    from gui.image_viewer import ImageViewer
    from gui.region_selector import RegionSelector
    from core.region_cropper import CTRegionCropper
    from utils.file_utils import FileUtils

logger = logging.getLogger(__name__)


class ProcessingThread(QThread):
    """处理线程"""
    
    progress_updated = pyqtSignal(int)
    status_updated = pyqtSignal(str)
    finished_successfully = pyqtSignal()
    error_occurred = pyqtSignal(str)
    
    def __init__(self, cropper, operation, *args):
        super().__init__()
        self.cropper = cropper
        self.operation = operation
        self.args = args
    
    def run(self):
        try:
            if self.operation == 'load':
                directory = self.args[0]
                self.status_updated.emit("正在加载DICOM文件...")
                success = self.cropper.load_dicom_series(directory)
                if success:
                    self.finished_successfully.emit()
                else:
                    self.error_occurred.emit("加载DICOM文件失败")
                    
            elif self.operation == 'crop':
                self.status_updated.emit("正在执行裁剪...")
                success = self.cropper.crop_all_slices()
                if success:
                    self.finished_successfully.emit()
                else:
                    self.error_occurred.emit("裁剪操作失败")
                    
            elif self.operation == 'save':
                output_dir = self.args[0]
                self.status_updated.emit("正在保存DICOM文件...")
                success = self.cropper.save_cropped_series(output_dir)
                if success:
                    self.finished_successfully.emit()
                else:
                    self.error_occurred.emit("保存DICOM文件失败")
                    
        except Exception as e:
            self.error_occurred.emit(str(e))


class CTRegionCropperApp(QMainWindow):
    """CT区域选框分割工具主窗口"""
    
    def __init__(self):
        super().__init__()
        self.cropper = CTRegionCropper()
        self.processing_thread = None
        
        self.setup_ui()
        self.setup_menu()
        self.setup_status_bar()
        self.setup_connections()
        
        # 设置窗口属性
        self.setWindowTitle("CT区域选框分割工具 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        logger.info("CT区域选框分割工具启动")
    
    def setup_ui(self):
        """设置用户界面"""
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧面板 - 图像显示
        self.image_viewer = ImageViewer()
        splitter.addWidget(self.image_viewer)
        
        # 右侧面板 - 控制面板
        right_panel = self.create_right_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([800, 400])
    
    def create_right_panel(self):
        """创建右侧控制面板"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # 文件操作组
        file_group = self.create_file_group()
        layout.addWidget(file_group)
        
        # 区域选择器
        self.region_selector = RegionSelector()
        layout.addWidget(self.region_selector)
        
        # 信息显示组
        info_group = self.create_info_group()
        layout.addWidget(info_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel
    
    def create_file_group(self):
        """创建文件操作组"""
        group = QGroupBox("文件操作")
        layout = QVBoxLayout(group)
        
        # 加载按钮
        self.load_btn = QPushButton("加载DICOM文件夹")
        self.load_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; padding: 8px; }")
        layout.addWidget(self.load_btn)
        
        # 保存按钮
        self.save_btn = QPushButton("保存裁剪结果")
        self.save_btn.setStyleSheet("QPushButton { background-color: #2196F3; color: white; padding: 8px; }")
        self.save_btn.setEnabled(False)
        layout.addWidget(self.save_btn)
        
        # 重置按钮
        self.reset_btn = QPushButton("重置")
        layout.addWidget(self.reset_btn)
        
        return group
    
    def create_info_group(self):
        """创建信息显示组"""
        group = QGroupBox("图像信息")
        layout = QVBoxLayout(group)
        
        # 信息文本框
        self.info_text = QTextEdit()
        self.info_text.setMaximumHeight(150)
        self.info_text.setReadOnly(True)
        layout.addWidget(self.info_text)
        
        return group
    
    def setup_menu(self):
        """设置菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu('文件')
        
        load_action = QAction('加载DICOM文件夹', self)
        load_action.setShortcut('Ctrl+O')
        load_action.triggered.connect(self.load_dicom_folder)
        file_menu.addAction(load_action)
        
        save_action = QAction('保存裁剪结果', self)
        save_action.setShortcut('Ctrl+S')
        save_action.triggered.connect(self.save_cropped_series)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        exit_action = QAction('退出', self)
        exit_action.setShortcut('Ctrl+Q')
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)
        
        # 帮助菜单
        help_menu = menubar.addMenu('帮助')
        
        about_action = QAction('关于', self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)
    
    def setup_status_bar(self):
        """设置状态栏"""
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        
        # 状态标签
        self.status_label = QLabel("就绪")
        self.status_bar.addWidget(self.status_label)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.status_bar.addPermanentWidget(self.progress_bar)
    
    def setup_connections(self):
        """设置信号连接"""
        # 按钮连接
        self.load_btn.clicked.connect(self.load_dicom_folder)
        self.save_btn.clicked.connect(self.save_cropped_series)
        self.reset_btn.clicked.connect(self.reset_application)
        
        # 图像查看器连接
        self.image_viewer.crop_region_changed.connect(self.on_crop_region_changed)
        
        # 区域选择器连接
        self.region_selector.region_changed.connect(self.on_region_changed)
        self.region_selector.region_cleared.connect(self.on_region_cleared)
        self.region_selector.crop_requested.connect(self.execute_crop)
    
    def load_dicom_folder(self):
        """加载DICOM文件夹"""
        try:
            folder = QFileDialog.getExistingDirectory(
                self, "选择DICOM文件夹", "", 
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )
            
            if not folder:
                return
            
            # 验证文件夹
            dicom_files = FileUtils.find_dicom_files(folder)
            if not dicom_files:
                QMessageBox.warning(self, "警告", "所选文件夹中没有找到DICOM文件")
                return
            
            # 在后台线程中加载
            self.start_processing('load', folder)
            
        except Exception as e:
            logger.error(f"加载DICOM文件夹失败: {e}")
            QMessageBox.critical(self, "错误", f"加载失败:\n{str(e)}")
    
    def on_load_completed(self):
        """加载完成处理"""
        try:
            # 更新图像显示
            self.image_viewer.load_image_array(self.cropper.image_array)
            
            # 更新区域选择器
            self.region_selector.set_image_shape(self.cropper.image_array.shape)
            
            # 更新信息显示
            self.update_info_display()
            
            self.status_label.setText("DICOM文件加载完成")
            logger.info("DICOM文件加载完成")
            
        except Exception as e:
            logger.error(f"加载完成处理失败: {e}")
            QMessageBox.critical(self, "错误", f"处理加载结果失败:\n{str(e)}")
    
    def on_crop_region_changed(self, x, y, width, height):
        """裁剪区域改变处理"""
        # 同步到区域选择器
        self.region_selector.set_region(x, y, width, height)
        
        # 设置裁剪区域
        self.cropper.set_crop_region(x, y, width, height)
    
    def on_region_changed(self, x, y, width, height):
        """区域改变处理"""
        # 同步到图像显示器
        self.image_viewer.set_crop_region(x, y, width, height)
        
        # 设置裁剪区域
        self.cropper.set_crop_region(x, y, width, height)
    
    def on_region_cleared(self):
        """区域清除处理"""
        self.image_viewer.clear_crop_region()
        self.cropper.crop_region = None
    
    def execute_crop(self):
        """执行裁剪操作"""
        try:
            if self.cropper.image_array is None:
                QMessageBox.warning(self, "警告", "请先加载DICOM文件")
                return
            
            if self.cropper.crop_region is None:
                QMessageBox.warning(self, "警告", "请先设置裁剪区域")
                return
            
            # 在后台线程中执行裁剪
            self.start_processing('crop')
            
        except Exception as e:
            logger.error(f"执行裁剪失败: {e}")
            QMessageBox.critical(self, "错误", f"裁剪失败:\n{str(e)}")
    
    def on_crop_completed(self):
        """裁剪完成处理"""
        self.save_btn.setEnabled(True)
        self.status_label.setText("裁剪操作完成")
        self.update_info_display()
        logger.info("裁剪操作完成")
    
    def save_cropped_series(self):
        """保存裁剪后的系列"""
        try:
            if self.cropper.cropped_array is None:
                QMessageBox.warning(self, "警告", "请先执行裁剪操作")
                return
            
            folder = QFileDialog.getExistingDirectory(
                self, "选择保存文件夹", "", 
                QFileDialog.ShowDirsOnly | QFileDialog.DontResolveSymlinks
            )
            
            if not folder:
                return
            
            # 在后台线程中保存
            self.start_processing('save', folder)
            
        except Exception as e:
            logger.error(f"保存裁剪系列失败: {e}")
            QMessageBox.critical(self, "错误", f"保存失败:\n{str(e)}")
    
    def on_save_completed(self):
        """保存完成处理"""
        self.status_label.setText("保存操作完成")
        QMessageBox.information(self, "成功", "裁剪结果保存完成")
        logger.info("保存操作完成")
    
    def start_processing(self, operation, *args):
        """开始后台处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            return
        
        self.processing_thread = ProcessingThread(self.cropper, operation, *args)
        self.processing_thread.status_updated.connect(self.status_label.setText)
        self.processing_thread.finished_successfully.connect(self.on_processing_finished)
        self.processing_thread.error_occurred.connect(self.on_processing_error)
        
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 不确定进度
        
        self.processing_thread.start()
    
    def on_processing_finished(self):
        """处理完成"""
        self.progress_bar.setVisible(False)
        
        if self.processing_thread.operation == 'load':
            self.on_load_completed()
        elif self.processing_thread.operation == 'crop':
            self.on_crop_completed()
        elif self.processing_thread.operation == 'save':
            self.on_save_completed()
    
    def on_processing_error(self, error_msg):
        """处理错误"""
        self.progress_bar.setVisible(False)
        self.status_label.setText("操作失败")
        QMessageBox.critical(self, "错误", f"操作失败:\n{error_msg}")
        logger.error(f"处理错误: {error_msg}")
    
    def update_info_display(self):
        """更新信息显示"""
        info = self.cropper.get_image_info()
        
        info_text = []
        if 'shape' in info:
            info_text.append(f"图像形状: {info['shape']}")
        if 'dtype' in info:
            info_text.append(f"数据类型: {info['dtype']}")
        if 'patient_id' in info:
            info_text.append(f"患者ID: {info['patient_id']}")
        if 'modality' in info:
            info_text.append(f"模态: {info['modality']}")
        if 'crop_region' in info:
            info_text.append(f"裁剪区域: {info['crop_region']}")
        if 'cropped_shape' in info:
            info_text.append(f"裁剪后形状: {info['cropped_shape']}")
        
        self.info_text.setText('\n'.join(info_text))
    
    def reset_application(self):
        """重置应用程序"""
        reply = QMessageBox.question(
            self, "确认重置", "确定要重置应用程序吗？这将清除所有当前数据。",
            QMessageBox.Yes | QMessageBox.No, QMessageBox.No
        )
        
        if reply == QMessageBox.Yes:
            self.cropper.reset()
            self.image_viewer.image_array = None
            self.image_viewer.update_display()
            self.region_selector.clear_region()
            self.save_btn.setEnabled(False)
            self.info_text.clear()
            self.status_label.setText("已重置")
            logger.info("应用程序已重置")
    
    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(
            self, "关于",
            "CT区域选框分割工具 v1.0\n\n"
            "这是一个专门用于CT影像区域选框分割的工具。\n"
            "支持DICOM格式的输入和输出，保持原有的元数据信息。\n\n"
            "主要功能:\n"
            "• DICOM文件读取和显示\n"
            "• 交互式区域选择\n"
            "• 多层面批量分割\n"
            "• 元数据保持\n"
            "• DICOM格式输出"
        )
    
    def closeEvent(self, event):
        """关闭事件处理"""
        if self.processing_thread and self.processing_thread.isRunning():
            reply = QMessageBox.question(
                self, "确认退出", "后台任务正在运行，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No
            )
            
            if reply == QMessageBox.Yes:
                self.processing_thread.terminate()
                self.processing_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("CT区域选框分割工具")
    app.setApplicationVersion("1.0")
    
    # 创建主窗口
    window = CTRegionCropperApp()
    window.show()
    
    sys.exit(app.exec_())
