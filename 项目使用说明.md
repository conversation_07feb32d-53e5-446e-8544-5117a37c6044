# 项目使用说明

## 1. 项目简介

欢迎使用本肺部CT影像分析工具。您可以将本工具看作一个包含两大步骤的智能助手：

1.  **第一步：智能识别与勾画肺部**
    *   您提供CT影像，本工具的第一个程序会自动、精确地在三维影像中找到您的肺部（包括左肺、右肺和肺叶），并将其"勾画"出来。这个过程我们称之为"分割"。
    *   这一步的产出是一个"蒙版(mask)"文件，它像一个透明的模板，精确地覆盖在肺部区域。

2.  **第二步：分析肺部健康状况**
    *   本工具的第二个程序会使用上一步生成的"蒙版"，对您肺部区域的CT影像进行详细分析。
    *   它能计算出您肺部的总体积，以及不同健康状况的肺组织（如正常充气、存在炎症、肺气肿、实变等）各占多少体积和比例。
    .   最终，它会给出一个关于肺气肿严重程度的评估，并能生成肺部的三维模型，以及一份可以导出的Excel数据报告。

简而言之，通过这两个步骤，您可以从原始的CT影像出发，最终得到一份关于肺部健康状况的量化分析报告。

---

## 2. 安装与准备

在开始使用前，我们需要在您的电脑上准备好运行环境。请严格按照以下步骤操作，这会比看起来简单。

#### **第1步：下载项目文件**

*   请从项目主页（通常是GitHub页面）上，找到一个绿色的 "Code" 按钮，点击后选择 "Download ZIP"。
*   下载后，您会得到一个名为 `lung_ct_segmenter-main.zip` (或类似名称) 的压缩包。
*   请将这个压缩包解压到一个您容易找到的位置，例如 `D:\Code\lung_ct_segmenter`。**请确保文件路径中不包含中文字符**。

#### **第2步：安装Python环境**

*   如果您的电脑还没有安装Python，请访问 [Python官网](https://www.python.org/downloads/) 下载最新版本的Python (例如 Python 3.9+)。
*   **重要提示**：在安装时，请务必勾选 "Add Python to PATH" 或 "将Python添加到环境变量" 这个选项。

#### **第3步：安装所需的依赖库**

这是最关键的一步，它会自动下载并安装本工具运行所需要的所有"零件"。

1.  **打开命令提示符(CMD)或终端(Terminal)**:
    *   **Windows用户**: 按下 `Win` + `R` 键，在弹出的"运行"窗口中输入 `cmd`，然后按回车。
    *   **macOS用户**: 在"应用程序" -> "实用工具"中找到"终端(Terminal)"并打开。

2.  **进入项目文件夹**:
    *   在打开的黑色命令窗口中，您需要进入到刚才解压的项目文件夹。
    *   假设您解压到了 `D:\Code\lung_ct_segmenter`，请输入以下命令并按回车 (您可以直接复制粘贴)：
        ```bash
        cd /d D:\Code\lung_ct_segmenter
        ```
    *   如果您的路径不同，请相应修改。

3.  **安装依赖**:
    *   在同一个窗口中，输入以下命令并按回车。计算机会开始自动下载和安装过程，根据您的网络情况，可能需要几分钟到十几分钟不等。
        ```bash
        pip install -r requirements.txt
        ```
    *   如果此命令运行缓慢，您可以尝试使用国内镜像源，命令如下：
        ```bash
        pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple
        ```

当窗口中不再有新的内容输出，并显示光标时，说明安装已完成。您现在已经准备好使用本工具了！

---

## 2.1 新版本快速启动方式

**重要更新**：项目已重新组织，现在提供更简单的启动方式！

在完成上述安装步骤后，您可以直接双击以下Python脚本来启动各个功能：

- **`run_lung_segmentation.py`** - 启动肺组织分割工具
- **`run_lung_tissue_analyzer.py`** - 启动肺组织分析工具
- **`run_image_to_dicom.py`** - 启动图片转DICOM工具
- **`run_dicom_renumber.py`** - 启动DICOM重新编号工具
- **`run_ct_region_cropper.py`** - 启动CT区域裁剪工具

或者在命令行中运行：
```bash
python run_lung_segmentation.py      # 肺组织分割
python run_lung_tissue_analyzer.py   # 肺组织分析
python run_image_to_dicom.py         # 图片转DICOM
python run_dicom_renumber.py         # DICOM重新编号
python run_ct_region_cropper.py      # CT区域裁剪
```

---

## 3. 操作流程：先分割，后分析

请牢记，使用本工具分为两步，需要依次运行两个不同的程序。

### **新版本启动方式（推荐）**

现在您可以直接使用项目根目录下的启动脚本：

1.  **第一步：肺部分割**
    ```bash
    python run_lung_segmentation.py
    ```
    或直接双击 `run_lung_segmentation.py` 文件

2.  **第二步：肺组织分析**
    ```bash
    python run_lung_tissue_analyzer.py
    ```
    或直接双击 `run_lung_tissue_analyzer.py` 文件

### **传统启动方式（仍然可用）**

如果您习惯使用原来的方式，也可以：

1.  打开命令提示符窗口
2.  进入项目文件夹：
    ```bash
    cd /d D:\Code\lung_ct_segmenter
    ```
3.  **启动分割程序**:
    ```bash
    python lung_segmentation/apps/gui_app.py
    ```
4.  **启动分析程序**:
    ```bash
    python lung_segmentation/apps/lung_tissue_analyzer.py
    ```

---

## 4. 第一步：肺部分割操作指南 (`gui_app.py`)

1.  **启动分割程序**，您会看到如下界面。

    ![分割程序界面](placeholder_segmenter.png)  *（这是一个界面占位图，实际界面可能略有不同）*

2.  **选择模式**:
    *   **单文件处理**: 如果您只想处理一个病人的CT影像，请保持默认的"单文件模式"。
    *   **批量处理**: 如果您有一个文件夹，里面包含多个病人的影像，可以选择"批处理模式"。

3.  **选择输入**:
    *   点击 **"浏览输入"** 按钮。
    *   选择您的CT影像文件（通常是 `.nii`, `.nii.gz` 格式的文件）或者包含DICOM序列的文件夹。

4.  **选择输出文件夹**:
    *   点击 **"浏览输出"** 按钮。
    *   选择一个文件夹，用来存放分割后的结果文件。建议新建一个专门的文件夹，例如 `D:\CT_Results`。

5.  **选择模型 (关键步骤)**:
    *   **模型类型**: 推荐使用 `lungmask`。
    *   **模型变体**:
        *   如果您想分割**整个肺部**，选择 `R231`。
        *   如果您想同时分割出**左肺叶和右肺叶**，请选择 `LTRCLobes` (推荐)。

6.  **开始分割**:
    *   点击界面右下角的 **"开始分割"** 按钮。
    *   程序会开始处理，界面下方的进度条和日志窗口会显示当前进度。处理时间从几分钟到十几分钟不等，取决于您的电脑性能和影像大小。

7.  **完成与结果**:
    *   当程序提示"分割完成"后，您可以在右侧的图像查看器中看到分割结果的预览。
    *   请打开您之前设定的**输出文件夹**。您会找到一个或多个新生成的文件。其中，**最重要的文件**是那个以 `_lungmask.nii.gz` 或 `_lobes.nii.gz` 结尾的文件。**这个文件是第二步分析的必需输入！**

---

## 5. 第二步：肺组织分析操作指南 (`lung_tissue_analyzer.py`)

在完成第一步并获得了 `_lungmask.nii.gz` 文件后，现在可以进行定量分析。

1.  **启动分析程序**，您会看到如下界面。

    ![分析程序界面](placeholder_analyzer.png) *（这是一个界面占位图，实际界面可能略有不同）*

2.  **选择CT影像**:
    *   点击第一个 **"浏览"** 按钮。
    *   在这里，请选择您在第一步中使用的**原始CT影像文件**。

3.  **选择肺部蒙版 (关键步骤)**:
    *   点击第二个 **"浏览"** 按钮。
    *   在这里，请务必选择第一步操作生成的那个 `_lungmask.nii.gz` 或 `_lobes.nii.gz` 文件。

4.  **开始分析**:
    *   点击界面右下角的 **"开始分析"** 按钮。
    *   分析过程通常很快，几秒到一分钟内即可完成。

5.  **查看与解读结果**:
    *   分析完成后，界面右侧会弹出结果窗口，包含三个选项卡：
        *   **统计结果**: 这是最重要的部分。它用表格清晰地列出了：
            *   `total_lung_volume_cm3`: 总肺体积（单位：立方厘米）。
            *   `tissue_analysis` 表格: 显示了不同CT值范围的组织所占的体积和百分比。例如 `bulla` 代表肺大泡/重度肺气肿区域，`inflated` 代表正常充气的肺泡区。
            *   `emphysema_grade`: 基于LAA%（即`bulla`的占比）给出的**肺气肿严重程度评估**，例如"轻度肺气肿"。
        *   **三维重建**: 显示了您肺部的三维模型，可以拖动旋转。
        *   **CT值分布**: 一个显示肺内所有像素CT值分布情况的直方图。

6.  **导出报告**:
    *   在结果窗口的左下角，有一个 **"导出结果"** 按钮。
    *   点击后，您可以将包含所有统计数据的表格保存为一个Excel文件 (`.xlsx`)，方便后续记录和分析。

---

## 6. 常见问题 (FAQ)

*   **Q: 点击"开始分割"后程序没反应或闪退怎么办？**
    *   **A:** 最常见的原因是电脑内存或显存不足。请尝试关闭其他所有不必要的程序，特别是大型软件（如游戏、浏览器等），然后重试。如果仍然失败，可以尝试勾选主界面上的 `强制使用CPU` 选项，这会牺牲速度但能降低对显卡的要求。

*   **Q: 程序提示 "模型下载失败" 或类似错误。**
    *   **A:** 本工具首次运行时，需要从网络上自动下载AI模型文件。如果您的网络不稳定，可能导致下载失败。请确保您的电脑已连接到互联网，并尝试再次运行。

*   **Q: 我可以选择 `totalsegmentator` 模型吗？**
    *   **A:** 可以，但 `totalsegmentator` 是一个更庞大的全身分割模型，首次使用时需要下载非常大的模型文件，且运行速度更慢。对于单纯的肺部分析，`lungmask` 模型是更高效、更推荐的选择。

*   **Q: 我可以在第二步分析时，不选择"肺部蒙版"吗？**
    *   **A:** 不可以。分析程序必须知道哪些区域是肺部，才能进行准确计算。这个"肺部蒙版"文件正是用来告诉分析程序肺部的位置和范围的，因此它是必需的。 