"""
元数据管理模块

负责DICOM元数据的保持、更新和验证
"""

import logging
import pydicom
import numpy as np
from typing import Dict, Any, Optional, List
from datetime import datetime

logger = logging.getLogger(__name__)


class MetadataManager:
    """DICOM元数据管理器"""
    
    def __init__(self):
        self.base_metadata = {}
        self.slice_metadata = []
        
    def extract_metadata(self, datasets: List[pydicom.Dataset]) -> Dict[str, Any]:
        """
        从DICOM数据集列表中提取元数据
        
        Args:
            datasets: DICOM数据集列表
            
        Returns:
            提取的元数据字典
        """
        try:
            if not datasets or len(datasets) == 0:
                raise ValueError("没有有效的DICOM数据集")
            
            # 找到第一个有效的数据集
            first_valid_ds = None
            for ds in datasets:
                if ds is not None:
                    first_valid_ds = ds
                    break
            
            if first_valid_ds is None:
                raise ValueError("所有DICOM数据集都无效")
            
            # 提取基本元数据
            self.base_metadata = self._extract_base_metadata(first_valid_ds)
            
            # 提取每个切片的特定元数据
            self.slice_metadata = []
            for i, ds in enumerate(datasets):
                if ds is not None:
                    slice_meta = self._extract_slice_metadata(ds, i)
                    self.slice_metadata.append(slice_meta)
                else:
                    # 使用默认值
                    self.slice_metadata.append(self._create_default_slice_metadata(i))
            
            logger.info(f"成功提取 {len(self.slice_metadata)} 个切片的元数据")
            return self.base_metadata
            
        except Exception as e:
            logger.error(f"提取元数据失败: {e}")
            raise
    
    def _extract_base_metadata(self, ds: pydicom.Dataset) -> Dict[str, Any]:
        """提取基本元数据"""
        metadata = {
            # 患者信息
            'patient_id': getattr(ds, 'PatientID', ''),
            'patient_name': str(getattr(ds, 'PatientName', '')),
            'patient_birth_date': getattr(ds, 'PatientBirthDate', ''),
            'patient_sex': getattr(ds, 'PatientSex', ''),
            
            # 检查信息
            'study_date': getattr(ds, 'StudyDate', ''),
            'study_time': getattr(ds, 'StudyTime', ''),
            'study_description': getattr(ds, 'StudyDescription', ''),
            'study_instance_uid': getattr(ds, 'StudyInstanceUID', ''),
            'accession_number': getattr(ds, 'AccessionNumber', ''),
            
            # 系列信息
            'series_date': getattr(ds, 'SeriesDate', ''),
            'series_time': getattr(ds, 'SeriesTime', ''),
            'series_description': getattr(ds, 'SeriesDescription', ''),
            'series_instance_uid': getattr(ds, 'SeriesInstanceUID', ''),
            'series_number': getattr(ds, 'SeriesNumber', ''),
            'modality': getattr(ds, 'Modality', 'CT'),
            
            # 设备信息
            'manufacturer': getattr(ds, 'Manufacturer', ''),
            'manufacturer_model_name': getattr(ds, 'ManufacturerModelName', ''),
            'station_name': getattr(ds, 'StationName', ''),
            'software_versions': getattr(ds, 'SoftwareVersions', ''),
            'institution_name': getattr(ds, 'InstitutionName', ''),
            
            # 图像信息
            'rows': getattr(ds, 'Rows', 512),
            'columns': getattr(ds, 'Columns', 512),
            'bits_allocated': getattr(ds, 'BitsAllocated', 16),
            'bits_stored': getattr(ds, 'BitsStored', 16),
            'high_bit': getattr(ds, 'HighBit', 15),
            'pixel_representation': getattr(ds, 'PixelRepresentation', 1),
            'photometric_interpretation': getattr(ds, 'PhotometricInterpretation', 'MONOCHROME2'),
            'samples_per_pixel': getattr(ds, 'SamplesPerPixel', 1),
            
            # 几何信息
            'pixel_spacing': list(getattr(ds, 'PixelSpacing', [1.0, 1.0])),
            'slice_thickness': getattr(ds, 'SliceThickness', 1.0),
            'spacing_between_slices': getattr(ds, 'SpacingBetweenSlices', None),
            'image_orientation_patient': list(getattr(ds, 'ImageOrientationPatient', [1.0, 0.0, 0.0, 0.0, 1.0, 0.0])),
            
            # CT特定信息
            'kvp': getattr(ds, 'KVP', None),
            'exposure_time': getattr(ds, 'ExposureTime', None),
            'x_ray_tube_current': getattr(ds, 'XRayTubeCurrent', None),
            'exposure': getattr(ds, 'Exposure', None),
            'filter_type': getattr(ds, 'FilterType', None),
            'convolution_kernel': getattr(ds, 'ConvolutionKernel', None),
            
            # 窗宽窗位
            'window_center': getattr(ds, 'WindowCenter', None),
            'window_width': getattr(ds, 'WindowWidth', None),
            'rescale_intercept': getattr(ds, 'RescaleIntercept', 0),
            'rescale_slope': getattr(ds, 'RescaleSlope', 1),
            'rescale_type': getattr(ds, 'RescaleType', 'HU'),
            
            # 参考系
            'frame_of_reference_uid': getattr(ds, 'FrameOfReferenceUID', ''),
            'position_reference_indicator': getattr(ds, 'PositionReferenceIndicator', ''),
        }
        
        return metadata
    
    def _extract_slice_metadata(self, ds: pydicom.Dataset, slice_index: int) -> Dict[str, Any]:
        """提取切片特定的元数据"""
        slice_meta = {
            'slice_index': slice_index,
            'instance_number': getattr(ds, 'InstanceNumber', slice_index + 1),
            'sop_instance_uid': getattr(ds, 'SOPInstanceUID', ''),
            'sop_class_uid': getattr(ds, 'SOPClassUID', '1.2.840.10008.5.1.4.1.1.2'),
            'image_position_patient': list(getattr(ds, 'ImagePositionPatient', [0.0, 0.0, float(slice_index)])),
            'slice_location': getattr(ds, 'SliceLocation', float(slice_index)),
            'acquisition_number': getattr(ds, 'AcquisitionNumber', 1),
            'acquisition_date': getattr(ds, 'AcquisitionDate', ''),
            'acquisition_time': getattr(ds, 'AcquisitionTime', ''),
            'content_date': getattr(ds, 'ContentDate', ''),
            'content_time': getattr(ds, 'ContentTime', ''),
        }
        
        return slice_meta
    
    def _create_default_slice_metadata(self, slice_index: int) -> Dict[str, Any]:
        """创建默认的切片元数据"""
        return {
            'slice_index': slice_index,
            'instance_number': slice_index + 1,
            'sop_instance_uid': pydicom.uid.generate_uid(),
            'sop_class_uid': '1.2.840.10008.5.1.4.1.1.2',
            'image_position_patient': [0.0, 0.0, float(slice_index)],
            'slice_location': float(slice_index),
            'acquisition_number': 1,
            'acquisition_date': datetime.now().strftime('%Y%m%d'),
            'acquisition_time': datetime.now().strftime('%H%M%S'),
            'content_date': datetime.now().strftime('%Y%m%d'),
            'content_time': datetime.now().strftime('%H%M%S'),
        }
    
    def create_dicom_dataset(self, pixel_array: np.ndarray, slice_index: int, 
                           crop_region: Optional[tuple] = None) -> pydicom.Dataset:
        """
        创建新的DICOM数据集
        
        Args:
            pixel_array: 像素数组
            slice_index: 切片索引
            crop_region: 裁剪区域 (x, y, width, height)
            
        Returns:
            新的DICOM数据集
        """
        try:
            # 创建新的数据集
            ds = pydicom.Dataset()
            
            # 设置文件元信息
            file_meta = pydicom.Dataset()
            file_meta.MediaStorageSOPClassUID = '1.2.840.10008.5.1.4.1.1.2'
            file_meta.MediaStorageSOPInstanceUID = pydicom.uid.generate_uid()
            file_meta.ImplementationClassUID = '1.2.276.0.7230010.3.0.3.6.1'
            file_meta.TransferSyntaxUID = pydicom.uid.ImplicitVRLittleEndian
            ds.file_meta = file_meta
            
            # 设置基本元数据
            for key, value in self.base_metadata.items():
                if value is not None and value != '':
                    setattr(ds, self._convert_key_to_dicom_tag(key), value)
            
            # 设置切片特定元数据
            if slice_index < len(self.slice_metadata):
                slice_meta = self.slice_metadata[slice_index]
                for key, value in slice_meta.items():
                    if value is not None and value != '':
                        setattr(ds, self._convert_key_to_dicom_tag(key), value)
            
            # 设置图像数据
            ds.Rows = pixel_array.shape[0]
            ds.Columns = pixel_array.shape[1]
            ds.PixelData = pixel_array.tobytes()
            
            # 生成新的UID
            ds.SOPInstanceUID = pydicom.uid.generate_uid()
            
            # 如果有裁剪区域，更新几何信息
            if crop_region:
                self._update_geometry_for_crop(ds, crop_region)
            
            return ds
            
        except Exception as e:
            logger.error(f"创建DICOM数据集失败: {e}")
            raise
    
    def _convert_key_to_dicom_tag(self, key: str) -> str:
        """将键名转换为DICOM标签名"""
        # 简单的键名映射
        key_mapping = {
            'patient_id': 'PatientID',
            'patient_name': 'PatientName',
            'patient_birth_date': 'PatientBirthDate',
            'patient_sex': 'PatientSex',
            'study_date': 'StudyDate',
            'study_time': 'StudyTime',
            'study_description': 'StudyDescription',
            'study_instance_uid': 'StudyInstanceUID',
            'accession_number': 'AccessionNumber',
            'series_date': 'SeriesDate',
            'series_time': 'SeriesTime',
            'series_description': 'SeriesDescription',
            'series_instance_uid': 'SeriesInstanceUID',
            'series_number': 'SeriesNumber',
            'modality': 'Modality',
            'manufacturer': 'Manufacturer',
            'manufacturer_model_name': 'ManufacturerModelName',
            'station_name': 'StationName',
            'software_versions': 'SoftwareVersions',
            'institution_name': 'InstitutionName',
            'rows': 'Rows',
            'columns': 'Columns',
            'bits_allocated': 'BitsAllocated',
            'bits_stored': 'BitsStored',
            'high_bit': 'HighBit',
            'pixel_representation': 'PixelRepresentation',
            'photometric_interpretation': 'PhotometricInterpretation',
            'samples_per_pixel': 'SamplesPerPixel',
            'pixel_spacing': 'PixelSpacing',
            'slice_thickness': 'SliceThickness',
            'spacing_between_slices': 'SpacingBetweenSlices',
            'image_orientation_patient': 'ImageOrientationPatient',
            'image_position_patient': 'ImagePositionPatient',
            'slice_location': 'SliceLocation',
            'instance_number': 'InstanceNumber',
            'sop_instance_uid': 'SOPInstanceUID',
            'sop_class_uid': 'SOPClassUID',
            'acquisition_number': 'AcquisitionNumber',
            'acquisition_date': 'AcquisitionDate',
            'acquisition_time': 'AcquisitionTime',
            'content_date': 'ContentDate',
            'content_time': 'ContentTime',
            'window_center': 'WindowCenter',
            'window_width': 'WindowWidth',
            'rescale_intercept': 'RescaleIntercept',
            'rescale_slope': 'RescaleSlope',
            'rescale_type': 'RescaleType',
            'frame_of_reference_uid': 'FrameOfReferenceUID',
            'position_reference_indicator': 'PositionReferenceIndicator',
        }
        
        return key_mapping.get(key, key)
    
    def _update_geometry_for_crop(self, ds: pydicom.Dataset, crop_region: tuple):
        """更新裁剪后的几何信息"""
        try:
            x, y, width, height = crop_region
            
            # 更新图像位置
            if hasattr(ds, 'ImagePositionPatient') and ds.ImagePositionPatient:
                pixel_spacing = getattr(ds, 'PixelSpacing', [1.0, 1.0])
                original_pos = ds.ImagePositionPatient
                new_pos = [
                    float(original_pos[0]) + x * float(pixel_spacing[1]),
                    float(original_pos[1]) + y * float(pixel_spacing[0]),
                    float(original_pos[2])
                ]
                ds.ImagePositionPatient = new_pos
                
        except Exception as e:
            logger.warning(f"更新几何信息失败: {e}")
    
    def validate_metadata(self, ds: pydicom.Dataset) -> bool:
        """
        验证DICOM数据集的元数据完整性
        
        Args:
            ds: DICOM数据集
            
        Returns:
            验证是否通过
        """
        try:
            required_tags = [
                'PatientID', 'StudyInstanceUID', 'SeriesInstanceUID', 
                'SOPInstanceUID', 'SOPClassUID', 'Modality',
                'Rows', 'Columns', 'BitsAllocated', 'BitsStored',
                'HighBit', 'PixelRepresentation', 'PhotometricInterpretation'
            ]
            
            for tag in required_tags:
                if not hasattr(ds, tag) or getattr(ds, tag) is None:
                    logger.warning(f"缺少必需的DICOM标签: {tag}")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"验证元数据失败: {e}")
            return False
