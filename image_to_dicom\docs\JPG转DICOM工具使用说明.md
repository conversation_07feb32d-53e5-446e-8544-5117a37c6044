# JPG转DICOM GUI工具使用说明

## 概述

这是一个图形化的JPG图片转DICOM格式工具，专门为处理按特定命名规则的医学影像文件而设计。

## 功能特点

- **图形化界面**: 用户友好的GUI界面，支持文件拖拽选择
- **双转换模式**: 支持单帧模式和多帧模式
  - **单帧模式**: 每个JPG文件生成一个DICOM文件
  - **多帧模式**: 同序列的多个JPG文件合并为一个多帧DICOM文件
- **批量转换**: 支持同时转换多个JPG文件
- **智能文件名解析**: 自动解析文件名中的序列号和图片编号
- **DICOM标准兼容**: 生成符合DICOM标准的医学影像文件
- **实时进度显示**: 转换过程中显示实时进度和日志
- **参数自定义**: 可自定义患者信息和检查描述

## 文件命名规则

工具支持以下文件命名格式：
- **格式**: `00020001` (8位数字)
- **第4位数字**: 序列号 (例如: 000**2**0001 中的 2)
- **末位数字**: 该序列中的图片编号 (例如: 0002000**1** 中的 1)

示例文件名：
- `00010001.jpg` - 序列1，图片1
- `00010002.jpg` - 序列1，图片2
- `00020001.jpg` - 序列2，图片1
- `00030001.jpg` - 序列3，图片1

## 安装要求

### 系统要求
- Python 3.6 或更高版本
- Windows/Linux/macOS

### 依赖包
```bash
pip install PyQt5 Pillow pydicom numpy
```

或者使用项目的requirements.txt：
```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 启动程序

#### 方法一：使用启动脚本
```bash
python run_jpg_to_dicom_gui.py
```

#### 方法二：直接运行主程序
```bash
python jpg_to_dicom_gui.py
```

### 2. 操作步骤

1. **选择输入文件**
   - 点击"选择JPG文件"按钮
   - 在文件对话框中选择要转换的JPG文件
   - 支持多选（按住Ctrl键选择多个文件）
   - 已选择的文件会显示在文件列表中

2. **设置输出目录**
   - 点击"浏览"按钮选择输出目录
   - 或直接在文本框中输入目录路径
   - 如果目录不存在，程序会自动创建

3. **配置DICOM参数**
   - **患者姓名**: 设置DICOM文件中的患者姓名（默认：Anonymous）
   - **检查描述**: 设置检查描述信息（默认：JPG to DICOM Conversion）

4. **选择转换模式**
   - **单帧模式**: 每个JPG文件生成一个DICOM文件
     - 例如：00010001.jpg → 00010001.dcm
   - **多帧模式**: 同序列的JPG文件合并为一个多帧DICOM文件
     - 例如：00010001.jpg + 00010002.jpg + 00010003.jpg → series_001_multiframe.dcm

5. **开始转换**
   - 点击"开始转换"按钮
   - 观察进度条和日志信息
   - 转换完成后会显示成功消息

### 3. 界面说明

#### 左侧控制面板
- **文件选择区域**: 选择输入文件和输出目录
- **DICOM参数区域**: 配置患者信息和检查描述
- **转换模式区域**: 选择单帧或多帧转换模式
- **转换控制区域**: 开始转换和进度显示

#### 右侧日志面板
- **实时日志**: 显示转换过程中的详细信息
- **时间戳**: 每条日志都带有时间戳
- **清除日志**: 可以清除当前日志内容

## 输出文件格式

### 单帧模式输出
- **文件命名**: 保持原JPG文件名，扩展名改为.dcm
- **文件数量**: 与输入JPG文件数量相同
- **帧数**: 每个DICOM文件包含1帧

### 多帧模式输出
- **文件命名**: series_XXX_multiframe.dcm (XXX为序列号)
- **文件数量**: 按序列号分组，每个序列生成一个文件
- **帧数**: 每个DICOM文件包含该序列的所有图片帧

### DICOM文件信息
转换后的DICOM文件包含以下信息：

- **文件格式**: .dcm
- **图像格式**: MONOCHROME2 (灰度)
- **位深度**: 8位
- **模态**: CR (计算机放射摄影)
- **检查部位**: CHEST (胸部)

### 自动生成的标识符
- **患者ID**: 基于转换时间自动生成
- **检查实例UID**: 基于转换时间自动生成
- **序列实例UID**: 基于序列号和转换时间生成
- **SOP实例UID**: 基于序列号、实例号和转换时间生成

## 测试工具

### 创建测试文件
```bash
python test_jpg_to_dicom_gui.py --sample-only
```
这会在当前目录创建 `sample_jpg_files` 文件夹，包含示例JPG文件用于测试。

### 完整测试
```bash
python test_jpg_to_dicom_gui.py
```
这会创建测试文件，启动GUI，并在转换完成后验证结果。

## 故障排除

### 常见问题

1. **程序无法启动**
   - 检查Python版本是否为3.6+
   - 确认所有依赖包已正确安装
   - 运行 `python run_jpg_to_dicom_gui.py` 查看详细错误信息

2. **文件转换失败**
   - 确认输入文件为有效的图像文件
   - 检查输出目录是否有写入权限
   - 查看日志面板中的错误信息

3. **DICOM文件无法打开**
   - 使用DICOM查看器（如RadiAnt、OsiriX）验证文件
   - 检查文件是否完整生成
   - 确认转换过程中没有错误

### 日志信息说明

- `✓ 转换成功`: 文件转换成功
- `✗ 转换失败`: 文件转换失败，后面会显示具体错误
- `[时间戳]`: 每条日志的时间戳

## 技术细节

### 支持的图像格式
- JPG/JPEG
- PNG
- BMP
- 其他PIL支持的格式

### DICOM标准兼容性
- 符合DICOM 3.0标准
- 使用隐式VR小端传输语法
- 包含必要的DICOM标签

### 性能特点
- 多线程转换，不阻塞界面
- 内存优化，适合大批量文件处理
- 实时进度反馈

## 版本信息

- **版本**: 2.1.0 (3D Slicer兼容性修复版)
- **作者**: Augment Agent
- **更新日期**: 2025-07-16

## 修复内容 (v2.0.0)

### 🔧 主要修复
1. **像素数据处理优化**
   - 使用numpy数组确保像素数据格式正确
   - 修复PIL Image转换可能导致的数据损坏问题

2. **DICOM标签完善**
   - 添加了所有必要的DICOM标签
   - 包含患者信息、检查信息、设备信息等完整标签
   - 符合DICOM 3.0标准要求

3. **UID生成改进**
   - 使用更精确的时间戳和微秒级精度
   - 避免批量转换时UID重复的问题
   - 使用标准的UID前缀

4. **SOP类优化**
   - 单帧模式：使用Secondary Capture Image Storage (1.2.840.10008.5.1.4.1.1.7)
   - 多帧模式：使用适合的多帧SOP类
   - 提高DICOM查看器兼容性

5. **文件验证功能**
   - 自动验证生成的DICOM文件完整性
   - 检查必要标签和像素数据
   - 确保文件符合DICOM标准

6. **错误处理增强**
   - 详细的错误信息和调试信息
   - 文件权限和路径验证
   - 更好的异常处理机制

### 🎯 修复的问题
- ✅ DICOM查看器无法正确显示图像
- ✅ 像素数据大小不匹配
- ✅ 缺少必要的DICOM标签
- ✅ UID重复导致的冲突
- ✅ 多帧DICOM不符合标准
- ✅ PACS系统导入失败

### 📈 性能改进
- 更稳定的转换过程
- 更好的内存管理
- 增强的错误恢复能力

## 3D Slicer兼容性修复 (v2.1.0)

### 🎯 解决的3D Slicer问题
- ✅ **序列分组错误**：修复了281张图片被错误分成多个序列的问题
- ✅ **切片间距警告**：解决了"Image slices are not equally spaced"警告
- ✅ **切片排序错误**：确保图片按正确顺序排列
- ✅ **UID一致性**：同一批次转换的文件使用相同的Study和Series UID

### 🔧 技术修复详情
1. **改进文件名解析逻辑**
   - 对于大量文件（如281张），自动识别为同一序列
   - 智能判断文件命名模式，优先保证序列连续性
   - 按文件名数字顺序正确排序

2. **优化切片位置计算**
   - 使用排序后的索引作为切片位置
   - 确保切片间距一致（1.0mm）
   - 添加SpacingBetweenSlices标签

3. **统一UID管理**
   - 同一批次转换使用相同的Study Instance UID
   - 同一序列使用相同的Series Instance UID
   - 确保Patient ID一致性

4. **实例号连续性**
   - 实例号按转换顺序连续递增
   - 避免基于文件名末位数字导致的跳跃

### 🏥 3D Slicer使用效果
修复后，您的281张图片在3D Slicer中将：
- 显示为单一序列（如：`5: Converted Series 5 [标量体积]`）
- 不会出现切片间距警告
- 图片按正确的解剖顺序排列
- 可以正常进行3D重建和分析

## 许可证

本工具仅供学习和研究使用，请遵守相关法律法规和医学影像处理的规范要求。
