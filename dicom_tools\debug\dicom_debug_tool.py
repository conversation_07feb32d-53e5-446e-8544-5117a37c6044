#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICOM文件调试工具
用于分析DICOM文件的排序问题

作者: AI Assistant
日期: 2025-01-17
"""

import os
import sys
import pydicom
from pathlib import Path
import argparse
from typing import List, Tuple
import csv


def get_dicom_files(directory: str) -> List[str]:
    """获取目录中的所有DICOM文件"""
    dicom_files = []
    directory = Path(directory)
    
    for file_path in directory.iterdir():
        if file_path.is_file():
            try:
                pydicom.dcmread(str(file_path), stop_before_pixels=True)
                dicom_files.append(str(file_path))
            except:
                continue
    
    return dicom_files


def analyze_dicom_files(directory: str, output_csv: str = None):
    """分析DICOM文件的详细信息"""
    
    print(f"正在分析目录: {directory}")
    
    # 获取所有DICOM文件
    dicom_files = get_dicom_files(directory)
    if not dicom_files:
        print("未找到DICOM文件！")
        return
    
    print(f"找到 {len(dicom_files)} 个DICOM文件")
    
    # 分析每个文件
    file_info = []
    
    for i, file_path in enumerate(dicom_files):
        try:
            ds = pydicom.dcmread(file_path, stop_before_pixels=True)
            
            filename = os.path.basename(file_path)
            instance_number = getattr(ds, 'InstanceNumber', None)
            slice_location = getattr(ds, 'SliceLocation', None)
            
            # 获取Image Position Patient
            image_position = getattr(ds, 'ImagePositionPatient', None)
            if image_position and len(image_position) >= 3:
                image_pos_z = float(image_position[2])
            else:
                image_pos_z = None
            
            # 获取其他可能的位置信息
            slice_thickness = getattr(ds, 'SliceThickness', None)
            spacing_between_slices = getattr(ds, 'SpacingBetweenSlices', None)
            
            # 获取序列信息
            series_number = getattr(ds, 'SeriesNumber', None)
            series_description = getattr(ds, 'SeriesDescription', '')
            
            # 获取采集信息
            acquisition_number = getattr(ds, 'AcquisitionNumber', None)
            
            file_info.append({
                'filename': filename,
                'instance_number': instance_number,
                'slice_location': slice_location,
                'image_pos_z': image_pos_z,
                'slice_thickness': slice_thickness,
                'spacing_between_slices': spacing_between_slices,
                'series_number': series_number,
                'series_description': series_description,
                'acquisition_number': acquisition_number,
                'file_path': file_path
            })
            
            if (i + 1) % 100 == 0:
                print(f"  已分析 {i + 1}/{len(dicom_files)} 个文件...")
                
        except Exception as e:
            print(f"错误: 无法读取文件 {file_path}: {e}")
            continue
    
    if not file_info:
        print("无法读取任何DICOM文件信息！")
        return
    
    # 分析结果
    print(f"\n=== 分析结果 ===")
    print(f"成功分析: {len(file_info)} 个文件")
    
    # 统计Instance Number
    instance_numbers = [info['instance_number'] for info in file_info if info['instance_number'] is not None]
    if instance_numbers:
        print(f"Instance Number 范围: {min(instance_numbers)} - {max(instance_numbers)}")
        print(f"Instance Number 数量: {len(set(instance_numbers))} 个唯一值")
        if len(set(instance_numbers)) != len(instance_numbers):
            print(f"警告: 发现重复的Instance Number!")
    
    # 统计位置信息
    slice_locations = [info['slice_location'] for info in file_info if info['slice_location'] is not None]
    image_pos_zs = [info['image_pos_z'] for info in file_info if info['image_pos_z'] is not None]
    
    print(f"有Slice Location的文件: {len(slice_locations)} 个")
    print(f"有Image Position Patient Z坐标的文件: {len(image_pos_zs)} 个")
    
    if slice_locations:
        print(f"Slice Location 范围: {min(slice_locations):.2f} - {max(slice_locations):.2f}")
    
    if image_pos_zs:
        print(f"Image Position Z 范围: {min(image_pos_zs):.2f} - {max(image_pos_zs):.2f}")
    
    # 按不同方式排序并显示
    print(f"\n=== 排序分析 ===")
    
    # 1. 按文件名排序（当前问题的排序方式）
    filename_sorted = sorted(file_info, key=lambda x: x['filename'])
    print(f"\n1. 按文件名排序 (当前问题的顺序):")
    for i in range(min(20, len(filename_sorted))):
        info = filename_sorted[i]
        print(f"  {i+1:3d}. {info['filename']} (Instance: {info['instance_number']}, "
              f"SliceLoc: {info['slice_location']}, ImgPosZ: {info['image_pos_z']})")
    if len(filename_sorted) > 20:
        print(f"  ... 还有 {len(filename_sorted)-20} 个文件")
    
    # 2. 按Instance Number排序
    instance_sorted = sorted([info for info in file_info if info['instance_number'] is not None], 
                           key=lambda x: x['instance_number'])
    print(f"\n2. 按Instance Number排序:")
    for i in range(min(20, len(instance_sorted))):
        info = instance_sorted[i]
        print(f"  {i+1:3d}. {info['filename']} (Instance: {info['instance_number']}, "
              f"SliceLoc: {info['slice_location']}, ImgPosZ: {info['image_pos_z']})")
    if len(instance_sorted) > 20:
        print(f"  ... 还有 {len(instance_sorted)-20} 个文件")
    
    # 3. 按Slice Location排序（推荐的排序方式）
    if slice_locations:
        slice_sorted = sorted([info for info in file_info if info['slice_location'] is not None], 
                            key=lambda x: x['slice_location'])
        print(f"\n3. 按Slice Location排序 (推荐的正确顺序):")
        for i in range(min(20, len(slice_sorted))):
            info = slice_sorted[i]
            print(f"  {i+1:3d}. {info['filename']} (Instance: {info['instance_number']}, "
                  f"SliceLoc: {info['slice_location']}, ImgPosZ: {info['image_pos_z']})")
        if len(slice_sorted) > 20:
            print(f"  ... 还有 {len(slice_sorted)-20} 个文件")
    
    # 4. 按Image Position Z排序
    if image_pos_zs:
        imgpos_sorted = sorted([info for info in file_info if info['image_pos_z'] is not None], 
                             key=lambda x: x['image_pos_z'])
        print(f"\n4. 按Image Position Z排序:")
        for i in range(min(20, len(imgpos_sorted))):
            info = imgpos_sorted[i]
            print(f"  {i+1:3d}. {info['filename']} (Instance: {info['instance_number']}, "
                  f"SliceLoc: {info['slice_location']}, ImgPosZ: {info['image_pos_z']})")
        if len(imgpos_sorted) > 20:
            print(f"  ... 还有 {len(imgpos_sorted)-20} 个文件")
    
    # 保存到CSV文件
    if output_csv:
        print(f"\n正在保存详细信息到: {output_csv}")
        with open(output_csv, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['filename', 'instance_number', 'slice_location', 'image_pos_z', 
                         'slice_thickness', 'spacing_between_slices', 'series_number', 
                         'series_description', 'acquisition_number', 'file_path']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            for info in file_info:
                writer.writerow(info)
        print(f"详细信息已保存到: {output_csv}")
    
    # 给出建议
    print(f"\n=== 建议 ===")
    if slice_locations:
        print("✓ 建议使用Slice Location进行排序")
    elif image_pos_zs:
        print("✓ 建议使用Image Position Patient Z坐标进行排序")
    else:
        print("⚠ 警告: 缺少位置信息，可能需要使用Instance Number排序")
    
    print("\n使用修复后的工具:")
    print(f"  python dicom_renumber_tool.py \"{directory}\"")
    print(f"  python dicom_renumber_gui.py")


def main():
    parser = argparse.ArgumentParser(description='DICOM文件调试分析工具')
    parser.add_argument('input_dir', help='输入DICOM文件目录')
    parser.add_argument('-o', '--output', help='输出CSV文件路径（可选）')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.input_dir):
        print(f"错误: 目录不存在: {args.input_dir}")
        sys.exit(1)
    
    try:
        analyze_dicom_files(args.input_dir, args.output)
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
