#!/usr/bin/env python3
"""
图片转DICOM工具启动脚本

使用方法：
python run_image_to_dicom.py
"""

import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)

def main():
    """启动图片转DICOM GUI应用"""
    try:
        from image_to_dicom.gui.enhanced_jpg_to_dicom_gui import main as gui_main
        print("正在启动图片转DICOM工具...")
        gui_main()
    except ImportError as e:
        print(f"导入错误: {e}")
        print("请确保已安装所有依赖包：pip install -r requirements.txt")
        return 1
    except Exception as e:
        print(f"启动失败: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
