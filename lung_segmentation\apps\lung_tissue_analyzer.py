#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
肺部组织成分分析GUI工具
基于CT值范围对肺内组织进行分类和统计分析
"""

import sys
import os
import logging
import numpy as np
import SimpleITK as sitk
from pathlib import Path
import traceback
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D
from mpl_toolkits.mplot3d.art3d import Poly3DCollection
from skimage import measure
import pandas as pd

os.environ["KMP_DUPLICATE_LIB_OK"] = "TRUE"

from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QLineEdit, QPushButton, QFileDialog, QComboBox,
    QCheckBox, QSpinBox, QMessageBox, QGroupBox, QRadioButton,
    QButtonGroup, QTabWidget, QTextEdit, QSp<PERSON>ter, QSlider, QProgressBar,
    QDoubleSpinBox, QListWidget, QBoxLayout, QTableWidget, QTableWidgetItem,
    QScrollArea, QFrame
)
from PyQt5.QtCore import Qt, QThread, pyqtSignal, QTimer
from PyQt5.QtGui import QFont, QPixmap, QPainter, QColor

# 忽略PyQt5的弃用警告
import warnings
warnings.filterwarnings("ignore", category=DeprecationWarning)

import matplotlib
matplotlib.use('Qt5Agg')
matplotlib.rcParams['font.sans-serif'] = ['SimHei']  # 使用黑体显示中文
matplotlib.rcParams['axes.unicode_minus'] = False  # 正常显示负号
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent.parent))
# 导入lung_ct_segmenter库
from shared_utils.common.io_utils import load_image

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('lung_tissue_analyzer.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

# 全局异常处理函数
def global_exception_handler(exctype, value, tb):
    """全局异常处理器"""
    error_msg = ''.join(traceback.format_exception(exctype, value, tb))
    logger.error(f"未捕获的异常: {error_msg}")
    QMessageBox.critical(None, "程序错误", 
                        f"程序发生未处理的异常:\n{str(value)}\n\n详细信息已记录到日志文件。")
    # 调用原始的异常处理器
    sys.__excepthook__(exctype, value, tb)

# 设置全局异常处理器
sys.excepthook = global_exception_handler

class LungTissueAnalyzer:
    """肺部组织成分分析器"""
    
    # CT值范围定义（基于文档）
    HU_RANGES = {
        'bulla': (-1000, -950),      # 肺大泡/重度肺气肿
        'inflated': (-950, -750),    # 正常肺泡区
        'infiltrated': (-750, -400), # 炎性/间质浸润区
        'collapsed': (-400, 0),      # 实变/不张
        'vessels': (0, 1000)         # 血管与软组织
    }
    
    # 肺气肿严重程度分级（LAA%）
    EMPHYSEMA_GRADES = {
        'normal': (0, 5, '正常范围或轻微肺气肿'),
        'mild': (5, 10, '轻度肺气肿'),
        'moderate': (10, 25, '中度肺气肿'),
        'severe': (25, 35, '重度肺气肿'),
        'very_severe': (35, 100, '极重度，可能存在呼吸衰竭风险；需干预')
    }
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
    def analyze_lung_tissue(self, image_data: np.ndarray, lung_mask: np.ndarray, 
                           spacing: tuple) -> dict:
        """
        分析肺部组织成分
        
        Args:
            image_data: CT图像数据
            lung_mask: 肺部分割掩码
            spacing: 体素间距 (x, y, z)
            
        Returns:
            分析结果字典
        """
        try:
            self.logger.info("开始肺部组织成分分析...")
            
            # 提取肺部区域的CT值
            lung_ct_values = image_data[lung_mask > 0]
            
            if len(lung_ct_values) == 0:
                raise ValueError("肺部掩码为空，无法进行分析")
            
            # 计算体素体积（立方厘米）
            voxel_volume_cm3 = np.prod(spacing) / 1000  # 从mm³转换为cm³
            
            # 分析各组织成分
            tissue_analysis = {}
            total_lung_voxels = len(lung_ct_values)
            
            for tissue_name, (min_hu, max_hu) in self.HU_RANGES.items():
                tissue_mask = (lung_ct_values >= min_hu) & (lung_ct_values < max_hu)
                tissue_voxels = np.sum(tissue_mask)
                tissue_volume_cm3 = tissue_voxels * voxel_volume_cm3
                tissue_percentage = (tissue_voxels / total_lung_voxels) * 100
                
                tissue_analysis[tissue_name] = {
                    'voxels': tissue_voxels,
                    'volume_cm3': tissue_volume_cm3,
                    'percentage': tissue_percentage,
                    'hu_range': (min_hu, max_hu)
                }
            
            # 计算总肺体积
            total_lung_volume_cm3 = total_lung_voxels * voxel_volume_cm3
            
            # CT值统计
            ct_stats = {
                'min': float(np.min(lung_ct_values)),
                'max': float(np.max(lung_ct_values)),
                'mean': float(np.mean(lung_ct_values)),
                'std': float(np.std(lung_ct_values)),
                'percentile_5': float(np.percentile(lung_ct_values, 5)),
                'percentile_95': float(np.percentile(lung_ct_values, 95)),
                'median': float(np.median(lung_ct_values))
            }
            
            # 肺气肿严重程度评估（LAA%）
            laa_percentage = tissue_analysis['bulla']['percentage']
            emphysema_grade = self._assess_emphysema_severity(laa_percentage)
            
            results = {
                'tissue_analysis': tissue_analysis,
                'total_lung_volume_cm3': total_lung_volume_cm3,
                'total_lung_voxels': total_lung_voxels,
                'voxel_volume_cm3': voxel_volume_cm3,
                'ct_stats': ct_stats,
                'laa_percentage': laa_percentage,
                'emphysema_grade': emphysema_grade,
                'spacing': spacing
            }
            
            self.logger.info(f"肺部组织分析完成，总肺体积: {total_lung_volume_cm3:.2f} cm³")
            self.logger.info(f"LAA%: {laa_percentage:.2f}%, 肺气肿程度: {emphysema_grade['description']}")
            
            return results
            
        except Exception as e:
            self.logger.error(f"肺部组织分析失败: {e}")
            raise
    
    def _assess_emphysema_severity(self, laa_percentage: float) -> dict:
        """
        评估肺气肿严重程度
        
        Args:
            laa_percentage: LAA百分比
            
        Returns:
            肺气肿严重程度信息
        """
        for grade, (min_pct, max_pct, description) in self.EMPHYSEMA_GRADES.items():
            if min_pct <= laa_percentage < max_pct:
                return {
                    'grade': grade,
                    'range': (min_pct, max_pct),
                    'description': description,
                    'percentage': laa_percentage
                }
        
        # 如果超出范围，返回最严重级别
        return {
            'grade': 'very_severe',
            'range': (35, 100),
            'description': '极重度，可能存在呼吸衰竭风险；需干预',
            'percentage': laa_percentage
        }
    
    def generate_3d_reconstruction(self, image_data: np.ndarray, lung_mask: np.ndarray, 
                                 spacing: tuple, tissue_analysis: dict) -> Figure:
        """
        生成肺部三维重建图像
        
        Args:
            image_data: CT图像数据
            lung_mask: 肺部分割掩码
            spacing: 体素间距
            tissue_analysis: 组织分析结果
            
        Returns:
            matplotlib Figure对象
        """
        try:
            self.logger.info("开始生成三维重建...")
            
            fig = Figure(figsize=(12, 8))
            
            # 创建子图
            ax1 = fig.add_subplot(121, projection='3d')
            ax2 = fig.add_subplot(122)
            
            # 1. 肺部轮廓三维重建
            try:
                # 检查掩码数据
                self.logger.info(f"肺部掩码形状: {lung_mask.shape}")
                self.logger.info(f"掩码唯一值: {np.unique(lung_mask)}")
                self.logger.info(f"掩码非零体素数: {np.sum(lung_mask > 0)}")
                
                # 确保掩码是二值化的
                binary_mask = (lung_mask > 0).astype(np.uint8)
                
                # 检查是否有足够的数据进行重建
                if np.sum(binary_mask) < 1000:
                    raise ValueError(f"掩码数据太少，无法进行三维重建 (只有 {np.sum(binary_mask)} 个体素)")
                
                # 降采样以提高性能
                downsample_factor = 4  # 增加降采样因子以提高性能
                downsampled_mask = binary_mask[::downsample_factor, ::downsample_factor, ::downsample_factor]
                downsampled_spacing = tuple(s * downsample_factor for s in spacing)
                
                self.logger.info(f"降采样后掩码形状: {downsampled_mask.shape}")
                self.logger.info(f"降采样后非零体素数: {np.sum(downsampled_mask > 0)}")
                
                # 使用marching cubes算法生成表面
                self.logger.info("开始执行marching cubes算法...")
                verts, faces, normals, values = measure.marching_cubes(
                    downsampled_mask, 
                    level=0.5, 
                    spacing=downsampled_spacing,
                    allow_degenerate=False
                )
                
                self.logger.info(f"生成的顶点数: {len(verts)}, 面数: {len(faces)}")
                
                if len(verts) == 0 or len(faces) == 0:
                    raise ValueError("Marching cubes算法未生成任何表面")
                
                # 创建3D多边形集合
                mesh = Poly3DCollection(verts[faces], alpha=0.4, facecolor='lightblue', edgecolor='darkblue', linewidth=0.1)
                ax1.add_collection3d(mesh)
                
                # 设置坐标轴范围（基于实际顶点范围）
                if len(verts) > 0:
                    x_min, x_max = verts[:, 0].min(), verts[:, 0].max()
                    y_min, y_max = verts[:, 1].min(), verts[:, 1].max()
                    z_min, z_max = verts[:, 2].min(), verts[:, 2].max()
                    
                    # 添加一些边距
                    x_margin = (x_max - x_min) * 0.1
                    y_margin = (y_max - y_min) * 0.1
                    z_margin = (z_max - z_min) * 0.1
                    
                    ax1.set_xlim(x_min - x_margin, x_max + x_margin)
                    ax1.set_ylim(y_min - y_margin, y_max + y_margin)
                    ax1.set_zlim(z_min - z_margin, z_max + z_margin)
                else:
                    # 备用方案：使用掩码尺寸
                    ax1.set_xlim(0, downsampled_mask.shape[2] * downsampled_spacing[2])
                    ax1.set_ylim(0, downsampled_mask.shape[1] * downsampled_spacing[1])
                    ax1.set_zlim(0, downsampled_mask.shape[0] * downsampled_spacing[0])
                
                ax1.set_xlabel('X (mm)')
                ax1.set_ylabel('Y (mm)')
                ax1.set_zlabel('Z (mm)')
                ax1.set_title('肺部三维重建')
                
                # 设置视角
                ax1.view_init(elev=20, azim=45)
                
                self.logger.info("三维重建成功完成")
                
            except Exception as e:
                self.logger.error(f"三维重建失败: {e}")
                self.logger.error(f"错误详情: {traceback.format_exc()}")
                
                # 显示错误信息和简化的体素可视化
                ax1.clear()
                ax1.text(0.5, 0.5, 0.5, f'三维重建失败\n{str(e)}\n\n尝试简化显示...', 
                        transform=ax1.transAxes, ha='center', va='center', fontsize=10)
                
                # 尝试简化的体素显示
                try:
                    # 大幅降采样
                    simple_downsample = 8
                    simple_mask = lung_mask[::simple_downsample, ::simple_downsample, ::simple_downsample]
                    
                    if np.sum(simple_mask > 0) > 0:
                        # 找到非零体素的坐标
                        z, y, x = np.where(simple_mask > 0)
                        
                        # 只显示部分点以避免过于密集
                        if len(x) > 1000:
                            indices = np.random.choice(len(x), 1000, replace=False)
                            x, y, z = x[indices], y[indices], z[indices]
                        
                        # 转换为实际坐标
                        x_coords = x * spacing[2] * simple_downsample
                        y_coords = y * spacing[1] * simple_downsample
                        z_coords = z * spacing[0] * simple_downsample
                        
                        ax1.scatter(x_coords, y_coords, z_coords, c='blue', alpha=0.6, s=1)
                        ax1.set_xlabel('X (mm)')
                        ax1.set_ylabel('Y (mm)')
                        ax1.set_zlabel('Z (mm)')
                        ax1.set_title('肺部体素分布（简化显示）')
                        
                        self.logger.info("使用简化体素显示")
                    
                except Exception as simple_e:
                    self.logger.error(f"简化显示也失败: {simple_e}")
                    ax1.text(0.5, 0.5, 0.5, '无法显示三维数据\n请检查掩码文件', 
                            transform=ax1.transAxes, ha='center', va='center')
            
            # 2. 组织成分饼图
            tissue_names = []
            tissue_percentages = []
            colors = ['red', 'lightgreen', 'orange', 'purple', 'brown']
            
            for i, (tissue_name, analysis) in enumerate(tissue_analysis.items()):
                if analysis['percentage'] > 0.1:  # 只显示占比大于0.1%的组织
                    tissue_names.append(f"{tissue_name}\n({analysis['percentage']:.1f}%)")
                    tissue_percentages.append(analysis['percentage'])
            
            if tissue_percentages:
                wedges, texts, autotexts = ax2.pie(tissue_percentages, labels=tissue_names, 
                                                  colors=colors[:len(tissue_percentages)],
                                                  autopct='%1.1f%%', startangle=90)
                ax2.set_title('肺部组织成分分布')
            
            fig.suptitle('肺部组织成分三维分析', fontsize=16, fontweight='bold')
            fig.tight_layout()
            
            self.logger.info("三维重建生成完成")
            return fig
            
        except Exception as e:
            self.logger.error(f"三维重建生成失败: {e}")
            # 返回空图
            fig = Figure(figsize=(12, 8))
            ax = fig.add_subplot(111)
            ax.text(0.5, 0.5, f'三维重建失败:\n{str(e)}', 
                   transform=ax.transAxes, ha='center', va='center')
            return fig

class AnalysisThread(QThread):
    """分析线程，用于在后台执行组织分析"""
    finished = pyqtSignal(dict)  # 完成信号，传递分析结果
    error = pyqtSignal(str)      # 错误信号，传递错误消息
    progress = pyqtSignal(str)   # 进度信号，传递进度消息
    
    def __init__(self, analyzer, image_data, lung_mask, spacing):
        super().__init__()
        self.analyzer = analyzer
        self.image_data = image_data
        self.lung_mask = lung_mask
        self.spacing = spacing
    
    def run(self):
        """执行分析操作"""
        try:
            self.progress.emit("开始肺部组织分析...")
            
            # 执行组织分析
            results = self.analyzer.analyze_lung_tissue(
                self.image_data, self.lung_mask, self.spacing
            )
            
            self.progress.emit("生成三维重建...")
            
            # 生成三维重建
            reconstruction_fig = self.analyzer.generate_3d_reconstruction(
                self.image_data, self.lung_mask, self.spacing, results['tissue_analysis']
            )
            
            results['reconstruction_fig'] = reconstruction_fig
            
            self.progress.emit("分析完成！")
            self.finished.emit(results)
            
        except Exception as e:
            error_msg = f"分析过程中发生错误: {str(e)}"
            logger.error(error_msg)
            logger.error(traceback.format_exc())
            self.error.emit(error_msg)

class ImageViewer(QWidget):
    """图像查看器组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.figure = Figure(figsize=(8, 6), dpi=100)
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        
        # 设置图像属性
        self.image = None
        self.mask = None
        self.slice_index = 0
        self.total_slices = 0
        
        # 窗宽窗位设置
        self.window_width = 1400  # 默认窗宽
        self.window_center = -300  # 默认窗位
        
        # 创建布局
        layout = QVBoxLayout()
        layout.addWidget(self.canvas, 1)
        
        # 添加控制面板
        control_panel = QWidget()
        control_layout = QVBoxLayout(control_panel)
        
        # 切片滑条
        slice_layout = QHBoxLayout()
        slice_layout.addWidget(QLabel("切片:"))
        self.slice_label = QLabel("0/0")
        self.slice_label.setMinimumWidth(50)
        slice_layout.addWidget(self.slice_label)
        self.slice_slider = QSlider(Qt.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.valueChanged.connect(self.on_slice_changed)
        slice_layout.addWidget(self.slice_slider)
        control_layout.addLayout(slice_layout)
        
        # 窗宽窗位控制
        window_layout = QHBoxLayout()
        window_layout.addWidget(QLabel("窗宽:"))
        self.window_width_slider = QSlider(Qt.Horizontal)
        self.window_width_slider.setMinimum(1)
        self.window_width_slider.setMaximum(4000)
        self.window_width_slider.setValue(self.window_width)
        self.window_width_slider.valueChanged.connect(self.on_window_width_changed)
        window_layout.addWidget(self.window_width_slider)
        
        window_layout.addWidget(QLabel("窗位:"))
        self.window_center_slider = QSlider(Qt.Horizontal)
        self.window_center_slider.setMinimum(-1000)
        self.window_center_slider.setMaximum(1000)
        self.window_center_slider.setValue(self.window_center)
        self.window_center_slider.valueChanged.connect(self.on_window_center_changed)
        window_layout.addWidget(self.window_center_slider)
        control_layout.addLayout(window_layout)
        
        control_panel.setMaximumHeight(100)
        layout.addWidget(control_panel)
        
        self.setLayout(layout)
    
    def set_image(self, image, mask=None):
        """设置要显示的图像和掩码"""
        try:
            self.image = image
            self.mask = mask
            
            if image is not None:
                self.total_slices = image.shape[0]
                self.slice_index = self.total_slices // 2
                
                # 更新切片滑条
                self.slice_slider.setMaximum(self.total_slices - 1)
                self.slice_slider.setValue(self.slice_index)
                self.slice_label.setText(f"{self.slice_index + 1}/{self.total_slices}")
                
                # 更新显示
                self.update_display()
            else:
                # 清空显示
                self.ax.clear()
                self.ax.set_title("无图像")
                self.ax.axis('off')
                self.canvas.draw()
                
        except Exception as e:
            logger.error(f"设置图像时出错: {e}")
    
    def update_display(self):
        """更新显示"""
        try:
            if self.image is None:
                return
            
            self.ax.clear()
            
            # 应用窗宽窗位
            display_image = self._apply_window(self.image[self.slice_index])
            
            # 显示图像
            self.ax.imshow(display_image, cmap='gray', origin='lower')
            
            # 如果有掩码，叠加显示
            if self.mask is not None:
                mask_slice = self.mask[self.slice_index]
                if np.any(mask_slice > 0):
                    masked_array = np.ma.masked_where(mask_slice == 0, mask_slice)
                    self.ax.imshow(masked_array, cmap='jet', alpha=0.3, origin='lower')
            
            self.ax.set_title(f"切片 {self.slice_index + 1}/{self.total_slices}")
            self.ax.axis('off')
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"更新显示时出错: {e}")
    
    def _apply_window(self, image):
        """应用窗宽窗位"""
        min_val = self.window_center - self.window_width // 2
        max_val = self.window_center + self.window_width // 2
        
        windowed = np.clip(image, min_val, max_val)
        windowed = (windowed - min_val) / (max_val - min_val)
        
        return windowed
    
    def on_slice_changed(self, value):
        """切片改变事件"""
        self.slice_index = value
        self.slice_label.setText(f"{self.slice_index + 1}/{self.total_slices}")
        self.update_display()
    
    def on_window_width_changed(self, value):
        """窗宽改变事件"""
        self.window_width = value
        self.update_display()
    
    def on_window_center_changed(self, value):
        """窗位改变事件"""
        self.window_center = value
        self.update_display()

class ResultsViewer(QWidget):
    """结果查看器组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        layout = QVBoxLayout()
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        
        # 统计结果标签页
        self.stats_tab = QWidget()
        self.init_stats_tab()
        self.tab_widget.addTab(self.stats_tab, "统计结果")
        
        # 三维重建标签页
        self.reconstruction_tab = QWidget()
        self.init_reconstruction_tab()
        self.tab_widget.addTab(self.reconstruction_tab, "三维重建")
        
        # CT值分布标签页
        self.distribution_tab = QWidget()
        self.init_distribution_tab()
        self.tab_widget.addTab(self.distribution_tab, "CT值分布")
        
        layout.addWidget(self.tab_widget)
        self.setLayout(layout)
    
    def init_stats_tab(self):
        """初始化统计结果标签页"""
        layout = QVBoxLayout()
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_widget = QWidget()
        scroll_layout = QVBoxLayout(scroll_widget)
        
        # 总体信息
        self.general_info_label = QLabel("请先加载图像并进行分析")
        self.general_info_label.setFont(QFont("Arial", 12, QFont.Bold))
        scroll_layout.addWidget(self.general_info_label)
        
        # 组织成分表格
        self.tissue_table = QTableWidget()
        self.tissue_table.setColumnCount(4)
        self.tissue_table.setHorizontalHeaderLabels(["组织类型", "体积(cm³)", "占比(%)", "CT值范围(HU)"])
        scroll_layout.addWidget(self.tissue_table)
        
        # CT值统计表格
        self.ct_stats_table = QTableWidget()
        self.ct_stats_table.setColumnCount(2)
        self.ct_stats_table.setHorizontalHeaderLabels(["统计指标", "数值"])
        scroll_layout.addWidget(self.ct_stats_table)
        
        # 肺气肿评估
        self.emphysema_label = QLabel()
        self.emphysema_label.setFont(QFont("Arial", 11, QFont.Bold))
        self.emphysema_label.setStyleSheet("color: red; padding: 10px; border: 1px solid red; border-radius: 5px;")
        scroll_layout.addWidget(self.emphysema_label)
        
        scroll_area.setWidget(scroll_widget)
        layout.addWidget(scroll_area)
        self.stats_tab.setLayout(layout)
    
    def init_reconstruction_tab(self):
        """初始化三维重建标签页"""
        layout = QVBoxLayout()
        
        # 三维重建画布
        self.reconstruction_figure = Figure(figsize=(12, 8))
        self.reconstruction_canvas = FigureCanvas(self.reconstruction_figure)
        layout.addWidget(self.reconstruction_canvas)
        
        self.reconstruction_tab.setLayout(layout)
    
    def init_distribution_tab(self):
        """初始化CT值分布标签页"""
        layout = QVBoxLayout()
        
        # CT值分布图画布
        self.distribution_figure = Figure(figsize=(12, 6))
        self.distribution_canvas = FigureCanvas(self.distribution_figure)
        layout.addWidget(self.distribution_canvas)
        
        self.distribution_tab.setLayout(layout)
    
    def update_results(self, results):
        """更新分析结果显示"""
        try:
            # 更新总体信息
            total_volume = results['total_lung_volume_cm3']
            laa_percentage = results['laa_percentage']
            emphysema_grade = results['emphysema_grade']
            
            general_info = f"""总肺体积: {total_volume:.2f} cm³
总体素数: {results['total_lung_voxels']:,}
体素体积: {results['voxel_volume_cm3']:.6f} cm³
LAA%: {laa_percentage:.2f}%"""
            
            self.general_info_label.setText(general_info)
            
            # 更新组织成分表格
            tissue_analysis = results['tissue_analysis']
            self.tissue_table.setRowCount(len(tissue_analysis))
            
            tissue_names_cn = {
                'bulla': '肺大泡/重度肺气肿',
                'inflated': '正常肺泡区',
                'infiltrated': '炎性/间质浸润区',
                'collapsed': '实变/不张',
                'vessels': '血管与软组织'
            }
            
            for i, (tissue_name, analysis) in enumerate(tissue_analysis.items()):
                self.tissue_table.setItem(i, 0, QTableWidgetItem(tissue_names_cn.get(tissue_name, tissue_name)))
                self.tissue_table.setItem(i, 1, QTableWidgetItem(f"{analysis['volume_cm3']:.2f}"))
                self.tissue_table.setItem(i, 2, QTableWidgetItem(f"{analysis['percentage']:.2f}"))
                hu_range = analysis['hu_range']
                self.tissue_table.setItem(i, 3, QTableWidgetItem(f"{hu_range[0]} ~ {hu_range[1]}"))
            
            self.tissue_table.resizeColumnsToContents()
            
            # 更新CT值统计表格
            ct_stats = results['ct_stats']
            stats_items = [
                ('最小值', f"{ct_stats['min']:.1f} HU"),
                ('最大值', f"{ct_stats['max']:.1f} HU"),
                ('平均值', f"{ct_stats['mean']:.1f} HU"),
                ('标准差', f"{ct_stats['std']:.1f} HU"),
                ('中位数', f"{ct_stats['median']:.1f} HU"),
                ('5%分位数', f"{ct_stats['percentile_5']:.1f} HU"),
                ('95%分位数', f"{ct_stats['percentile_95']:.1f} HU")
            ]
            
            self.ct_stats_table.setRowCount(len(stats_items))
            for i, (name, value) in enumerate(stats_items):
                self.ct_stats_table.setItem(i, 0, QTableWidgetItem(name))
                self.ct_stats_table.setItem(i, 1, QTableWidgetItem(value))
            
            self.ct_stats_table.resizeColumnsToContents()
            
            # 更新肺气肿评估
            emphysema_text = f"""肺气肿严重程度评估:
LAA%: {laa_percentage:.2f}%
等级: {emphysema_grade['description']}"""
            self.emphysema_label.setText(emphysema_text)
            
            # 设置颜色
            if emphysema_grade['grade'] in ['normal', 'mild']:
                color = 'green'
            elif emphysema_grade['grade'] == 'moderate':
                color = 'orange'
            else:
                color = 'red'
            
            self.emphysema_label.setStyleSheet(f"color: {color}; padding: 10px; border: 1px solid {color}; border-radius: 5px;")
            
            # 更新三维重建
            if 'reconstruction_fig' in results:
                # 直接替换canvas的figure
                source_fig = results['reconstruction_fig']
                
                # 创建新的canvas来显示源图形
                self.reconstruction_canvas.figure = source_fig
                self.reconstruction_canvas.draw()
            
            # 更新CT值分布图
            self.update_ct_distribution(results)
            
        except Exception as e:
            logger.error(f"更新结果显示时出错: {e}")
            QMessageBox.warning(self, "警告", f"更新结果显示时出错: {str(e)}")
    
    def update_ct_distribution(self, results):
        """更新CT值分布图"""
        try:
            self.distribution_figure.clear()
            
            # 创建子图
            ax1 = self.distribution_figure.add_subplot(121)
            ax2 = self.distribution_figure.add_subplot(122)
            
            # 模拟CT值分布（实际应用中需要从原始数据计算）
            tissue_analysis = results['tissue_analysis']
            
            # 1. 组织成分条形图
            tissue_names = []
            tissue_percentages = []
            colors = ['red', 'lightgreen', 'orange', 'purple', 'brown']
            
            tissue_names_cn = {
                'bulla': '肺大泡',
                'inflated': '正常肺泡',
                'infiltrated': '炎性浸润',
                'collapsed': '实变',
                'vessels': '血管'
            }
            
            for tissue_name, analysis in tissue_analysis.items():
                tissue_names.append(tissue_names_cn.get(tissue_name, tissue_name))
                tissue_percentages.append(analysis['percentage'])
            
            bars = ax1.bar(tissue_names, tissue_percentages, color=colors[:len(tissue_names)])
            ax1.set_ylabel('占比 (%)')
            ax1.set_title('肺部组织成分分布')
            ax1.tick_params(axis='x', rotation=45)
            
            # 在条形图上添加数值标签
            for bar, percentage in zip(bars, tissue_percentages):
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                        f'{percentage:.1f}%', ha='center', va='bottom')
            
            # 2. CT值范围示意图
            hu_ranges = []
            range_names = []
            range_colors = colors[:len(tissue_analysis)]
            
            for tissue_name, analysis in tissue_analysis.items():
                hu_range = analysis['hu_range']
                hu_ranges.append([hu_range[0], hu_range[1]])
                range_names.append(tissue_names_cn.get(tissue_name, tissue_name))
            
            # 绘制CT值范围
            y_pos = np.arange(len(range_names))
            for i, (name, hu_range, color) in enumerate(zip(range_names, hu_ranges, range_colors)):
                ax2.barh(i, hu_range[1] - hu_range[0], left=hu_range[0], 
                        color=color, alpha=0.7, height=0.6)
                # 添加范围标签
                ax2.text(hu_range[0] + (hu_range[1] - hu_range[0])/2, i, 
                        f'{hu_range[0]} ~ {hu_range[1]}', 
                        ha='center', va='center', fontsize=8)
            
            ax2.set_yticks(y_pos)
            ax2.set_yticklabels(range_names)
            ax2.set_xlabel('CT值 (HU)')
            ax2.set_title('CT值范围分布')
            ax2.grid(True, alpha=0.3)
            
            self.distribution_figure.tight_layout()
            self.distribution_canvas.draw()
            
        except Exception as e:
            logger.error(f"更新CT值分布图时出错: {e}")

class LungTissueAnalyzerGUI(QMainWindow):
    """肺部组织成分分析GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("肺部组织成分分析工具")
        self.setGeometry(100, 100, 1400, 900)
        
        self.analyzer = LungTissueAnalyzer()
        self.current_image = None
        self.current_mask = None
        self.current_spacing = None
        self.current_results = None
        
        self.init_ui()
    
    def init_ui(self):
        """初始化用户界面"""
        # 主布局
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        main_layout = QHBoxLayout(main_widget)
        
        # 左侧控制面板
        control_panel = QWidget()
        control_panel.setMaximumWidth(350)
        control_layout = QVBoxLayout(control_panel)
        
        # 文件加载组
        file_group = QGroupBox("文件加载")
        file_layout = QVBoxLayout()
        
        # CT图像加载
        ct_layout = QHBoxLayout()
        ct_layout.addWidget(QLabel("CT图像(NIfTI/DICOM):"))
        self.ct_path_edit = QLineEdit()
        self.ct_path_edit.setPlaceholderText("支持NIfTI文件或DICOM目录")
        ct_layout.addWidget(self.ct_path_edit)
        self.browse_ct_btn = QPushButton("浏览...")
        self.browse_ct_btn.clicked.connect(self.browse_ct_image)
        ct_layout.addWidget(self.browse_ct_btn)
        file_layout.addLayout(ct_layout)
        
        # 肺部掩码加载
        mask_layout = QHBoxLayout()
        mask_layout.addWidget(QLabel("肺部掩码(NIfTI/DICOM):"))
        self.mask_path_edit = QLineEdit()
        self.mask_path_edit.setPlaceholderText("支持NIfTI文件或DICOM目录")
        mask_layout.addWidget(self.mask_path_edit)
        self.browse_mask_btn = QPushButton("浏览...")
        self.browse_mask_btn.clicked.connect(self.browse_lung_mask)
        mask_layout.addWidget(self.browse_mask_btn)
        file_layout.addLayout(mask_layout)
        
        # 加载按钮
        self.load_btn = QPushButton("加载图像")
        self.load_btn.clicked.connect(self.load_images)
        file_layout.addWidget(self.load_btn)
        
        file_group.setLayout(file_layout)
        control_layout.addWidget(file_group)
        
        # 分析参数组
        params_group = QGroupBox("分析参数")
        params_layout = QVBoxLayout()
        
        # 体素间距设置
        spacing_layout = QHBoxLayout()
        spacing_layout.addWidget(QLabel("体素间距(mm):"))
        self.spacing_x_spin = QDoubleSpinBox()
        self.spacing_x_spin.setRange(0.1, 10.0)
        self.spacing_x_spin.setValue(1.0)
        self.spacing_x_spin.setDecimals(3)
        spacing_layout.addWidget(self.spacing_x_spin)
        
        self.spacing_y_spin = QDoubleSpinBox()
        self.spacing_y_spin.setRange(0.1, 10.0)
        self.spacing_y_spin.setValue(1.0)
        self.spacing_y_spin.setDecimals(3)
        spacing_layout.addWidget(self.spacing_y_spin)
        
        self.spacing_z_spin = QDoubleSpinBox()
        self.spacing_z_spin.setRange(0.1, 10.0)
        self.spacing_z_spin.setValue(1.0)
        self.spacing_z_spin.setDecimals(3)
        spacing_layout.addWidget(self.spacing_z_spin)
        params_layout.addLayout(spacing_layout)
        
        # 自动检测间距复选框
        self.auto_spacing_check = QCheckBox("自动检测体素间距")
        self.auto_spacing_check.setChecked(True)
        self.auto_spacing_check.toggled.connect(self.on_auto_spacing_toggled)
        params_layout.addWidget(self.auto_spacing_check)
        
        params_group.setLayout(params_layout)
        control_layout.addWidget(params_group)
        
        # 分析按钮
        self.analyze_btn = QPushButton("开始分析")
        self.analyze_btn.clicked.connect(self.start_analysis)
        self.analyze_btn.setEnabled(False)
        control_layout.addWidget(self.analyze_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        control_layout.addWidget(self.progress_bar)
        
        # 状态信息
        self.status_text = QTextEdit()
        self.status_text.setMaximumHeight(150)
        self.status_text.setReadOnly(True)
        control_layout.addWidget(self.status_text)
        
        # 导出结果按钮
        self.export_btn = QPushButton("导出结果")
        self.export_btn.clicked.connect(self.export_results)
        self.export_btn.setEnabled(False)
        control_layout.addWidget(self.export_btn)
        
        control_layout.addStretch()
        main_layout.addWidget(control_panel)
        
        # 右侧显示区域
        display_splitter = QSplitter(Qt.Horizontal)
        
        # 图像查看器
        self.image_viewer = ImageViewer()
        display_splitter.addWidget(self.image_viewer)
        
        # 结果查看器
        self.results_viewer = ResultsViewer()
        display_splitter.addWidget(self.results_viewer)
        
        # 设置分割比例
        display_splitter.setSizes([400, 600])
        
        main_layout.addWidget(display_splitter, 1)
        
        # 初始化状态
        self.on_auto_spacing_toggled(True)
        self.log("肺部组织成分分析工具已启动")
    
    def browse_ct_image(self):
        """浏览CT图像文件或DICOM目录"""
        # 提供选择文件或目录的选项
        choice = QMessageBox.question(
            self, "选择输入类型", 
            "请选择CT图像的输入类型:\n\n" +
            "是(Yes): 选择NIfTI文件\n" +
            "否(No): 选择DICOM目录\n" +
            "取消(Cancel): 取消操作",
            QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
            QMessageBox.Yes
        )
        
        if choice == QMessageBox.Yes:
            # 选择NIfTI文件
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择CT图像文件", "", 
                "医学图像文件 (*.nii *.nii.gz);;所有文件 (*)"
            )
            if file_path:
                self.ct_path_edit.setText(file_path)
        elif choice == QMessageBox.No:
            # 选择DICOM目录
            dir_path = QFileDialog.getExistingDirectory(
                self, "选择DICOM目录", ""
            )
            if dir_path:
                self.ct_path_edit.setText(dir_path)
    
    def browse_lung_mask(self):
        """浏览肺部掩码文件或DICOM目录"""
        # 提供选择文件或目录的选项
        choice = QMessageBox.question(
            self, "选择输入类型", 
            "请选择肺部掩码的输入类型:\n\n" +
            "是(Yes): 选择NIfTI文件\n" +
            "否(No): 选择DICOM目录\n" +
            "取消(Cancel): 取消操作",
            QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel,
            QMessageBox.Yes
        )
        
        if choice == QMessageBox.Yes:
            # 选择NIfTI文件
            file_path, _ = QFileDialog.getOpenFileName(
                self, "选择肺部掩码文件", "", 
                "医学图像文件 (*.nii *.nii.gz);;所有文件 (*)"
            )
            if file_path:
                self.mask_path_edit.setText(file_path)
        elif choice == QMessageBox.No:
            # 选择DICOM目录
            dir_path = QFileDialog.getExistingDirectory(
                self, "选择DICOM目录", ""
            )
            if dir_path:
                self.mask_path_edit.setText(dir_path)
    
    def on_auto_spacing_toggled(self, checked):
        """自动检测间距复选框状态改变"""
        self.spacing_x_spin.setEnabled(not checked)
        self.spacing_y_spin.setEnabled(not checked)
        self.spacing_z_spin.setEnabled(not checked)
    
    def load_images(self):
        """加载图像和掩码"""
        try:
            ct_path = self.ct_path_edit.text().strip()
            mask_path = self.mask_path_edit.text().strip()
            
            if not ct_path:
                QMessageBox.warning(self, "警告", "请选择CT图像文件")
                return
            
            if not mask_path:
                QMessageBox.warning(self, "警告", "请选择肺部掩码文件")
                return
            
            self.log("正在加载图像...")
            
            # 检测并显示文件类型
            ct_type = "DICOM目录" if os.path.isdir(ct_path) else "NIfTI文件"
            mask_type = "DICOM目录" if os.path.isdir(mask_path) else "NIfTI文件"
            self.log(f"CT图像类型: {ct_type}")
            self.log(f"肺部掩码类型: {mask_type}")
            
            # 加载CT图像
            ct_image, ct_metadata = load_image(ct_path)
            self.log(f"CT图像加载完成: {ct_image.shape}")
            
            # 加载肺部掩码
            lung_mask, mask_metadata = load_image(mask_path)
            self.log(f"肺部掩码加载完成: {lung_mask.shape}")
            
            # 检查尺寸匹配
            if ct_image.shape != lung_mask.shape:
                QMessageBox.warning(self, "警告", 
                    f"CT图像和肺部掩码尺寸不匹配:\nCT图像: {ct_image.shape}\n肺部掩码: {lung_mask.shape}")
                return
            
            # 检查掩码是否为二值图像
            unique_values = np.unique(lung_mask)
            if len(unique_values) > 2 or (len(unique_values) == 2 and not (0 in unique_values and 1 in unique_values)):
                self.log(f"警告: 肺部掩码包含非二值数据，唯一值: {unique_values}")
                # 尝试二值化
                lung_mask = (lung_mask > 0).astype(np.uint8)
                self.log("已自动二值化肺部掩码")
            
            # 保存数据
            self.current_image = ct_image
            self.current_mask = lung_mask
            
            # 获取体素间距
            if self.auto_spacing_check.isChecked() and 'spacing' in ct_metadata:
                self.current_spacing = ct_metadata['spacing']
                self.spacing_x_spin.setValue(self.current_spacing[0])
                self.spacing_y_spin.setValue(self.current_spacing[1])
                self.spacing_z_spin.setValue(self.current_spacing[2])
                self.log(f"自动检测到体素间距: {self.current_spacing}")
            else:
                self.current_spacing = (
                    self.spacing_x_spin.value(),
                    self.spacing_y_spin.value(),
                    self.spacing_z_spin.value()
                )
                self.log(f"使用手动设置的体素间距: {self.current_spacing}")
            
            # 更新图像查看器
            self.image_viewer.set_image(ct_image, lung_mask)
            
            # 启用分析按钮
            self.analyze_btn.setEnabled(True)
            
            self.log("图像加载完成，可以开始分析")
            
        except Exception as e:
            error_msg = f"加载图像时出错: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def start_analysis(self):
        """开始组织分析"""
        try:
            if self.current_image is None or self.current_mask is None:
                QMessageBox.warning(self, "警告", "请先加载图像和掩码")
                return
            
            # 检查肺部掩码是否有效
            if np.count_nonzero(self.current_mask) == 0:
                QMessageBox.warning(self, "警告", "肺部掩码为空，无法进行分析")
                return
            
            self.log("开始肺部组织分析...")
            
            # 禁用分析按钮
            self.analyze_btn.setEnabled(False)
            self.progress_bar.setVisible(True)
            self.progress_bar.setRange(0, 0)  # 不确定进度
            
            # 创建分析线程
            self.analysis_thread = AnalysisThread(
                self.analyzer, 
                self.current_image, 
                self.current_mask, 
                self.current_spacing
            )
            
            # 连接信号
            self.analysis_thread.finished.connect(self.on_analysis_finished)
            self.analysis_thread.error.connect(self.on_analysis_error)
            self.analysis_thread.progress.connect(self.log)
            
            # 启动线程
            self.analysis_thread.start()
            
        except Exception as e:
            error_msg = f"启动分析时出错: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
            self.analyze_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
    
    def on_analysis_finished(self, results):
        """分析完成处理"""
        try:
            self.current_results = results
            
            # 更新结果显示
            self.results_viewer.update_results(results)
            
            # 恢复UI状态
            self.analyze_btn.setEnabled(True)
            self.progress_bar.setVisible(False)
            self.export_btn.setEnabled(True)
            
            # 显示分析摘要
            total_volume = results['total_lung_volume_cm3']
            laa_percentage = results['laa_percentage']
            emphysema_grade = results['emphysema_grade']['description']
            
            summary = f"""分析完成！
总肺体积: {total_volume:.2f} cm³
LAA%: {laa_percentage:.2f}%
肺气肿程度: {emphysema_grade}"""
            
            self.log(summary)
            QMessageBox.information(self, "分析完成", summary)
            
        except Exception as e:
            error_msg = f"处理分析结果时出错: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "错误", error_msg)
    
    def on_analysis_error(self, error_msg):
        """分析错误处理"""
        self.log(f"分析失败: {error_msg}")
        QMessageBox.critical(self, "分析失败", error_msg)
        
        # 恢复UI状态
        self.analyze_btn.setEnabled(True)
        self.progress_bar.setVisible(False)
    
    def export_results(self):
        """导出分析结果"""
        try:
            if self.current_results is None:
                QMessageBox.warning(self, "警告", "没有可导出的结果")
                return
            
            # 选择导出文件
            file_path, _ = QFileDialog.getSaveFileName(
                self, "导出分析结果", "lung_tissue_analysis.xlsx", 
                "Excel文件 (*.xlsx);;CSV文件 (*.csv);;所有文件 (*)"
            )
            
            if not file_path:
                return
            
            self.log("正在导出结果...")
            
            # 准备导出数据
            results = self.current_results
            
            # 创建DataFrame
            export_data = []
            
            # 总体信息
            export_data.append(['总体信息', '', '', ''])
            export_data.append(['总肺体积(cm³)', f"{results['total_lung_volume_cm3']:.2f}", '', ''])
            export_data.append(['总体素数', f"{results['total_lung_voxels']:,}", '', ''])
            export_data.append(['体素体积(cm³)', f"{results['voxel_volume_cm3']:.6f}", '', ''])
            export_data.append(['LAA%', f"{results['laa_percentage']:.2f}%", '', ''])
            export_data.append(['', '', '', ''])
            
            # 组织成分分析
            export_data.append(['组织成分分析', '', '', ''])
            export_data.append(['组织类型', '体积(cm³)', '占比(%)', 'CT值范围(HU)'])
            
            tissue_names_cn = {
                'bulla': '肺大泡/重度肺气肿',
                'inflated': '正常肺泡区',
                'infiltrated': '炎性/间质浸润区',
                'collapsed': '实变/不张',
                'vessels': '血管与软组织'
            }
            
            for tissue_name, analysis in results['tissue_analysis'].items():
                export_data.append([
                    tissue_names_cn.get(tissue_name, tissue_name),
                    f"{analysis['volume_cm3']:.2f}",
                    f"{analysis['percentage']:.2f}",
                    f"{analysis['hu_range'][0]} ~ {analysis['hu_range'][1]}"
                ])
            
            export_data.append(['', '', '', ''])
            
            # CT值统计
            export_data.append(['CT值统计', '', '', ''])
            ct_stats = results['ct_stats']
            stats_items = [
                ('最小值(HU)', f"{ct_stats['min']:.1f}"),
                ('最大值(HU)', f"{ct_stats['max']:.1f}"),
                ('平均值(HU)', f"{ct_stats['mean']:.1f}"),
                ('标准差(HU)', f"{ct_stats['std']:.1f}"),
                ('中位数(HU)', f"{ct_stats['median']:.1f}"),
                ('5%分位数(HU)', f"{ct_stats['percentile_5']:.1f}"),
                ('95%分位数(HU)', f"{ct_stats['percentile_95']:.1f}")
            ]
            
            for name, value in stats_items:
                export_data.append([name, value, '', ''])
            
            export_data.append(['', '', '', ''])
            
            # 肺气肿评估
            export_data.append(['肺气肿评估', '', '', ''])
            emphysema_grade = results['emphysema_grade']
            export_data.append(['LAA%', f"{emphysema_grade['percentage']:.2f}%", '', ''])
            export_data.append(['严重程度', emphysema_grade['description'], '', ''])
            
            # 创建DataFrame并导出
            df = pd.DataFrame(export_data, columns=['项目', '数值', '备注1', '备注2'])
            
            if file_path.endswith('.xlsx'):
                try:
                    df.to_excel(file_path, index=False, sheet_name='肺部组织分析结果')
                except ImportError:
                    # 如果缺少openpyxl，自动转换为CSV格式
                    csv_path = file_path.replace('.xlsx', '.csv')
                    df.to_csv(csv_path, index=False, encoding='utf-8-sig')
                    self.log("警告: 缺少openpyxl模块，已自动转换为CSV格式")
                    file_path = csv_path
            else:
                df.to_csv(file_path, index=False, encoding='utf-8-sig')
            
            self.log(f"结果已导出到: {file_path}")
            QMessageBox.information(self, "导出完成", f"分析结果已成功导出到:\n{file_path}")
            
        except Exception as e:
            error_msg = f"导出结果时出错: {str(e)}"
            self.log(error_msg)
            QMessageBox.critical(self, "导出失败", error_msg)
    
    def log(self, message):
        """记录日志信息"""
        import datetime
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        log_message = f"[{timestamp}] {message}"
        self.status_text.append(log_message)
        logger.info(message)
        
        # 自动滚动到底部
        cursor = self.status_text.textCursor()
        cursor.movePosition(cursor.End)
        self.status_text.setTextCursor(cursor)

def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    # 设置应用程序信息
    app.setApplicationName("肺部组织成分分析工具")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Medical Image Analysis")
    
    # 创建主窗口
    window = LungTissueAnalyzerGUI()
    window.show()
    
    # 运行应用程序
    sys.exit(app.exec_())

if __name__ == "__main__":
    main()