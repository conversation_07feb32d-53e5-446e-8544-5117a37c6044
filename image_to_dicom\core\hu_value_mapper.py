#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HU值映射器
将JPG图像的灰度值映射到CT的HU值

作者: AI Assistant
日期: 2025-01-17
"""

import numpy as np
from typing import Dict, Any, Optional, Tuple
import json
from enum import Enum


class MappingStrategy(Enum):
    """映射策略枚举"""
    LINEAR = "linear"           # 线性映射
    TISSUE_BASED = "tissue"     # 基于组织类型的映射
    LOOKUP_TABLE = "lookup"     # 查找表映射
    WINDOWING = "windowing"     # 基于窗宽窗位的映射


class HUValueMapper:
    """HU值映射器"""
    
    def __init__(self):
        # 标准组织HU值范围（参考医学文献）
        self.tissue_hu_ranges = {
            'air': -1000,
            'lung': (-900, -500),      # 肺组织
            'fat': (-120, -60),        # 脂肪组织
            'water': 0,                # 水
            'soft_tissue': (20, 60),   # 软组织
            'muscle': (40, 80),        # 肌肉
            'blood': (40, 60),         # 血液
            'bone_spongy': (150, 300), # 松质骨
            'bone_compact': (300, 1000), # 密质骨
            'contrast': (100, 300),    # 对比剂
        }
        
        # 默认线性映射参数
        self.default_linear_params = {
            'min_hu': -1000,  # 最小HU值（空气）
            'max_hu': 1000,   # 最大HU值（骨骼）
            'min_gray': 0,    # 最小灰度值
            'max_gray': 255   # 最大灰度值
        }
        
        # 胸部CT的典型窗宽窗位设置
        self.window_presets = {
            'lung': {'center': -600, 'width': 1600},      # 肺窗
            'mediastinum': {'center': 40, 'width': 350},  # 纵隔窗
            'bone': {'center': 300, 'width': 1500},       # 骨窗
            'soft_tissue': {'center': 40, 'width': 400},  # 软组织窗
        }
    
    def linear_mapping(self, gray_values: np.ndarray, 
                      min_hu: float = -1000, max_hu: float = 1000,
                      min_gray: int = 0, max_gray: int = 255) -> np.ndarray:
        """
        线性映射：灰度值 -> HU值
        
        Args:
            gray_values: 输入灰度值数组
            min_hu: 最小HU值
            max_hu: 最大HU值
            min_gray: 最小灰度值
            max_gray: 最大灰度值
        
        Returns:
            HU值数组
        """
        # 线性映射公式: HU = (gray - min_gray) * (max_hu - min_hu) / (max_gray - min_gray) + min_hu
        hu_values = (gray_values.astype(np.float32) - min_gray) * (max_hu - min_hu) / (max_gray - min_gray) + min_hu
        
        # 限制HU值范围
        hu_values = np.clip(hu_values, min_hu, max_hu)
        
        return hu_values.astype(np.int16)
    
    def tissue_based_mapping(self, gray_values: np.ndarray, 
                           tissue_type: str = 'chest') -> np.ndarray:
        """
        基于组织类型的映射
        
        Args:
            gray_values: 输入灰度值数组
            tissue_type: 组织类型 ('chest', 'abdomen', 'head')
        
        Returns:
            HU值数组
        """
        hu_values = np.zeros_like(gray_values, dtype=np.int16)
        
        if tissue_type == 'chest':
            # 胸部CT的典型映射
            # 0-50: 空气和肺组织 (-1000 to -500)
            mask1 = gray_values <= 50
            hu_values[mask1] = self.linear_mapping(gray_values[mask1], -1000, -500, 0, 50)
            
            # 51-100: 肺组织到软组织 (-500 to 0)
            mask2 = (gray_values > 50) & (gray_values <= 100)
            hu_values[mask2] = self.linear_mapping(gray_values[mask2], -500, 0, 51, 100)
            
            # 101-180: 软组织 (0 to 80)
            mask3 = (gray_values > 100) & (gray_values <= 180)
            hu_values[mask3] = self.linear_mapping(gray_values[mask3], 0, 80, 101, 180)
            
            # 181-255: 骨骼和高密度组织 (80 to 1000)
            mask4 = gray_values > 180
            hu_values[mask4] = self.linear_mapping(gray_values[mask4], 80, 1000, 181, 255)
            
        elif tissue_type == 'abdomen':
            # 腹部CT的典型映射
            # 更多软组织，较少空气
            mask1 = gray_values <= 30
            hu_values[mask1] = self.linear_mapping(gray_values[mask1], -200, -50, 0, 30)
            
            mask2 = (gray_values > 30) & (gray_values <= 200)
            hu_values[mask2] = self.linear_mapping(gray_values[mask2], -50, 100, 31, 200)
            
            mask3 = gray_values > 200
            hu_values[mask3] = self.linear_mapping(gray_values[mask3], 100, 1000, 201, 255)
            
        else:  # 默认使用胸部映射
            return self.tissue_based_mapping(gray_values, 'chest')
        
        return hu_values
    
    def windowing_based_mapping(self, gray_values: np.ndarray,
                              window_center: float = 40, window_width: float = 350) -> np.ndarray:
        """
        基于窗宽窗位的映射
        
        Args:
            gray_values: 输入灰度值数组
            window_center: 窗位（HU值）
            window_width: 窗宽（HU值）
        
        Returns:
            HU值数组
        """
        # 计算窗的上下限
        window_min = window_center - window_width / 2
        window_max = window_center + window_width / 2
        
        # 将灰度值映射到窗范围内
        hu_values = self.linear_mapping(gray_values, window_min, window_max, 0, 255)
        
        return hu_values
    
    def lookup_table_mapping(self, gray_values: np.ndarray, 
                           lookup_table: Optional[Dict[int, int]] = None) -> np.ndarray:
        """
        查找表映射
        
        Args:
            gray_values: 输入灰度值数组
            lookup_table: 灰度值到HU值的查找表
        
        Returns:
            HU值数组
        """
        if lookup_table is None:
            # 创建默认查找表（基于胸部CT）
            lookup_table = self._create_default_lookup_table()
        
        # 应用查找表
        hu_values = np.zeros_like(gray_values, dtype=np.int16)
        for gray, hu in lookup_table.items():
            mask = gray_values == gray
            hu_values[mask] = hu
        
        # 对于查找表中没有的值，使用线性插值
        for gray in range(256):
            if gray not in lookup_table:
                mask = gray_values == gray
                if np.any(mask):
                    # 找到最近的两个已知值进行插值
                    lower_gray = max([g for g in lookup_table.keys() if g < gray], default=0)
                    upper_gray = min([g for g in lookup_table.keys() if g > gray], default=255)
                    
                    if lower_gray in lookup_table and upper_gray in lookup_table:
                        # 线性插值
                        ratio = (gray - lower_gray) / (upper_gray - lower_gray)
                        hu_value = lookup_table[lower_gray] + ratio * (lookup_table[upper_gray] - lookup_table[lower_gray])
                        hu_values[mask] = int(hu_value)
        
        return hu_values
    
    def _create_default_lookup_table(self) -> Dict[int, int]:
        """创建默认的查找表"""
        lookup_table = {}
        
        # 关键点映射
        key_points = [
            (0, -1000),    # 黑色 -> 空气
            (25, -800),    # 深灰 -> 肺组织（低密度）
            (50, -600),    # 灰色 -> 肺组织（中密度）
            (75, -400),    # 浅灰 -> 肺组织（高密度）
            (100, -100),   # 中灰 -> 脂肪组织
            (128, 0),      # 中性灰 -> 水
            (150, 40),     # 浅灰 -> 软组织
            (180, 80),     # 更浅 -> 肌肉
            (200, 200),    # 亮灰 -> 对比剂/软骨
            (230, 500),    # 很亮 -> 松质骨
            (255, 1000),   # 白色 -> 密质骨
        ]
        
        # 在关键点之间进行线性插值
        for i in range(len(key_points) - 1):
            gray1, hu1 = key_points[i]
            gray2, hu2 = key_points[i + 1]
            
            for gray in range(gray1, gray2 + 1):
                ratio = (gray - gray1) / (gray2 - gray1) if gray2 != gray1 else 0
                hu = int(hu1 + ratio * (hu2 - hu1))
                lookup_table[gray] = hu
        
        return lookup_table
    
    def map_to_hu(self, gray_values: np.ndarray, 
                  strategy: MappingStrategy = MappingStrategy.TISSUE_BASED,
                  **kwargs) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        将灰度值映射到HU值
        
        Args:
            gray_values: 输入灰度值数组
            strategy: 映射策略
            **kwargs: 策略相关参数
        
        Returns:
            (HU值数组, 映射信息字典)
        """
        if strategy == MappingStrategy.LINEAR:
            hu_values = self.linear_mapping(gray_values, **kwargs)
            mapping_info = {
                'strategy': 'linear',
                'parameters': kwargs,
                'hu_range': (int(hu_values.min()), int(hu_values.max())),
                'rescale_intercept': kwargs.get('min_hu', -1000),
                'rescale_slope': (kwargs.get('max_hu', 1000) - kwargs.get('min_hu', -1000)) / 255
            }
            
        elif strategy == MappingStrategy.TISSUE_BASED:
            tissue_type = kwargs.get('tissue_type', 'chest')
            hu_values = self.tissue_based_mapping(gray_values, tissue_type)
            mapping_info = {
                'strategy': 'tissue_based',
                'tissue_type': tissue_type,
                'hu_range': (int(hu_values.min()), int(hu_values.max())),
                'rescale_intercept': -1024,  # 标准CT值
                'rescale_slope': 1
            }
            
        elif strategy == MappingStrategy.WINDOWING:
            window_center = kwargs.get('window_center', 40)
            window_width = kwargs.get('window_width', 350)
            hu_values = self.windowing_based_mapping(gray_values, window_center, window_width)
            mapping_info = {
                'strategy': 'windowing',
                'window_center': window_center,
                'window_width': window_width,
                'hu_range': (int(hu_values.min()), int(hu_values.max())),
                'rescale_intercept': window_center - window_width / 2,
                'rescale_slope': window_width / 255
            }
            
        elif strategy == MappingStrategy.LOOKUP_TABLE:
            lookup_table = kwargs.get('lookup_table', None)
            hu_values = self.lookup_table_mapping(gray_values, lookup_table)
            mapping_info = {
                'strategy': 'lookup_table',
                'hu_range': (int(hu_values.min()), int(hu_values.max())),
                'rescale_intercept': -1024,
                'rescale_slope': 1
            }
            
        else:
            raise ValueError(f"不支持的映射策略: {strategy}")
        
        return hu_values, mapping_info
    
    def get_window_preset(self, preset_name: str) -> Dict[str, float]:
        """获取窗宽窗位预设"""
        return self.window_presets.get(preset_name, self.window_presets['mediastinum'])
    
    def save_mapping_config(self, config: Dict[str, Any], filepath: str):
        """保存映射配置"""
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=2, ensure_ascii=False)
    
    def load_mapping_config(self, filepath: str) -> Dict[str, Any]:
        """加载映射配置"""
        with open(filepath, 'r', encoding='utf-8') as f:
            return json.load(f)
