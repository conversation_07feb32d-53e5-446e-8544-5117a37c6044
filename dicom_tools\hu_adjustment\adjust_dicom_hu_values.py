#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICOM文件CT值调整工具

该工具用于调整DICOM文件中的CT值，通过修改RescaleIntercept和RescaleSlope参数
使CT值在原有基础上减少约33074

作者: AI Assistant
日期: 2024-06-10
"""

import os
import sys
import pydicom
import argparse
from pathlib import Path
import numpy as np
from tqdm import tqdm


class DicomHUAdjuster:
    """DICOM文件CT值调整器"""
    
    def __init__(self, adjustment_value=-33074):
        """
        初始化调整器
        
        Args:
            adjustment_value: 需要调整的CT值数值，默认为-33074
        """
        self.adjustment_value = adjustment_value
    
    def adjust_single_file(self, input_file, output_file=None):
        """
        调整单个DICOM文件的CT值
        
        Args:
            input_file: 输入DICOM文件路径
            output_file: 输出DICOM文件路径，如果为None则覆盖原文件
        
        Returns:
            bool: 操作是否成功
        """
        try:
            # 读取DICOM文件
            dcm = pydicom.dcmread(input_file)
            
            # 检查是否包含需要的标签
            if hasattr(dcm, 'RescaleIntercept') and hasattr(dcm, 'RescaleSlope'):
                # 记录原始值
                original_intercept = dcm.RescaleIntercept
                original_slope = dcm.RescaleSlope
                
                # 调整RescaleIntercept（截距）
                new_intercept = original_intercept + self.adjustment_value
                dcm.RescaleIntercept = new_intercept
                
                print(f"文件: {os.path.basename(input_file)}")
                print(f"  原始RescaleIntercept: {original_intercept}")
                print(f"  新的RescaleIntercept: {new_intercept}")
                print(f"  RescaleSlope保持不变: {original_slope}")
                
                # 保存修改后的DICOM文件
                if output_file is None:
                    output_file = input_file
                
                dcm.save_as(output_file)
                return True
            else:
                print(f"警告: {input_file} 缺少RescaleIntercept或RescaleSlope标签")
                return False
                
        except Exception as e:
            print(f"处理 {input_file} 时出错: {e}")
            return False
    
    def batch_process(self, input_dir, output_dir=None, file_pattern="*.dcm"):
        """
        批量处理DICOM文件
        
        Args:
            input_dir: 输入目录路径
            output_dir: 输出目录路径，如果为None则覆盖原文件
            file_pattern: 文件匹配模式，默认为"*.dcm"
        
        Returns:
            tuple: (成功数, 失败数)
        """
        input_path = Path(input_dir)
        dicom_files = list(input_path.glob(file_pattern))
        
        success_count = 0
        fail_count = 0
        
        print(f"找到 {len(dicom_files)} 个DICOM文件")
        
        for dcm_file in tqdm(dicom_files, desc="处理DICOM文件"):
            if output_dir:
                # 创建相同的目录结构
                rel_path = dcm_file.relative_to(input_path)
                out_file = Path(output_dir) / rel_path
                os.makedirs(out_file.parent, exist_ok=True)
            else:
                out_file = None  # 覆盖原文件
                
            if self.adjust_single_file(str(dcm_file), str(out_file) if out_file else None):
                success_count += 1
            else:
                fail_count += 1
        
        return success_count, fail_count


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="DICOM文件CT值调整工具")
    parser.add_argument("--input", "-i", required=True, help="输入DICOM文件或目录")
    parser.add_argument("--output", "-o", help="输出DICOM文件或目录（如果不指定，则覆盖原文件）")
    parser.add_argument("--adjustment", "-a", type=float, default=-33074, 
                      help="CT值调整数值（默认为-33074）")
    parser.add_argument("--pattern", "-p", default="*.dcm", 
                      help="DICOM文件匹配模式（默认为'*.dcm'）")
    
    args = parser.parse_args()
    
    adjuster = DicomHUAdjuster(args.adjustment)
    
    input_path = Path(args.input)
    
    # 检查输入是文件还是目录
    if input_path.is_file():
        # 单个文件处理
        if args.output:
            output_path = Path(args.output)
            if output_path.is_dir():
                output_file = output_path / input_path.name
            else:
                output_file = output_path
        else:
            output_file = None  # 覆盖原文件
            
        if adjuster.adjust_single_file(str(input_path), str(output_file) if output_file else None):
            print("文件处理成功")
        else:
            print("文件处理失败")
            
    elif input_path.is_dir():
        # 目录批量处理
        success, fail = adjuster.batch_process(str(input_path), args.output, args.pattern)
        print(f"批量处理完成: {success} 个文件成功, {fail} 个文件失败")
        
    else:
        print(f"错误: 输入路径 {args.input} 不存在")
        sys.exit(1)


if __name__ == "__main__":
    main() 