# CT区域选框分割工具

## 项目简介

这是一个专门用于CT影像区域选框分割的工具，支持在CT影像的某一层面划定选框，然后自动在所有层面的相同位置进行区域分割。分割后的文件保持原有的DICOM格式和元数据信息。

## 主要功能

1. **DICOM文件读取**: 支持读取DICOM格式的CT影像文件
2. **区域选框**: 在CT影像的某一层面上划定矩形选框区域
3. **多层面分割**: 自动在所有层面的相同位置进行区域分割
4. **DICOM输出**: 保存分割结果为DICOM格式，保持原有元数据
5. **元数据保持**: 保持原有的UID、层厚、像素间距等重要信息

## 项目结构

```
ct_region_cropper/
├── core/                   # 核心功能模块
│   ├── __init__.py
│   ├── dicom_handler.py    # DICOM文件处理
│   ├── region_cropper.py   # 区域分割功能
│   └── metadata_manager.py # 元数据管理
├── gui/                    # 图形界面
│   ├── __init__.py
│   ├── main_window.py      # 主窗口
│   ├── image_viewer.py     # 图像显示组件
│   └── region_selector.py  # 区域选择组件
├── utils/                  # 工具函数
│   ├── __init__.py
│   ├── image_utils.py      # 图像处理工具
│   └── file_utils.py       # 文件处理工具
├── tests/                  # 测试文件
│   ├── __init__.py
│   └── test_cropper.py     # 功能测试
├── examples/               # 示例代码
│   ├── __init__.py
│   └── basic_usage.py      # 基本使用示例
├── README.md               # 项目说明
└── requirements.txt        # 依赖包列表
```

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 图形界面使用

```python
from gui.main_window import CTRegionCropperApp
import sys
from PyQt5.QtWidgets import QApplication

app = QApplication(sys.argv)
window = CTRegionCropperApp()
window.show()
sys.exit(app.exec_())
```

### 2. 编程接口使用

```python
from core.region_cropper import CTRegionCropper

# 创建分割器实例
cropper = CTRegionCropper()

# 加载DICOM系列
cropper.load_dicom_series("path/to/dicom/folder")

# 设置选框区域 (x, y, width, height)
cropper.set_crop_region(100, 100, 200, 200)

# 执行分割
cropper.crop_all_slices()

# 保存结果
cropper.save_cropped_series("path/to/output/folder")
```

## 技术特点

- **完整的DICOM支持**: 使用pydicom和SimpleITK处理DICOM文件
- **元数据保持**: 确保分割后的文件保持原有的医学影像元数据
- **交互式界面**: 提供直观的图形界面进行区域选择
- **批量处理**: 支持对整个DICOM系列进行批量分割
- **坐标系统**: 正确处理DICOM坐标系统和图像方向

## 注意事项

1. 输入文件必须是标准的DICOM格式
2. 确保DICOM文件包含完整的几何信息
3. 分割区域不能超出图像边界
4. 输出文件将保持原有的像素值范围和数据类型

## 依赖包

- pydicom: DICOM文件处理
- SimpleITK: 医学图像处理
- numpy: 数值计算
- PyQt5: 图形界面
- matplotlib: 图像显示
