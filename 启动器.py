#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
肺部CT影像分析工具集启动器

这个脚本提供一个简单的菜单界面，让用户选择要启动的功能模块
"""

import sys
import os
import subprocess
from pathlib import Path

def show_menu():
    """显示功能菜单"""
    print("=" * 60)
    print("           肺部CT影像分析工具集")
    print("=" * 60)
    print()
    print("请选择要启动的功能：")
    print()
    print("1. 肺组织分割工具        - 从CT影像中分割肺部结构")
    print("2. 肺组织分析工具        - 分析肺部组织，计算肺气肿等指标")
    print("3. 图片转DICOM工具       - 将JPG/PNG图片转换为DICOM格式")
    print("4. DICOM重新编号工具     - 重新编号DICOM文件序列")
    print("5. CT区域裁剪工具        - 裁剪CT影像的指定区域")
    print()
    print("0. 退出")
    print()
    print("=" * 60)

def run_script(script_name):
    """运行指定的脚本"""
    try:
        print(f"\n正在启动 {script_name}...")
        print("请稍候...")
        
        # 使用subprocess启动脚本
        result = subprocess.run([sys.executable, script_name], 
                              cwd=Path(__file__).parent,
                              capture_output=False)
        
        if result.returncode != 0:
            print(f"\n启动失败，返回码: {result.returncode}")
        else:
            print(f"\n{script_name} 已正常退出")
            
    except Exception as e:
        print(f"\n启动失败: {e}")
        print("请确保已安装所有依赖包：pip install -r requirements.txt")

def main():
    """主函数"""
    
    # 脚本映射
    scripts = {
        '1': 'run_lung_segmentation.py',
        '2': 'run_lung_tissue_analyzer.py', 
        '3': 'run_image_to_dicom.py',
        '4': 'run_dicom_renumber.py',
        '5': 'run_ct_region_cropper.py'
    }
    
    while True:
        show_menu()
        
        try:
            choice = input("请输入选项编号 (0-5): ").strip()
            
            if choice == '0':
                print("\n感谢使用！再见！")
                break
            elif choice in scripts:
                run_script(scripts[choice])
                input("\n按回车键返回主菜单...")
            else:
                print("\n无效选项，请重新选择！")
                input("按回车键继续...")
                
        except KeyboardInterrupt:
            print("\n\n用户中断，退出程序")
            break
        except Exception as e:
            print(f"\n发生错误: {e}")
            input("按回车键继续...")

if __name__ == "__main__":
    main()
