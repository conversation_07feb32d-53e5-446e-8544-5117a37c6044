#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICOM Metadata分析器
从真实DICOM文件中提取metadata模板，用于JPG转DICOM时的信息还原

作者: AI Assistant
日期: 2025-01-17
"""

import os
import pydicom
from typing import Dict, Any, Optional, List
import json
from pathlib import Path


class DicomMetadataAnalyzer:
    """DICOM Metadata分析器"""
    
    def __init__(self):
        # 基于filesdata.md中的真实DICOM metadata定义模板
        self.ge_ct750_template = {
            # 设备信息
            'device_info': {
                'Manufacturer': 'GE MEDICAL SYSTEMS',
                'ManufacturerModelName': 'Discovery CT750 HD',
                'StationName': 'ct99',
                'SoftwareVersions': 'gmp_hde.74',
                'DeviceSerialNumber': '*',
                'InstitutionName': 'HRB MU NO.4 HOSPITAL'
            },
            
            # 扫描参数
            'acquisition_params': {
                'Modality': 'CT',
                'KVP': '120',
                'SliceThickness': '5.000000',
                'SpacingBetweenSlices': '10.000000',
                'DataCollectionDiameter': '500.000000',
                'ReconstructionDiameter': '450.000000',
                'DistanceSourceToDetector': '946.746000',
                'DistanceSourceToPatient': '538.520000',
                'GantryDetectorTilt': '0.000000',
                'TableHeight': '120.500000',
                'RotationDirection': 'CW',
                'ExposureTime': '600',
                'XRayTubeCurrent': '150',
                'Exposure': '11',
                'FilterType': 'BODY FILTER',
                'GeneratorPower': '18000',
                'FocalSpots': '0.700000',
                'ConvolutionKernel': 'STANDARD',
                'PatientPosition': 'HFS',
                'ScanOptions': 'HELICAL MODE',
                'ProtocolName': '5.1 Routine Chest ASIR30'
            },
            
            # 图像参数
            'image_params': {
                'ImageType': ['ORIGINAL', 'PRIMARY', 'AXIAL'],
                'PhotometricInterpretation': 'MONOCHROME2',
                'Rows': 512,
                'Columns': 512,
                'PixelSpacing': [0.878906, 0.878906],
                'BitsAllocated': 16,
                'BitsStored': 16,
                'HighBit': 15,
                'PixelRepresentation': 1,
                'PixelPaddingValue': -2000,
                'WindowCenter': 40,
                'WindowWidth': 350,
                'RescaleIntercept': -1024,
                'RescaleSlope': 1,
                'RescaleType': 'HU'
            },
            
            # 几何信息模板
            'geometry_template': {
                'ImageOrientationPatient': [1.0, 0.0, 0.0, 0.0, 1.0, 0.0],
                'ImagePositionPatient': [-225.0, -225.0, 0.0],  # 会根据切片位置调整Z值
                'SliceLocation': 0.0,  # 会根据切片位置调整
                'PositionReferenceIndicator': 'SN'
            },
            
            # 序列信息
            'series_info': {
                'SeriesDescription': '10MM std',
                'StudyDescription': 'Chest'
            }
        }
    
    def extract_metadata_from_dicom(self, dicom_path: str) -> Dict[str, Any]:
        """从DICOM文件中提取metadata"""
        try:
            ds = pydicom.dcmread(dicom_path, stop_before_pixels=True)
            
            metadata = {}
            
            # 提取设备信息
            device_fields = [
                'Manufacturer', 'ManufacturerModelName', 'StationName',
                'SoftwareVersions', 'DeviceSerialNumber', 'InstitutionName'
            ]
            
            metadata['device_info'] = {}
            for field in device_fields:
                if hasattr(ds, field):
                    metadata['device_info'][field] = str(getattr(ds, field))
            
            # 提取扫描参数
            acquisition_fields = [
                'Modality', 'KVP', 'SliceThickness', 'SpacingBetweenSlices',
                'DataCollectionDiameter', 'ReconstructionDiameter',
                'DistanceSourceToDetector', 'DistanceSourceToPatient',
                'GantryDetectorTilt', 'TableHeight', 'RotationDirection',
                'ExposureTime', 'XRayTubeCurrent', 'Exposure', 'FilterType',
                'GeneratorPower', 'FocalSpots', 'ConvolutionKernel',
                'PatientPosition', 'ScanOptions', 'ProtocolName'
            ]
            
            metadata['acquisition_params'] = {}
            for field in acquisition_fields:
                if hasattr(ds, field):
                    value = getattr(ds, field)
                    metadata['acquisition_params'][field] = str(value) if value is not None else None
            
            # 提取图像参数
            metadata['image_params'] = {}
            if hasattr(ds, 'ImageType'):
                metadata['image_params']['ImageType'] = list(ds.ImageType)
            
            image_fields = [
                'PhotometricInterpretation', 'Rows', 'Columns', 'BitsAllocated',
                'BitsStored', 'HighBit', 'PixelRepresentation', 'PixelPaddingValue',
                'WindowCenter', 'WindowWidth', 'RescaleIntercept', 'RescaleSlope',
                'RescaleType'
            ]
            
            for field in image_fields:
                if hasattr(ds, field):
                    value = getattr(ds, field)
                    metadata['image_params'][field] = value
            
            if hasattr(ds, 'PixelSpacing'):
                metadata['image_params']['PixelSpacing'] = list(ds.PixelSpacing)
            
            # 提取几何信息
            metadata['geometry_info'] = {}
            geometry_fields = [
                'ImageOrientationPatient', 'ImagePositionPatient',
                'SliceLocation', 'PositionReferenceIndicator'
            ]
            
            for field in geometry_fields:
                if hasattr(ds, field):
                    value = getattr(ds, field)
                    if isinstance(value, (list, tuple)):
                        metadata['geometry_info'][field] = list(value)
                    else:
                        metadata['geometry_info'][field] = value
            
            # 提取序列信息
            metadata['series_info'] = {}
            series_fields = ['SeriesDescription', 'StudyDescription']
            for field in series_fields:
                if hasattr(ds, field):
                    metadata['series_info'][field] = str(getattr(ds, field))
            
            return metadata
            
        except Exception as e:
            print(f"提取DICOM metadata失败: {e}")
            return {}
    
    def create_template_from_directory(self, dicom_dir: str) -> Dict[str, Any]:
        """从DICOM目录创建metadata模板"""
        dicom_files = []
        for file_path in Path(dicom_dir).iterdir():
            if file_path.is_file():
                try:
                    pydicom.dcmread(str(file_path), stop_before_pixels=True)
                    dicom_files.append(str(file_path))
                except:
                    continue
        
        if not dicom_files:
            print("未找到有效的DICOM文件")
            return self.ge_ct750_template
        
        # 使用第一个文件作为模板基础
        template = self.extract_metadata_from_dicom(dicom_files[0])
        
        if not template:
            print("使用默认GE CT750模板")
            return self.ge_ct750_template
        
        return template
    
    def get_default_template(self) -> Dict[str, Any]:
        """获取默认的GE CT750模板"""
        return self.ge_ct750_template.copy()
    
    def save_template(self, template: Dict[str, Any], output_path: str):
        """保存模板到JSON文件"""
        try:
            # 处理pydicom的MultiValue类型
            def convert_multivalue(obj):
                if hasattr(obj, '__iter__') and not isinstance(obj, (str, bytes)):
                    return list(obj)
                return obj

            # 递归处理模板中的所有值
            def process_template(data):
                if isinstance(data, dict):
                    return {k: process_template(v) for k, v in data.items()}
                elif isinstance(data, list):
                    return [process_template(item) for item in data]
                else:
                    return convert_multivalue(data)

            processed_template = process_template(template)

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(processed_template, f, indent=2, ensure_ascii=False)
            print(f"模板已保存到: {output_path}")
        except Exception as e:
            print(f"保存模板失败: {e}")
    
    def load_template(self, template_path: str) -> Dict[str, Any]:
        """从JSON文件加载模板"""
        try:
            with open(template_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            print(f"加载模板失败: {e}")
            return self.ge_ct750_template


def main():
    """主函数"""
    analyzer = DicomMetadataAnalyzer()
    
    print("DICOM Metadata分析器")
    print("=" * 50)
    
    # 保存默认模板
    default_template = analyzer.get_default_template()
    analyzer.save_template(default_template, "ge_ct750_template.json")
    
    print("默认GE CT750模板已创建")
    print("可以使用此模板进行JPG转DICOM时的信息还原")


if __name__ == "__main__":
    main()
