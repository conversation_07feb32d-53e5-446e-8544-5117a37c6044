"""
DICOM文件处理模块

负责DICOM文件的读取、写入和元数据管理
"""

import os
import logging
import numpy as np
import pydicom
import SimpleITK as sitk
from typing import List, Dict, Any, Tuple, Optional
from pathlib import Path

logger = logging.getLogger(__name__)


class DICOMHandler:
    """DICOM文件处理器"""
    
    def __init__(self):
        self.dicom_files = []
        self.image_array = None
        self.metadata = {}
        self.original_datasets = []
        
    def load_dicom_series(self, directory: str) -> Tuple[np.ndarray, Dict[str, Any]]:
        """
        加载DICOM系列文件
        
        Args:
            directory: DICOM文件目录路径
            
        Returns:
            图像数组和元数据字典
        """
        try:
            if not os.path.isdir(directory):
                raise ValueError(f"DICOM目录不存在: {directory}")
            
            # 使用SimpleITK读取DICOM系列
            reader = sitk.ImageSeriesReader()
            series_IDs = sitk.ImageSeriesReader.GetGDCMSeriesIDs(directory)
            
            if not series_IDs:
                raise ValueError(f"在目录 {directory} 中未找到有效的DICOM系列")
            
            # 使用第一个系列
            series_ID = series_IDs[0]
            if len(series_IDs) > 1:
                logger.warning(f"发现多个DICOM系列，使用第一个系列 (共{len(series_IDs)}个)")
            
            # 获取文件名列表
            dicom_names = reader.GetGDCMSeriesFileNames(directory, series_ID)
            if not dicom_names:
                raise ValueError(f"无法获取DICOM系列文件名")
            
            self.dicom_files = dicom_names
            logger.info(f"找到 {len(dicom_names)} 个DICOM文件")
            
            # 读取图像数据
            reader.SetFileNames(dicom_names)
            sitk_image = reader.Execute()
            self.image_array = sitk.GetArrayFromImage(sitk_image)
            
            # 提取基本元数据
            self.metadata = {
                'spacing': sitk_image.GetSpacing(),
                'origin': sitk_image.GetOrigin(),
                'direction': sitk_image.GetDirection(),
                'size': sitk_image.GetSize()
            }
            
            # 读取每个DICOM文件的详细元数据
            self._load_detailed_metadata()
            
            logger.info(f"成功加载DICOM系列: {len(dicom_names)}个文件, 形状={self.image_array.shape}")
            return self.image_array, self.metadata
            
        except Exception as e:
            logger.error(f"加载DICOM系列失败 {directory}: {e}")
            raise
    
    def _load_detailed_metadata(self):
        """加载详细的DICOM元数据"""
        try:
            self.original_datasets = []
            
            for file_path in self.dicom_files:
                try:
                    ds = pydicom.dcmread(file_path)
                    self.original_datasets.append(ds)
                except Exception as e:
                    logger.warning(f"读取DICOM文件元数据失败 {file_path}: {e}")
                    self.original_datasets.append(None)
            
            # 从第一个有效文件中提取公共元数据
            first_valid_ds = None
            for ds in self.original_datasets:
                if ds is not None:
                    first_valid_ds = ds
                    break
            
            if first_valid_ds:
                self.metadata.update({
                    'patient_id': getattr(first_valid_ds, 'PatientID', ''),
                    'patient_name': str(getattr(first_valid_ds, 'PatientName', '')),
                    'study_date': getattr(first_valid_ds, 'StudyDate', ''),
                    'study_time': getattr(first_valid_ds, 'StudyTime', ''),
                    'modality': getattr(first_valid_ds, 'Modality', ''),
                    'study_description': getattr(first_valid_ds, 'StudyDescription', ''),
                    'series_description': getattr(first_valid_ds, 'SeriesDescription', ''),
                    'study_instance_uid': getattr(first_valid_ds, 'StudyInstanceUID', ''),
                    'series_instance_uid': getattr(first_valid_ds, 'SeriesInstanceUID', ''),
                    'frame_of_reference_uid': getattr(first_valid_ds, 'FrameOfReferenceUID', ''),
                    'manufacturer': getattr(first_valid_ds, 'Manufacturer', ''),
                    'manufacturer_model_name': getattr(first_valid_ds, 'ManufacturerModelName', ''),
                    'slice_thickness': getattr(first_valid_ds, 'SliceThickness', None),
                    'pixel_spacing': getattr(first_valid_ds, 'PixelSpacing', None),
                    'image_orientation_patient': getattr(first_valid_ds, 'ImageOrientationPatient', None),
                    'rows': getattr(first_valid_ds, 'Rows', 0),
                    'columns': getattr(first_valid_ds, 'Columns', 0),
                    'bits_allocated': getattr(first_valid_ds, 'BitsAllocated', 16),
                    'bits_stored': getattr(first_valid_ds, 'BitsStored', 16),
                    'high_bit': getattr(first_valid_ds, 'HighBit', 15),
                    'pixel_representation': getattr(first_valid_ds, 'PixelRepresentation', 1),
                    'photometric_interpretation': getattr(first_valid_ds, 'PhotometricInterpretation', 'MONOCHROME2'),
                    'rescale_intercept': getattr(first_valid_ds, 'RescaleIntercept', 0),
                    'rescale_slope': getattr(first_valid_ds, 'RescaleSlope', 1),
                    'window_center': getattr(first_valid_ds, 'WindowCenter', None),
                    'window_width': getattr(first_valid_ds, 'WindowWidth', None)
                })
                
        except Exception as e:
            logger.error(f"加载详细元数据失败: {e}")
    
    def get_slice_metadata(self, slice_index: int) -> Optional[pydicom.Dataset]:
        """
        获取指定切片的元数据
        
        Args:
            slice_index: 切片索引
            
        Returns:
            DICOM数据集或None
        """
        if 0 <= slice_index < len(self.original_datasets):
            return self.original_datasets[slice_index]
        return None
    
    def save_dicom_series(self, image_array: np.ndarray, output_directory: str, 
                         crop_region: Optional[Tuple[int, int, int, int]] = None):
        """
        保存图像数组为DICOM系列
        
        Args:
            image_array: 要保存的图像数组
            output_directory: 输出目录
            crop_region: 裁剪区域 (x, y, width, height)，用于更新几何信息
        """
        try:
            os.makedirs(output_directory, exist_ok=True)
            
            if len(self.original_datasets) == 0:
                raise ValueError("没有原始DICOM数据集，无法保存")
            
            num_slices = image_array.shape[0]
            logger.info(f"开始保存 {num_slices} 个DICOM切片到 {output_directory}")
            
            for i in range(num_slices):
                # 获取原始数据集
                original_ds = self.original_datasets[i] if i < len(self.original_datasets) else self.original_datasets[0]
                if original_ds is None:
                    logger.warning(f"切片 {i} 没有原始数据集，跳过")
                    continue
                
                # 创建新的数据集副本
                new_ds = original_ds.copy()
                
                # 更新图像数据
                slice_data = image_array[i]
                new_ds.PixelData = slice_data.astype(original_ds.pixel_array.dtype).tobytes()
                
                # 更新图像尺寸
                new_ds.Rows = slice_data.shape[0]
                new_ds.Columns = slice_data.shape[1]
                
                # 如果有裁剪区域，更新几何信息
                if crop_region:
                    self._update_geometry_for_crop(new_ds, crop_region, i)
                
                # 生成新的SOP Instance UID
                import uuid
                new_ds.SOPInstanceUID = pydicom.uid.generate_uid()
                
                # 保存文件
                output_file = os.path.join(output_directory, f"slice_{i+1:04d}.dcm")
                new_ds.save_as(output_file, write_like_original=False)
                
                if (i + 1) % 10 == 0 or i == num_slices - 1:
                    logger.info(f"保存进度: {i+1}/{num_slices}")
            
            logger.info(f"成功保存DICOM系列到 {output_directory}")
            
        except Exception as e:
            logger.error(f"保存DICOM系列失败: {e}")
            raise
    
    def _update_geometry_for_crop(self, ds: pydicom.Dataset, 
                                 crop_region: Tuple[int, int, int, int], 
                                 slice_index: int):
        """
        更新裁剪后的几何信息
        
        Args:
            ds: DICOM数据集
            crop_region: 裁剪区域 (x, y, width, height)
            slice_index: 切片索引
        """
        try:
            x, y, width, height = crop_region
            
            # 更新像素间距（保持不变）
            if hasattr(ds, 'PixelSpacing') and ds.PixelSpacing:
                pixel_spacing = ds.PixelSpacing
            else:
                pixel_spacing = [1.0, 1.0]
            
            # 更新图像位置
            if hasattr(ds, 'ImagePositionPatient') and ds.ImagePositionPatient:
                # 计算新的图像位置
                original_pos = ds.ImagePositionPatient
                # 根据裁剪偏移调整位置
                new_pos = [
                    float(original_pos[0]) + x * float(pixel_spacing[1]),  # X方向偏移
                    float(original_pos[1]) + y * float(pixel_spacing[0]),  # Y方向偏移
                    float(original_pos[2])  # Z方向保持不变
                ]
                ds.ImagePositionPatient = new_pos
            
        except Exception as e:
            logger.warning(f"更新几何信息失败: {e}")
