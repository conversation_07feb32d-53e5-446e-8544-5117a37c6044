#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICOM文件重新编号工具
解决3D Slicer中DICOM文件排序问题（1、10、100、101...）

作者: AI Assistant
日期: 2025-01-17
"""

import os
import sys
import pydicom
import shutil
from pathlib import Path
import argparse
from typing import List, Tuple
import tempfile


def get_dicom_files(directory: str) -> List[str]:
    """获取目录中的所有DICOM文件"""
    dicom_files = []
    directory = Path(directory)
    
    for file_path in directory.iterdir():
        if file_path.is_file():
            try:
                # 尝试读取DICOM文件
                pydicom.dcmread(str(file_path), stop_before_pixels=True)
                dicom_files.append(str(file_path))
            except:
                # 不是DICOM文件，跳过
                continue
    
    return dicom_files


def extract_instance_info(dicom_files: List[str]) -> List[Tuple[str, int, float, str]]:
    """提取DICOM文件的实例信息"""
    file_info = []

    for file_path in dicom_files:
        try:
            ds = pydicom.dcmread(file_path, stop_before_pixels=True)

            # 获取Instance Number
            instance_number = getattr(ds, 'InstanceNumber', len(file_info) + 1)

            # 尝试多种方式获取切片位置
            slice_location = None

            # 方法1: 直接使用SliceLocation
            if hasattr(ds, 'SliceLocation') and ds.SliceLocation is not None:
                slice_location = float(ds.SliceLocation)

            # 方法2: 使用ImagePositionPatient的Z坐标
            elif hasattr(ds, 'ImagePositionPatient') and ds.ImagePositionPatient is not None:
                if len(ds.ImagePositionPatient) >= 3:
                    slice_location = float(ds.ImagePositionPatient[2])

            # 方法3: 使用Instance Number作为位置
            if slice_location is None:
                slice_location = float(instance_number)
                print(f"警告: {os.path.basename(file_path)} 缺少位置信息，使用Instance Number")

            # 获取原始文件名用于调试
            original_filename = os.path.basename(file_path)

            file_info.append((file_path, int(instance_number), slice_location, original_filename))

        except Exception as e:
            print(f"错误: 无法读取文件 {file_path}: {e}")
            continue

    return file_info


def renumber_dicom_files(input_dir: str, output_dir: str = None, backup: bool = True):
    """重新编号DICOM文件"""
    
    input_path = Path(input_dir)
    if not input_path.exists():
        raise FileNotFoundError(f"输入目录不存在: {input_dir}")
    
    # 如果没有指定输出目录，就在原目录操作
    if output_dir is None:
        output_dir = input_dir
        in_place = True
    else:
        in_place = False
        os.makedirs(output_dir, exist_ok=True)
    
    # 获取所有DICOM文件
    print("正在扫描DICOM文件...")
    dicom_files = get_dicom_files(input_dir)
    
    if not dicom_files:
        print("未找到DICOM文件！")
        return
    
    print(f"找到 {len(dicom_files)} 个DICOM文件")
    
    # 提取实例信息
    print("正在分析文件信息...")
    file_info = extract_instance_info(dicom_files)
    
    if not file_info:
        print("无法读取任何DICOM文件信息！")
        return
    
    # 按切片位置排序，如果位置相同则按Instance Number排序
    file_info.sort(key=lambda x: (x[2], x[1]))

    print(f"文件排序信息 (共{len(file_info)}个文件):")
    for i, (file_path, old_instance, slice_loc, orig_name) in enumerate(file_info[:10]):
        print(f"  {i+1:3d}. {orig_name} (原Instance: {old_instance}, 位置: {slice_loc:.2f})")
    if len(file_info) > 10:
        print(f"  ... 还有 {len(file_info)-10} 个文件")

    # 检查是否有重复的位置
    positions = [info[2] for info in file_info]
    unique_positions = set(positions)
    if len(positions) != len(unique_positions):
        print(f"警告: 发现 {len(positions) - len(unique_positions)} 个重复位置")
    
    # 创建备份（如果需要且是原地操作）
    if backup and in_place:
        backup_dir = input_path.parent / f"{input_path.name}_backup"
        if backup_dir.exists():
            print(f"备份目录已存在: {backup_dir}")
            response = input("是否覆盖备份？(y/N): ").strip().lower()
            if response != 'y':
                print("操作取消")
                return
            shutil.rmtree(backup_dir)
        
        print(f"正在创建备份到: {backup_dir}")
        shutil.copytree(input_dir, backup_dir)
    
    # 重新编号并保存文件
    print("正在重新编号DICOM文件...")
    print("重新排序映射:")
    # 根据文件总数决定位数
    total_files = len(file_info)
    if total_files < 1000:
        digits = 3
    elif total_files < 10000:
        digits = 4
    else:
        digits = 5

    for i in range(min(10, len(file_info))):
        old_name = file_info[i][3]  # 使用存储的原始文件名
        new_name = f"{i+1:04d}.dcm"
        print(f"  位置{i+1}: {old_name} -> {new_name} (原Instance: {file_info[i][1]}, 位置: {file_info[i][2]:.2f})")
    if len(file_info) > 10:
        print(f"  ... 还有 {len(file_info)-10} 个文件")

    # 使用临时目录避免文件名冲突
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_files = []

        for i, (file_path, old_instance, slice_loc, orig_name) in enumerate(file_info):
            new_instance_number = i + 1

            try:
                # 读取DICOM文件
                ds = pydicom.dcmread(file_path)

                # 更新Instance Number
                ds.InstanceNumber = new_instance_number

                # 更新Slice Location确保连续性
                ds.SliceLocation = float(i)

                # 确保完整的几何信息
                # 1. Image Position Patient (必须有完整的X,Y,Z坐标)
                if not hasattr(ds, 'ImagePositionPatient') or ds.ImagePositionPatient is None:
                    ds.ImagePositionPatient = [0.0, 0.0, float(i)]
                else:
                    # 确保是3个元素的列表
                    if len(ds.ImagePositionPatient) < 3:
                        ds.ImagePositionPatient = [0.0, 0.0, float(i)]
                    else:
                        ds.ImagePositionPatient[2] = float(i)

                # 2. Image Orientation Patient (定义图像方向)
                if not hasattr(ds, 'ImageOrientationPatient') or ds.ImageOrientationPatient is None:
                    # 标准轴向图像方向：行方向(1,0,0)，列方向(0,1,0)
                    ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]

                # 3. Pixel Spacing (像素间距)
                if not hasattr(ds, 'PixelSpacing') or ds.PixelSpacing is None:
                    ds.PixelSpacing = [1.0, 1.0]  # 默认1mm x 1mm

                # 4. Slice Thickness (切片厚度)
                if not hasattr(ds, 'SliceThickness') or ds.SliceThickness is None:
                    ds.SliceThickness = 1.0  # 默认1mm

                # 5. Spacing Between Slices (切片间距)
                if not hasattr(ds, 'SpacingBetweenSlices') or ds.SpacingBetweenSlices is None:
                    ds.SpacingBetweenSlices = 1.0  # 默认1mm

                # 重要：按新的顺序重新命名文件
                # 统一使用4位数格式化
                new_filename = f"{new_instance_number:04d}.dcm"

                temp_file = os.path.join(temp_dir, new_filename)
                ds.save_as(temp_file, write_like_original=False)
                temp_files.append((temp_file, file_path, new_filename))

                if i % 50 == 0 or i == len(file_info) - 1:
                    print(f"  处理进度: {i+1}/{len(file_info)} - {orig_name} -> {new_filename}")

            except Exception as e:
                print(f"错误: 处理文件 {file_path} 时出错: {e}")
                continue
        
        # 将临时文件移动到最终位置
        print("正在保存文件...")

        # 如果是原地修改，先删除所有原始文件
        if in_place:
            print("正在删除原始文件...")
            for _, original_path, _ in temp_files:
                if os.path.exists(original_path):
                    os.remove(original_path)

        # 保存新文件
        for i, (temp_file, original_path, new_filename) in enumerate(temp_files):
            if in_place:
                # 原地修改：使用新的文件名
                final_path = os.path.join(os.path.dirname(original_path), new_filename)
            else:
                # 输出到新目录：使用新的文件名
                final_path = os.path.join(output_dir, new_filename)

            shutil.move(temp_file, final_path)
            if len(temp_files) <= 20 or (len(temp_files) > 20 and i % 50 == 0) or i == len(temp_files) - 1:
                print(f"  保存: {new_filename} ({i+1}/{len(temp_files)})")
    
    print(f"完成！处理了 {len(temp_files)} 个文件")
    if backup and in_place:
        print(f"原始文件已备份到: {backup_dir}")


def main():
    parser = argparse.ArgumentParser(description='DICOM文件重新编号工具')
    parser.add_argument('input_dir', help='输入DICOM文件目录')
    parser.add_argument('-o', '--output', help='输出目录（可选，默认原地修改）')
    parser.add_argument('--no-backup', action='store_true', help='不创建备份（仅在原地修改时）')
    
    args = parser.parse_args()
    
    try:
        renumber_dicom_files(
            args.input_dir, 
            args.output, 
            backup=not args.no_backup
        )
    except Exception as e:
        print(f"错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
