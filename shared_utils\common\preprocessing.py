import numpy as np
import SimpleITK as sitk
import logging
from typing import Optional

logger = logging.getLogger(__name__)

def preprocess_ct_image(image: np.ndarray, 
                        target_spacing: Optional[tuple] = None,  # 默认不重采样
                        clip_hu: bool = True,
                        normalize: bool = True) -> np.ndarray:
    """
    预处理CT图像，包括重采样、HU值裁剪和归一化
    
    Args:
        image: 输入CT图像数组
        target_spacing: 目标体素间距，用于重采样
        clip_hu: 是否裁剪Hounsfield单位值到标准范围
        normalize: 是否归一化图像强度
        
    Returns:
        预处理后的图像数组
    """
    try:
        # 打印图像信息
        logger.info(f"原始图像信息: 形状={image.shape}, 类型={image.dtype}, 最小值={image.min()}, 最大值={image.max()}")
        
        # 转换为SimpleITK图像
        sitk_image = sitk.GetImageFromArray(image)
        
        # 1. 重采样到目标间距（如果提供）
        if target_spacing is not None:
            original_spacing = sitk_image.GetSpacing()
            original_size = sitk_image.GetSize()
            new_size = [
                int(round(original_size[i] * (original_spacing[i] / target_spacing[i])))
                for i in range(3)
            ]
            resampler = sitk.ResampleImageFilter()
            resampler.SetOutputSpacing(target_spacing)
            resampler.SetSize(new_size)
            resampler.SetOutputDirection(sitk_image.GetDirection())
            resampler.SetOutputOrigin(sitk_image.GetOrigin())
            resampler.SetTransform(sitk.Transform())
            resampler.SetDefaultPixelValue(sitk_image.GetPixelIDValue())
            resampler.SetInterpolator(sitk.sitkBSpline)
            sitk_image = resampler.Execute(sitk_image)
            image = sitk.GetArrayFromImage(sitk_image)
        
        # 2. 裁剪HU值到肺部CT典型范围
        if clip_hu:
            # 肺部CT的典型HU值范围为-1000到400
            hu_min, hu_max = -1000, 400
            image = np.clip(image, hu_min, hu_max)
            logger.info(f"完成HU值裁剪: 范围=[{hu_min}, {hu_max}]")
        
        # 3. 归一化到[0, 1]范围
        if normalize:
            # 肺部组织的归一化，将空气(-1000 HU)映射到0，水(0 HU)映射到1
            if clip_hu:
                # 如果已裁剪HU值，使用裁剪范围进行归一化
                image = (image - hu_min) / (hu_max - hu_min)
            else:
                # 否则使用图像的实际范围
                image = (image - image.min()) / (image.max() - image.min() + 1e-6)
            
            logger.info(f"完成图像归一化: 新范围=[{image.min():.4f}, {image.max():.4f}]")
        
        # 打印预处理后的图像信息
        logger.info(f"预处理后图像信息: 形状={image.shape}, 类型={image.dtype}, 最小值={image.min():.4f}, 最大值={image.max():.4f}")
        
        return image
        
    except Exception as e:
        logger.error(f"图像预处理失败: {e}")
        raise