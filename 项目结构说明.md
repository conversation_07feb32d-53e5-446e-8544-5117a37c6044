# 项目结构说明

## 整体架构

本项目已按功能模块重新组织，采用模块化设计，每个功能模块相对独立，便于维护和扩展。

```
lung_ct_segmenter/
├── lung_segmentation/          # 肺组织分割模块
│   ├── core/                   # 核心分割算法
│   ├── models/                 # AI模型封装
│   ├── apps/                   # GUI应用程序
│   ├── examples/               # 使用示例
│   └── tests/                  # 测试文件
├── image_to_dicom/             # 图片转DICOM模块
│   ├── core/                   # 核心转换功能
│   ├── gui/                    # 图形用户界面
│   ├── templates/              # DICOM模板文件
│   └── docs/                   # 使用文档
├── dicom_tools/                # DICOM工具模块
│   ├── renumber/               # 重新编号工具
│   ├── debug/                  # 调试验证工具
│   ├── geometry_fix/           # 几何修复工具
│   └── hu_adjustment/          # HU值调整工具
├── ct_region_tools/            # CT区域裁剪工具
│   ├── core/                   # 核心裁剪功能
│   ├── gui/                    # 图形用户界面
│   ├── utils/                  # 工具函数
│   └── tests/                  # 测试文件
├── shared_utils/               # 共享工具模块
│   ├── io/                     # 文件I/O操作
│   ├── preprocessing/          # 图像预处理
│   └── common/                 # 通用工具函数
├── run_*.py                    # 各模块启动脚本
├── requirements.txt            # 依赖包列表
└── README.md                   # 项目说明
```

## 各模块详细说明

### 1. 肺组织分割模块 (lung_segmentation)

**功能**：提供完整的肺部CT分割和组织分析功能

**主要文件**：
- `core/segmenter.py` - 主要分割器类
- `core/ai_models.py` - AI模型基类
- `models/lungmask_wrapper.py` - Lungmask模型封装
- `models/totalseg_wrapper.py` - TotalSegmentator模型封装
- `apps/gui_app.py` - 分割GUI应用
- `apps/lung_tissue_analyzer.py` - 组织分析GUI应用

**启动方式**：
```bash
python run_lung_segmentation.py      # 启动分割工具
python run_lung_tissue_analyzer.py   # 启动分析工具
```

### 2. 图片转DICOM模块 (image_to_dicom)

**功能**：将JPG/PNG等格式图片转换为DICOM格式

**主要文件**：
- `core/enhanced_jpg_to_dicom.py` - 增强版转换器
- `core/hu_value_mapper.py` - HU值映射器
- `gui/enhanced_jpg_to_dicom_gui.py` - 增强版GUI
- `templates/ge_ct750_template.json` - DICOM模板

**启动方式**：
```bash
python run_image_to_dicom.py
```

### 3. DICOM工具模块 (dicom_tools)

**功能**：提供各种DICOM文件处理工具

**子模块**：
- `renumber/` - DICOM文件重新编号
- `debug/` - DICOM调试和验证
- `geometry_fix/` - DICOM几何信息修复
- `hu_adjustment/` - HU值调整

**启动方式**：
```bash
python run_dicom_renumber.py         # DICOM重新编号工具
```

### 4. CT区域裁剪工具 (ct_region_tools)

**功能**：CT影像区域选框分割，保持完整DICOM元数据

**主要文件**：
- `core/region_cropper.py` - 区域裁剪核心功能
- `core/dicom_handler.py` - DICOM文件处理
- `gui/main_window.py` - 主窗口界面
- `gui/image_viewer.py` - 图像显示组件

**启动方式**：
```bash
python run_ct_region_cropper.py
```

### 5. 共享工具模块 (shared_utils)

**功能**：提供各模块共享的工具函数

**主要文件**：
- `common/io_utils.py` - 文件I/O操作
- `common/preprocessing.py` - 图像预处理
- `common/__init__.py` - 模块初始化

## 使用建议

1. **新用户**：建议从肺组织分割模块开始，这是项目的核心功能
2. **开发者**：每个模块都有独立的`__init__.py`文件，可以作为Python包导入使用
3. **扩展功能**：新功能建议按照现有模块结构添加，保持代码组织的一致性

## 依赖关系

- 各功能模块尽量保持独立，减少相互依赖
- 共享功能统一放在`shared_utils`模块中
- 所有模块都可以使用`shared_utils`中的工具函数
