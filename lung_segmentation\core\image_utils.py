import numpy as np
import <PERSON>IT<PERSON> as sitk
from typing import Op<PERSON>, Dict, Tuple, Union
import logging
from pathlib import Path

from .ai_models import LungMaskModel, TotalSegmentatorModel
from .postprocessing import PostProcessor
from ..utils.image_io import ImageIO
from ..utils.preprocessing import Preprocessor
from ..config.settings import DEFAULT_MODEL_TYPE, DEFAULT_MODEL_NAME, DEFAULT_DEVICE, LOG_LEVEL


class LungCTSegmenter:
    """
    独立的肺部CT分割器，支持多种AI模型进行肺部和气道分割
    """

    def __init__(self,
                 model_type: str = DEFAULT_MODEL_TYPE,
                 model_name: str = DEFAULT_MODEL_NAME,
                 device: str = DEFAULT_DEVICE):
        """
        初始化分割器

        Args:
            model_type: AI模型类型 ("lungmask" 或 "totalsegmentator")
            model_name: 具体模型名称
            device: 计算设备 ("cpu", "cuda", "auto")
        """
        self.model_type = model_type
        self.model_name = model_name
        self.device = device

        # 设置日志 - 移到前面
        logging.basicConfig(level=getattr(logging, LOG_LEVEL))
        self.logger = logging.getLogger(__name__)

        # 初始化组件
        self.image_io = ImageIO()
        self.preprocessor = Preprocessor()
        self.postprocessor = PostProcessor()

        # 初始化AI模型
        self._init_model()

        # 设置日志
        #logging.basicConfig(level=getattr(logging, LOG_LEVEL))
        #self.logger = logging.getLogger(__name__)

    def _init_model(self):
        """初始化AI模型"""
        try:
            if self.model_type.lower() == "lungmask":
                self.model = LungMaskModel(self.model_name, self.device)
            elif self.model_type.lower() == "totalsegmentator":
                self.model = TotalSegmentatorModel(self.model_name, self.device)
            else:
                raise ValueError(f"不支持的模型类型: {self.model_type}")
        except Exception as e:
            self.logger.error(f"初始化模型失败: {e}")
            raise

    def segment_from_file(self,
                          input_path: Union[str, Path],
                          output_dir: Union[str, Path],
                          include_airways: bool = True,
                          include_lobes: bool = True,
                          smooth_results: bool = True) -> Dict[str, str]:
        """
        从文件路径进行分割

        Args:
            input_path: 输入CT图像路径
            output_dir: 输出目录
            include_airways: 是否包含气道分割
            include_lobes: 是否包含肺叶分割
            smooth_results: 是否平滑结果

        Returns:
            包含输出文件路径的字典
        """
        # 读取图像
        self.logger.info(f"读取图像: {input_path}")
        image = self.image_io.load_image(input_path)

        # 执行分割
        results = self.segment(image, include_airways, include_lobes, smooth_results)

        # 保存结果
        output_paths = self._save_results(results, output_dir, Path(input_path).stem)

        return output_paths

    def segment(self,
                image: sitk.Image,
                include_airways: bool = True,
                include_lobes: bool = True,
                smooth_results: bool = True) -> Dict[str, np.ndarray]:
        """
        执行肺部分割

        Args:
            image: SimpleITK图像对象
            include_airways: 是否包含气道分割
            include_lobes: 是否包含肺叶分割
            smooth_results: 是否平滑结果

        Returns:
            分割结果字典，键为结构名称，值为分割掩码
        """
        self.logger.info("开始肺部分割...")

        # 预处理
        processed_image = self.preprocessor.preprocess(image)

        # AI分割
        segmentation_results = self.model.predict(processed_image)

        # 后处理
        if smooth_results:
            segmentation_results = self.postprocessor.smooth_segmentations(
                segmentation_results, image)

        # 根据需求过滤结果
        filtered_results = self._filter_results(
            segmentation_results, include_airways, include_lobes)

        self.logger.info("分割完成")
        return filtered_results

    def _filter_results(self,
                        results: Dict[str, np.ndarray],
                        include_airways: bool,
                        include_lobes: bool) -> Dict[str, np.ndarray]:
        """根据用户需求过滤分割结果"""
        filtered = {}
        # 肺部相关结构的关键词映射
        lung_keywords = ["lung", "lobe"]
        airway_keywords = ["airway", "trachea", "bronchi"]

        for key in results:
            key_lower = key.lower()
            is_lung = any(kw in key_lower for kw in lung_keywords)
            is_lobe = "lobe" in key_lower
            is_airway = any(kw in key_lower for kw in airway_keywords)

            if is_lung:
                if include_lobes or not is_lobe:
                    filtered[key] = results[key]
            elif is_airway and include_airways:
                filtered[key] = results[key]

        return filtered

    def _save_results(self,
                      results: Dict[str, np.ndarray],
                      output_dir: Union[str, Path],
                      base_name: str) -> Dict[str, str]:
        """保存分割结果为NIFTI格式"""
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        output_paths = {}

        for structure_name, mask in results.items():
            # 清理文件名
            clean_name = structure_name.replace(" ", "_").lower()
            output_path = output_dir / f"{base_name}_{clean_name}.nii.gz"

            # 保存为NIFTI
            self.image_io.save_mask(mask, output_path)
            output_paths[structure_name] = str(output_path)

            self.logger.info(f"保存 {structure_name}: {output_path}")

        return output_paths

    def batch_process(self,
                      input_dir: Union[str, Path],
                      output_dir: Union[str, Path],
                      file_pattern: str = "*.nii*",
                      **kwargs) -> Dict[str, Dict[str, str]]:
        """
        批量处理目录中的CT图像

        Args:
            input_dir: 输入目录
            output_dir: 输出目录
            file_pattern: 文件匹配模式
            **kwargs: 传递给segment_from_file的其他参数

        Returns:
            每个文件的处理结果
        """
        input_dir = Path(input_dir)
        output_dir = Path(output_dir)

        # 查找所有匹配的文件
        input_files = list(input_dir.glob(file_pattern))

        if not input_files:
            raise ValueError(f"在 {input_dir} 中未找到匹配 {file_pattern} 的文件")

        self.logger.info(f"找到 {len(input_files)} 个文件进行处理")

        results = {}
        for i, input_file in enumerate(input_files, 1):
            self.logger.info(f"处理文件 {i}/{len(input_files)}: {input_file.name}")

            try:
                # 为每个文件创建子目录
                file_output_dir = output_dir / input_file.stem
                file_results = self.segment_from_file(input_file, file_output_dir, **kwargs)
                results[str(input_file)] = file_results

            except Exception as e:
                error_msg = f"处理文件 {input_file} 时出错: {e}"
                self.logger.error(error_msg)
                results[str(input_file)] = {"error": str(e)}

        return results
