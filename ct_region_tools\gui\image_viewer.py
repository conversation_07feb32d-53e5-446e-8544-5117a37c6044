"""
图像显示组件

提供CT图像的显示和交互功能
"""

import numpy as np
import logging
from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QHBoxLayout, QLabel, 
                            QSlider, QSpinBox, QPushButton, QCheckBox,
                            QDoubleSpinBox, QGroupBox)
from PyQt5.QtCore import Qt, pyqtSignal
from PyQt5.QtGui import QPixmap, QImage
import matplotlib.pyplot as plt
from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
from matplotlib.figure import Figure
import matplotlib.patches as patches

try:
    from ..utils.image_utils import ImageUtils
except ImportError:
    from utils.image_utils import ImageUtils

logger = logging.getLogger(__name__)


class ImageViewer(QWidget):
    """图像显示器组件"""
    
    # 信号定义
    slice_changed = pyqtSignal(int)  # 切片改变信号
    crop_region_changed = pyqtSignal(int, int, int, int)  # 裁剪区域改变信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.image_array = None
        self.current_slice = 0
        self.crop_region = None
        self.window_center = None
        self.window_width = None
        
        self.setup_ui()
        self.setup_connections()
        
    def setup_ui(self):
        """设置用户界面"""
        layout = QVBoxLayout(self)
        
        # 图像显示区域
        self.figure = Figure(figsize=(8, 8))
        self.canvas = FigureCanvas(self.figure)
        self.ax = self.figure.add_subplot(111)
        self.ax.set_aspect('equal')
        self.ax.axis('off')
        
        layout.addWidget(self.canvas)
        
        # 控制面板
        control_panel = self.create_control_panel()
        layout.addWidget(control_panel)
        
        # 切片控制
        slice_control = self.create_slice_control()
        layout.addWidget(slice_control)
        
    def create_control_panel(self):
        """创建控制面板"""
        group = QGroupBox("显示控制")
        layout = QVBoxLayout(group)
        
        # 窗宽窗位控制
        window_layout = QHBoxLayout()
        
        window_layout.addWidget(QLabel("窗位:"))
        self.window_center_spin = QDoubleSpinBox()
        self.window_center_spin.setRange(-10000, 50000)  # 扩大范围以支持特殊CT影像
        self.window_center_spin.setValue(32824)  # 设置为您的特殊CT影像的最佳窗位
        self.window_center_spin.setSingleStep(100)  # 增大步长以便快速调整
        window_layout.addWidget(self.window_center_spin)

        window_layout.addWidget(QLabel("窗宽:"))
        self.window_width_spin = QDoubleSpinBox()
        self.window_width_spin.setRange(1, 10000)  # 扩大范围以支持特殊CT影像
        self.window_width_spin.setValue(1265)  # 设置为您的特殊CT影像的最佳窗宽
        self.window_width_spin.setSingleStep(50)  # 增大步长以便快速调整
        window_layout.addWidget(self.window_width_spin)
        
        # 窗宽窗位预设按钮
        preset_layout = QHBoxLayout()

        # 自动窗宽窗位按钮
        self.auto_window_btn = QPushButton("自动窗宽窗位")
        preset_layout.addWidget(self.auto_window_btn)

        # 预设窗宽窗位按钮
        self.special_preset_btn = QPushButton("特殊CT预设")
        self.special_preset_btn.setToolTip("WW=1265, WL=32824")
        preset_layout.addWidget(self.special_preset_btn)

        self.lung_preset_btn = QPushButton("肺窗")
        self.lung_preset_btn.setToolTip("WW=1600, WL=-600")
        preset_layout.addWidget(self.lung_preset_btn)

        self.soft_tissue_btn = QPushButton("软组织窗")
        self.soft_tissue_btn.setToolTip("WW=400, WL=40")
        preset_layout.addWidget(self.soft_tissue_btn)

        window_layout.addLayout(preset_layout)
        
        layout.addLayout(window_layout)
        
        # 显示选项
        display_layout = QHBoxLayout()
        
        self.show_crop_overlay = QCheckBox("显示裁剪区域")
        self.show_crop_overlay.setChecked(True)
        display_layout.addWidget(self.show_crop_overlay)
        
        self.show_crosshair = QCheckBox("显示十字线")
        display_layout.addWidget(self.show_crosshair)
        
        layout.addLayout(display_layout)
        
        return group
        
    def create_slice_control(self):
        """创建切片控制"""
        group = QGroupBox("切片控制")
        layout = QHBoxLayout(group)
        
        layout.addWidget(QLabel("切片:"))
        
        # 切片滑块
        self.slice_slider = QSlider(Qt.Horizontal)
        self.slice_slider.setMinimum(0)
        self.slice_slider.setMaximum(0)
        self.slice_slider.setValue(0)
        layout.addWidget(self.slice_slider)
        
        # 切片数值框
        self.slice_spin = QSpinBox()
        self.slice_spin.setMinimum(0)
        self.slice_spin.setMaximum(0)
        self.slice_spin.setValue(0)
        layout.addWidget(self.slice_spin)
        
        # 切片信息标签
        self.slice_info_label = QLabel("0 / 0")
        layout.addWidget(self.slice_info_label)
        
        return group
        
    def setup_connections(self):
        """设置信号连接"""
        self.slice_slider.valueChanged.connect(self.on_slice_changed)
        self.slice_spin.valueChanged.connect(self.on_slice_changed)
        self.window_center_spin.valueChanged.connect(self.update_display)
        self.window_width_spin.valueChanged.connect(self.update_display)
        self.auto_window_btn.clicked.connect(self.auto_adjust_window)
        self.special_preset_btn.clicked.connect(self.apply_special_preset)
        self.lung_preset_btn.clicked.connect(self.apply_lung_preset)
        self.soft_tissue_btn.clicked.connect(self.apply_soft_tissue_preset)
        self.show_crop_overlay.toggled.connect(self.update_display)
        self.show_crosshair.toggled.connect(self.update_display)
        
        # 鼠标事件
        self.canvas.mpl_connect('button_press_event', self.on_mouse_press)
        self.canvas.mpl_connect('button_release_event', self.on_mouse_release)
        self.canvas.mpl_connect('motion_notify_event', self.on_mouse_move)
        
        # 初始化鼠标状态
        self.mouse_pressed = False
        self.crop_start_pos = None
        self.crop_rect = None
        
    def load_image_array(self, image_array: np.ndarray):
        """
        加载图像数组
        
        Args:
            image_array: 图像数组 (slices, height, width)
        """
        try:
            self.image_array = image_array
            num_slices = image_array.shape[0]
            
            # 更新切片控制
            self.slice_slider.setMaximum(num_slices - 1)
            self.slice_spin.setMaximum(num_slices - 1)
            self.current_slice = 0
            self.slice_slider.setValue(0)
            self.slice_spin.setValue(0)
            
            # 计算自动窗宽窗位
            self.auto_adjust_window()
            
            # 更新显示
            self.update_display()
            
            logger.info(f"加载图像数组成功，形状: {image_array.shape}")
            
        except Exception as e:
            logger.error(f"加载图像数组失败: {e}")
    
    def on_slice_changed(self, value):
        """切片改变事件处理"""
        if self.image_array is None:
            return
            
        self.current_slice = value
        
        # 同步滑块和数值框
        if self.sender() == self.slice_slider:
            self.slice_spin.setValue(value)
        elif self.sender() == self.slice_spin:
            self.slice_slider.setValue(value)
        
        # 更新切片信息
        total_slices = self.image_array.shape[0]
        self.slice_info_label.setText(f"{value + 1} / {total_slices}")
        
        # 更新显示
        self.update_display()
        
        # 发送信号
        self.slice_changed.emit(value)
    
    def update_display(self):
        """更新图像显示"""
        if self.image_array is None:
            return
            
        try:
            # 获取当前切片
            current_image = self.image_array[self.current_slice]
            
            # 应用窗宽窗位
            window_center = self.window_center_spin.value()
            window_width = self.window_width_spin.value()
            
            display_image = ImageUtils.normalize_image(
                current_image, window_center, window_width
            )
            
            # 清除之前的显示
            self.ax.clear()
            self.ax.set_aspect('equal')
            self.ax.axis('off')
            
            # 显示图像
            self.ax.imshow(display_image, cmap='gray', origin='upper')
            
            # 显示裁剪区域覆盖层
            if self.show_crop_overlay.isChecked() and self.crop_region is not None:
                x, y, width, height = self.crop_region
                rect = patches.Rectangle(
                    (x, y), width, height,
                    linewidth=2, edgecolor='red', facecolor='none'
                )
                self.ax.add_patch(rect)
            
            # 显示十字线
            if self.show_crosshair.isChecked():
                img_height, img_width = current_image.shape
                center_x, center_y = img_width // 2, img_height // 2
                self.ax.axhline(y=center_y, color='yellow', linestyle='--', alpha=0.7)
                self.ax.axvline(x=center_x, color='yellow', linestyle='--', alpha=0.7)
            
            # 设置显示范围
            self.ax.set_xlim(0, current_image.shape[1])
            self.ax.set_ylim(current_image.shape[0], 0)
            
            # 刷新画布
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"更新显示失败: {e}")
    
    def auto_adjust_window(self):
        """自动调整窗宽窗位"""
        if self.image_array is None:
            return
            
        try:
            current_image = self.image_array[self.current_slice]
            window_center, window_width = ImageUtils.get_optimal_window(current_image)
            
            self.window_center_spin.setValue(window_center)
            self.window_width_spin.setValue(window_width)
            
            self.update_display()
            
        except Exception as e:
            logger.error(f"自动调整窗宽窗位失败: {e}")

    def apply_special_preset(self):
        """应用特殊CT影像预设窗宽窗位"""
        try:
            self.window_center_spin.setValue(32824)
            self.window_width_spin.setValue(1265)
            self.update_display()
            logger.info("已应用特殊CT预设: WW=1265, WL=32824")
        except Exception as e:
            logger.error(f"应用特殊CT预设失败: {e}")

    def apply_lung_preset(self):
        """应用肺窗预设窗宽窗位"""
        try:
            self.window_center_spin.setValue(-600)
            self.window_width_spin.setValue(1600)
            self.update_display()
            logger.info("已应用肺窗预设: WW=1600, WL=-600")
        except Exception as e:
            logger.error(f"应用肺窗预设失败: {e}")

    def apply_soft_tissue_preset(self):
        """应用软组织窗预设窗宽窗位"""
        try:
            self.window_center_spin.setValue(40)
            self.window_width_spin.setValue(400)
            self.update_display()
            logger.info("已应用软组织窗预设: WW=400, WL=40")
        except Exception as e:
            logger.error(f"应用软组织窗预设失败: {e}")

    def on_mouse_press(self, event):
        """鼠标按下事件"""
        if event.inaxes != self.ax or self.image_array is None:
            return
            
        if event.button == 1:  # 左键
            self.mouse_pressed = True
            self.crop_start_pos = (int(event.xdata), int(event.ydata))
            
            # 移除之前的矩形
            if self.crop_rect:
                self.crop_rect.remove()
                self.crop_rect = None
    
    def on_mouse_move(self, event):
        """鼠标移动事件"""
        if not self.mouse_pressed or event.inaxes != self.ax or self.crop_start_pos is None:
            return
            
        try:
            current_pos = (int(event.xdata), int(event.ydata))
            
            # 计算矩形参数
            x1, y1 = self.crop_start_pos
            x2, y2 = current_pos
            
            x = min(x1, x2)
            y = min(y1, y2)
            width = abs(x2 - x1)
            height = abs(y2 - y1)
            
            # 移除之前的矩形
            if self.crop_rect:
                self.crop_rect.remove()
            
            # 绘制新矩形
            self.crop_rect = patches.Rectangle(
                (x, y), width, height,
                linewidth=2, edgecolor='red', facecolor='none', linestyle='--'
            )
            self.ax.add_patch(self.crop_rect)
            self.canvas.draw()
            
        except Exception as e:
            logger.error(f"鼠标移动事件处理失败: {e}")
    
    def on_mouse_release(self, event):
        """鼠标释放事件"""
        if not self.mouse_pressed or event.inaxes != self.ax or self.crop_start_pos is None:
            return
            
        try:
            self.mouse_pressed = False
            current_pos = (int(event.xdata), int(event.ydata))
            
            # 计算最终的裁剪区域
            x1, y1 = self.crop_start_pos
            x2, y2 = current_pos
            
            x = min(x1, x2)
            y = min(y1, y2)
            width = abs(x2 - x1)
            height = abs(y2 - y1)
            
            # 验证区域大小
            if width > 5 and height > 5:  # 最小尺寸限制
                # 确保区域在图像范围内
                img_height, img_width = self.image_array.shape[1], self.image_array.shape[2]
                x = max(0, min(x, img_width - 1))
                y = max(0, min(y, img_height - 1))
                width = min(width, img_width - x)
                height = min(height, img_height - y)
                
                self.crop_region = (x, y, width, height)
                
                # 发送信号
                self.crop_region_changed.emit(x, y, width, height)
                
                logger.info(f"设置裁剪区域: ({x}, {y}, {width}, {height})")
            
            # 清理临时矩形
            if self.crop_rect:
                self.crop_rect.remove()
                self.crop_rect = None
            
            # 更新显示
            self.update_display()
            
        except Exception as e:
            logger.error(f"鼠标释放事件处理失败: {e}")
    
    def set_crop_region(self, x: int, y: int, width: int, height: int):
        """
        设置裁剪区域
        
        Args:
            x: 左上角X坐标
            y: 左上角Y坐标
            width: 宽度
            height: 高度
        """
        self.crop_region = (x, y, width, height)
        self.update_display()
    
    def clear_crop_region(self):
        """清除裁剪区域"""
        self.crop_region = None
        self.update_display()
    
    def get_current_slice_image(self) -> np.ndarray:
        """获取当前切片图像"""
        if self.image_array is None:
            return None
        return self.image_array[self.current_slice]

    def get_crop_region(self):
        """获取当前裁剪区域"""
        return self.crop_region

    def get_current_slice_index(self) -> int:
        """获取当前切片索引"""
        return self.current_slice
