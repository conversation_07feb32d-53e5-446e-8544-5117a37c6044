import numpy as np
import SimpleITK as sitk
from abc import ABC, abstractmethod
from typing import Dict, Optional
import logging
import os


class BaseModel(ABC):
    def __init__(self, model_name: str, device: str = "auto"):
        print("BaseModel init called, logger set")  # 调试用
        self.model_name = model_name
        self.device = self._setup_device(device)
        self.logger = logging.getLogger(__name__)  # 修复：取消注释，确保logger正确初始化

    def _setup_device(self, device: str) -> str:
        """设置计算设备"""
        if device == "auto":
            try:
                import torch
                if torch.cuda.is_available():
                    self.logger.info("检测到CUDA设备，使用GPU加速")
                    return "cuda"
                else:
                    self.logger.info("未检测到CUDA设备，使用CPU")
                    return "cpu"
            except ImportError:
                self.logger.warning("无法导入torch，默认为CPU设备。请确保安装了PyTorch以支持GPU加速。")
                return "cpu"
            except Exception as e:
                self.logger.error(f"设备检测失败: {e}，默认为CPU")
                return "cpu"
        return device

    @abstractmethod
    def predict(self, image: sitk.Image) -> Dict[str, np.ndarray]:
        """执行预测"""
        pass


class LungMaskModel(BaseModel):
    def __init__(self, model_name: str = "R231", device: str = "auto"):
        # 调试用的打印移到父类初始化之后
        super().__init__(model_name, device)
        print("LungMaskModel init called")  # 调试用
        print("BaseModel class:", BaseModel)
        print("BaseModel module:", BaseModel.__module__)
        print("LungMaskModel mro:", LungMaskModel.mro())
        self._load_model()

    def _load_model(self):
        """加载LungMask模型"""
        try:
            import lungmask
            from lungmask import LMInferer

            self.logger.info(f"加载LungMask模型: {self.model_name}")

            # 根据模型名称选择合适的推理器
            if self.model_name in ["LTRCLobes", "LTRCLobes_R231"]:
                if self.model_name == "LTRCLobes_R231":
                    self.inferer = LMInferer(modelname='LTRCLobes', fillmodel='R231')
                else:
                    self.inferer = LMInferer(modelname='LTRCLobes')
            else:
                self.inferer = LMInferer(modelname=self.model_name)

        except ImportError:
            raise ImportError("请安装lungmask: pip install lungmask")

    def predict(self, image: sitk.Image) -> Dict[str, np.ndarray]:
        """使用LungMask进行预测"""
        self.logger.info("执行LungMask预测...")

        # 执行分割
        segmentation_np = self.inferer.apply(image)

        # 根据模型类型解析结果
        results = {}

        if "Lobes" in self.model_name:
            # 肺叶分割
            results["left_upper_lobe"] = (segmentation_np == 1).astype(np.uint8)
            results["left_lower_lobe"] = (segmentation_np == 2).astype(np.uint8)
            results["right_upper_lobe"] = (segmentation_np == 3).astype(np.uint8)
            results["right_middle_lobe"] = (segmentation_np == 4).astype(np.uint8)
            results["right_lower_lobe"] = (segmentation_np == 5).astype(np.uint8)

            # 合并为整个肺部
            results["right_lung"] = ((segmentation_np == 3) |
                                     (segmentation_np == 4) |
                                     (segmentation_np == 5)).astype(np.uint8)
            results["left_lung"] = ((segmentation_np == 1) |
                                    (segmentation_np == 2)).astype(np.uint8)
        else:
            # 基础肺部分割
            results["right_lung"] = (segmentation_np == 1).astype(np.uint8)
            results["left_lung"] = (segmentation_np == 2).astype(np.uint8)

        return results


class TotalSegmentatorModel(BaseModel):
    """TotalSegmentator模型接口"""

    def __init__(self, model_name: str = "lung_basic", device: str = "auto"):
        super().__init__(model_name, device)
        self._check_installation()

    def _check_installation(self):
        """检查TotalSegmentator安装"""
        try:
            import totalsegmentator
            self.logger.info("TotalSegmentator已安装")
        except ImportError:
            raise ImportError("请安装TotalSegmentator: pip install TotalSegmentator")

    def predict(self, image: sitk.Image) -> Dict[str, np.ndarray]:
        """使用TotalSegmentator进行预测"""
        self.logger.info("执行TotalSegmentator预测...")

        import tempfile
        import os
        from totalsegmentator.python_api import totalsegmentator

        # 创建临时文件
        with tempfile.TemporaryDirectory() as temp_dir:
            input_path = os.path.join(temp_dir, "input.nii.gz")
            output_dir = os.path.join(temp_dir, "output")

            # 保存输入图像
            sitk.WriteImage(image, input_path)

            # 执行分割
            if "extended" in self.model_name:
                totalsegmentator(input_path, output_dir,
                                 task="total", fast=False)
            else:
                totalsegmentator(input_path, output_dir,
                                 task="lung_vessels", fast=False)

            # 读取结果
            results = self._parse_totalsegmentator_output(output_dir)

        return results

    def _parse_totalsegmentator_output(self, output_dir: str) -> Dict[str, np.ndarray]:
        """解析TotalSegmentator输出"""
        import os

        results = {}

        # 定义感兴趣的结构映射，动态匹配可能的结构
        structure_mapping = {
            "lung_upper_lobe_left": "left_upper_lobe",
            "lung_lower_lobe_left": "left_lower_lobe",
            "lung_upper_lobe_right": "right_upper_lobe",
            "lung_middle_lobe_right": "right_middle_lobe",
            "lung_lower_lobe_right": "right_lower_lobe",
            "trachea": "trachea",
            "lung_trachea_bronchia": "airways_and_bronchi",
            "lung_vessels": "lung_vessels"
        }

        for filename in os.listdir(output_dir):
            if filename.endswith(".nii.gz"):
                base_name = filename.replace(".nii.gz", "")
                for key, structure_name in structure_mapping.items():
                    if key in base_name:
                        filepath = os.path.join(output_dir, filename)
                        mask_image = sitk.ReadImage(filepath)
                        mask_array = sitk.GetArrayFromImage(mask_image)
                        results[structure_name] = mask_array.astype(np.uint8)
                        break

        # 合并肺叶为整个肺部
        if "left_upper_lobe" in results and "left_lower_lobe" in results:
            results["left_lung"] = (results["left_upper_lobe"] |
                                    results["left_lower_lobe"]).astype(np.uint8)

        if all(lobe in results for lobe in ["right_upper_lobe", "right_middle_lobe", "right_lower_lobe"]):
            results["right_lung"] = (results["right_upper_lobe"] |
                                     results["right_middle_lobe"] |
                                     results["right_lower_lobe"]).astype(np.uint8)

        return results
