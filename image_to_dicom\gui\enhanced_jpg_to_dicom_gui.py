#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版JPG转DICOM GUI工具
基于真实DICOM metadata最大化还原DICOM信息

作者: AI Assistant
日期: 2025-01-17
"""

import os
import sys
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QLineEdit, QPushButton,
                             QFileDialog, QTextEdit, QProgressBar, QGroupBox,
                             QGridLayout, QMessageBox, QListWidget, QSplitter,
                             QFrame, QCheckBox, QSpinBox, QComboBox, QTabWidget)
from PyQt5.QtCore import Qt, QThread, pyqtSignal
from PyQt5.QtGui import QFont
import datetime
from pathlib import Path

from enhanced_jpg_to_dicom import EnhancedJpgToDicom
from dicom_metadata_analyzer import DicomMetadataAnalyzer
from hu_value_mapper import MappingStrategy
import json


class EnhancedConversionWorker(QThread):
    """增强版转换工作线程"""
    progress_updated = pyqtSignal(int)
    log_updated = pyqtSignal(str)
    conversion_finished = pyqtSignal(bool, str)

    def __init__(self, input_files, output_dir, patient_name, patient_id,
                 study_description, series_number, template_path=None,
                 enable_hu_mapping=True, hu_mapping_strategy=MappingStrategy.TISSUE_BASED,
                 hu_mapping_params=None):
        super().__init__()
        self.input_files = input_files
        self.output_dir = output_dir
        self.patient_name = patient_name
        self.patient_id = patient_id
        self.study_description = study_description
        self.series_number = series_number
        self.template_path = template_path
        self.enable_hu_mapping = enable_hu_mapping
        self.hu_mapping_strategy = hu_mapping_strategy
        self.hu_mapping_params = hu_mapping_params or {}
        
    def run(self):
        """执行转换任务"""
        try:
            # 创建转换器
            converter = EnhancedJpgToDicom(
                self.template_path,
                self.enable_hu_mapping,
                self.hu_mapping_strategy
            )
            
            total_files = len(self.input_files)
            success_count = 0
            
            # 按文件名排序
            sorted_files = sorted(self.input_files, 
                                key=lambda x: converter._extract_number_from_filename(os.path.basename(x)))
            
            for i, input_file in enumerate(sorted_files):
                try:
                    # 生成输出文件名
                    filename = os.path.basename(input_file)
                    name_without_ext = os.path.splitext(filename)[0]
                    output_filename = f"{name_without_ext}.dcm"
                    output_filepath = os.path.join(self.output_dir, output_filename)
                    
                    self.log_updated.emit(f"正在转换: {filename}")
                    
                    # 计算切片位置
                    slice_position = float(i * 10.0)  # 10mm间距
                    
                    # 执行转换
                    success = converter.convert_jpg_to_dicom(
                        input_file, output_filepath,
                        self.patient_name, self.patient_id,
                        self.study_description, self.series_number,
                        i + 1, slice_position, self.hu_mapping_params
                    )
                    
                    if success:
                        success_count += 1
                        self.log_updated.emit(f"✓ 转换成功: {filename} -> {output_filename}")
                    else:
                        self.log_updated.emit(f"✗ 转换失败: {filename}")
                        
                except Exception as e:
                    self.log_updated.emit(f"✗ 转换失败: {filename} - {str(e)}")
                
                # 更新进度
                progress = int((i + 1) / total_files * 100)
                self.progress_updated.emit(progress)
            
            # 转换完成
            if success_count == total_files:
                self.conversion_finished.emit(True, f"所有 {total_files} 个文件转换成功！")
            else:
                self.conversion_finished.emit(False,
                    f"转换完成：成功 {success_count}/{total_files} 个文件")
                    
        except Exception as e:
            self.conversion_finished.emit(False, f"转换过程出错: {str(e)}")


class EnhancedJpgToDicomGUI(QMainWindow):
    """增强版JPG转DICOM GUI主窗口"""
    
    def __init__(self):
        super().__init__()
        self.input_files = []
        self.output_dir = ""
        self.template_path = None
        self.conversion_worker = None
        self.analyzer = DicomMetadataAnalyzer()
        self.init_ui()
        
    def init_ui(self):
        """初始化用户界面"""
        self.setWindowTitle("增强版JPG转DICOM工具 - 基于真实DICOM Metadata")
        self.setGeometry(100, 100, 1000, 800)
        
        # 创建中央窗口部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页
        tab_widget = QTabWidget()
        main_layout.addWidget(tab_widget)
        
        # 转换标签页
        conversion_tab = self.create_conversion_tab()
        tab_widget.addTab(conversion_tab, "JPG转DICOM")

        # HU值映射标签页
        hu_mapping_tab = self.create_hu_mapping_tab()
        tab_widget.addTab(hu_mapping_tab, "HU值映射")

        # 模板管理标签页
        template_tab = self.create_template_tab()
        tab_widget.addTab(template_tab, "模板管理")
        
        # 状态栏
        self.statusBar().showMessage("就绪 - 使用真实DICOM metadata模板")
        
    def create_conversion_tab(self):
        """创建转换标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        layout.addWidget(splitter)
        
        # 左侧面板 - 控制区域
        left_panel = self.create_control_panel()
        splitter.addWidget(left_panel)
        
        # 右侧面板 - 日志区域
        right_panel = self.create_log_panel()
        splitter.addWidget(right_panel)
        
        # 设置分割器比例
        splitter.setSizes([450, 550])
        
        return tab
        
    def create_template_tab(self):
        """创建模板管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        
        # 模板信息组
        template_group = QGroupBox("DICOM模板管理")
        template_layout = QVBoxLayout(template_group)
        
        # 当前模板显示
        current_layout = QHBoxLayout()
        current_layout.addWidget(QLabel("当前模板:"))
        self.current_template_label = QLabel("默认GE CT750模板")
        self.current_template_label.setStyleSheet("color: #2E8B57; font-weight: bold;")
        current_layout.addWidget(self.current_template_label)
        current_layout.addStretch()
        template_layout.addLayout(current_layout)
        
        # 模板操作按钮
        button_layout = QHBoxLayout()
        
        self.load_template_btn = QPushButton("加载模板文件")
        self.load_template_btn.clicked.connect(self.load_template)
        button_layout.addWidget(self.load_template_btn)
        
        self.create_template_btn = QPushButton("从DICOM目录创建模板")
        self.create_template_btn.clicked.connect(self.create_template_from_dicom)
        button_layout.addWidget(self.create_template_btn)
        
        self.reset_template_btn = QPushButton("重置为默认模板")
        self.reset_template_btn.clicked.connect(self.reset_template)
        button_layout.addWidget(self.reset_template_btn)
        
        template_layout.addLayout(button_layout)
        
        # 模板信息显示
        self.template_info_text = QTextEdit()
        self.template_info_text.setReadOnly(True)
        self.template_info_text.setMaximumHeight(400)
        template_layout.addWidget(QLabel("模板信息:"))
        template_layout.addWidget(self.template_info_text)
        
        layout.addWidget(template_group)
        layout.addStretch()
        
        # 初始化显示默认模板信息
        self.update_template_info()
        
        return tab

    def create_hu_mapping_tab(self):
        """创建HU值映射标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # HU值映射控制组
        hu_group = QGroupBox("HU值映射设置")
        hu_layout = QVBoxLayout(hu_group)

        # 启用HU值映射
        self.enable_hu_mapping_cb = QCheckBox("启用HU值映射")
        self.enable_hu_mapping_cb.setChecked(True)
        self.enable_hu_mapping_cb.stateChanged.connect(self.on_hu_mapping_toggled)
        hu_layout.addWidget(self.enable_hu_mapping_cb)

        # 映射策略选择
        strategy_layout = QHBoxLayout()
        strategy_layout.addWidget(QLabel("映射策略:"))
        self.mapping_strategy_combo = QComboBox()
        self.mapping_strategy_combo.addItems([
            "tissue - 基于组织类型映射",
            "linear - 线性映射",
            "windowing - 基于窗宽窗位映射",
            "lookup - 查找表映射"
        ])
        self.mapping_strategy_combo.currentTextChanged.connect(self.on_strategy_changed)
        strategy_layout.addWidget(self.mapping_strategy_combo)
        hu_layout.addLayout(strategy_layout)

        # 参数配置区域
        self.params_group = QGroupBox("映射参数")
        self.params_layout = QVBoxLayout(self.params_group)
        hu_layout.addWidget(self.params_group)

        # 初始化参数界面
        self.update_params_ui()

        layout.addWidget(hu_group)

        # HU值信息显示
        info_group = QGroupBox("HU值信息")
        info_layout = QVBoxLayout(info_group)

        info_text = QLabel(
            "HU值（Hounsfield Unit）是CT图像中表示组织密度的标准单位：\n"
            "• 空气: -1000 HU\n"
            "• 肺组织: -900 到 -500 HU\n"
            "• 脂肪: -120 到 -60 HU\n"
            "• 水: 0 HU\n"
            "• 软组织: 20 到 60 HU\n"
            "• 肌肉: 40 到 80 HU\n"
            "• 骨骼: 150 到 1000+ HU"
        )
        info_text.setStyleSheet("color: #666; font-size: 10px; margin: 10px;")
        info_layout.addWidget(info_text)

        layout.addWidget(info_group)
        layout.addStretch()

        return tab

    def create_control_panel(self):
        """创建控制面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)
        
        # 文件选择组
        file_group = QGroupBox("文件选择")
        file_layout = QVBoxLayout(file_group)
        
        # 输入文件选择
        input_layout = QHBoxLayout()
        input_layout.addWidget(QLabel("输入文件:"))
        self.select_files_btn = QPushButton("选择JPG文件")
        self.select_files_btn.clicked.connect(self.select_input_files)
        input_layout.addWidget(self.select_files_btn)
        file_layout.addLayout(input_layout)
        
        # 文件列表
        self.file_list = QListWidget()
        self.file_list.setMaximumHeight(120)
        file_layout.addWidget(QLabel("已选择的文件:"))
        file_layout.addWidget(self.file_list)
        
        # 输出目录选择
        output_layout = QHBoxLayout()
        output_layout.addWidget(QLabel("输出目录:"))
        self.output_dir_edit = QLineEdit()
        self.output_dir_edit.setPlaceholderText("选择输出目录...")
        output_layout.addWidget(self.output_dir_edit)
        self.select_output_btn = QPushButton("浏览")
        self.select_output_btn.clicked.connect(self.select_output_dir)
        output_layout.addWidget(self.select_output_btn)
        file_layout.addLayout(output_layout)
        
        layout.addWidget(file_group)
        
        # DICOM参数组
        param_group = QGroupBox("DICOM参数")
        param_layout = QGridLayout(param_group)

        param_layout.addWidget(QLabel("患者姓名:"), 0, 0)
        self.patient_name_edit = QLineEdit("DaiShan")  # 使用真实数据中的患者名
        param_layout.addWidget(self.patient_name_edit, 0, 1)

        param_layout.addWidget(QLabel("患者ID:"), 1, 0)
        self.patient_id_edit = QLineEdit("**********")  # 使用真实数据中的患者ID
        param_layout.addWidget(self.patient_id_edit, 1, 1)

        param_layout.addWidget(QLabel("检查描述:"), 2, 0)
        self.study_desc_edit = QLineEdit("Chest")
        param_layout.addWidget(self.study_desc_edit, 2, 1)

        param_layout.addWidget(QLabel("序列号:"), 3, 0)
        self.series_num_spin = QSpinBox()
        self.series_num_spin.setMinimum(1)
        self.series_num_spin.setMaximum(999)
        self.series_num_spin.setValue(1)
        param_layout.addWidget(self.series_num_spin, 3, 1)

        layout.addWidget(param_group)
        
        # 转换控制组
        control_group = QGroupBox("转换控制")
        control_layout = QVBoxLayout(control_group)
        
        # 转换按钮
        self.convert_btn = QPushButton("开始转换")
        self.convert_btn.clicked.connect(self.start_conversion)
        self.convert_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 10px; }")
        control_layout.addWidget(self.convert_btn)
        
        # 进度条
        self.progress_bar = QProgressBar()
        control_layout.addWidget(self.progress_bar)
        
        layout.addWidget(control_group)
        
        # 添加弹性空间
        layout.addStretch()
        
        return panel

    def create_log_panel(self):
        """创建日志面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)
        layout = QVBoxLayout(panel)

        layout.addWidget(QLabel("转换日志:"))

        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        self.log_text.setFont(QFont("Consolas", 9))
        layout.addWidget(self.log_text)

        # 清除日志按钮
        clear_btn = QPushButton("清除日志")
        clear_btn.clicked.connect(self.log_text.clear)
        layout.addWidget(clear_btn)

        return panel

    def select_input_files(self):
        """选择输入文件"""
        files, _ = QFileDialog.getOpenFileNames(
            self, "选择JPG文件", "",
            "图像文件 (*.jpg *.jpeg *.png *.bmp);;所有文件 (*.*)"
        )

        if files:
            self.input_files = files
            self.file_list.clear()
            for file in files:
                self.file_list.addItem(os.path.basename(file))
            self.log(f"已选择 {len(files)} 个文件")

    def select_output_dir(self):
        """选择输出目录"""
        dir_path = QFileDialog.getExistingDirectory(self, "选择输出目录")
        if dir_path:
            self.output_dir = dir_path
            self.output_dir_edit.setText(dir_path)
            self.log(f"输出目录: {dir_path}")

    def load_template(self):
        """加载模板文件"""
        template_file, _ = QFileDialog.getOpenFileName(
            self, "选择DICOM模板文件", "", "JSON文件 (*.json);;所有文件 (*.*)"
        )

        if template_file:
            self.template_path = template_file
            self.current_template_label.setText(f"自定义模板: {os.path.basename(template_file)}")
            self.update_template_info()
            self.log(f"已加载模板: {template_file}")

    def create_template_from_dicom(self):
        """从DICOM目录创建模板"""
        dicom_dir = QFileDialog.getExistingDirectory(self, "选择包含DICOM文件的目录")

        if dicom_dir:
            try:
                # 创建临时转换器来生成模板
                temp_converter = EnhancedJpgToDicom()
                if temp_converter.create_template_from_dicom_dir(dicom_dir):
                    # 保存模板
                    template_file = os.path.join(os.path.dirname(__file__), "custom_template.json")
                    temp_converter.analyzer.save_template(temp_converter.template, template_file)

                    # 使用新模板
                    self.template_path = template_file
                    self.current_template_label.setText("自定义模板: custom_template.json")
                    self.update_template_info()

                    QMessageBox.information(self, "成功", f"模板已从DICOM目录创建并保存到:\n{template_file}")
                    self.log(f"从DICOM目录创建模板: {dicom_dir}")
                else:
                    QMessageBox.warning(self, "错误", "无法从指定目录创建模板")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"创建模板时出错:\n{str(e)}")

    def reset_template(self):
        """重置为默认模板"""
        self.template_path = None
        self.current_template_label.setText("默认GE CT750模板")
        self.update_template_info()
        self.log("已重置为默认模板")

    def update_template_info(self):
        """更新模板信息显示"""
        try:
            if self.template_path:
                template = self.analyzer.load_template(self.template_path)
            else:
                template = self.analyzer.get_default_template()

            # 格式化显示模板信息
            info_text = "设备信息:\n"
            device_info = template.get('device_info', {})
            for key, value in device_info.items():
                info_text += f"  {key}: {value}\n"

            info_text += "\n扫描参数:\n"
            acquisition_params = template.get('acquisition_params', {})
            for key, value in acquisition_params.items():
                info_text += f"  {key}: {value}\n"

            info_text += "\n图像参数:\n"
            image_params = template.get('image_params', {})
            for key, value in image_params.items():
                info_text += f"  {key}: {value}\n"

            self.template_info_text.setPlainText(info_text)

        except Exception as e:
            self.template_info_text.setPlainText(f"无法加载模板信息: {str(e)}")

    def start_conversion(self):
        """开始转换"""
        # 验证输入
        if not self.input_files:
            QMessageBox.warning(self, "警告", "请先选择输入文件！")
            return

        if not self.output_dir:
            QMessageBox.warning(self, "警告", "请先选择输出目录！")
            return

        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)

        # 禁用转换按钮
        self.convert_btn.setEnabled(False)
        self.convert_btn.setText("转换中...")

        # 重置进度条
        self.progress_bar.setValue(0)

        # 获取HU值映射参数
        enable_hu, hu_strategy, hu_params = self.get_hu_mapping_params()

        # 启动转换线程
        self.conversion_worker = EnhancedConversionWorker(
            self.input_files,
            self.output_dir,
            self.patient_name_edit.text(),
            self.patient_id_edit.text(),
            self.study_desc_edit.text(),
            self.series_num_spin.value(),
            self.template_path,
            enable_hu,
            hu_strategy,
            hu_params
        )

        self.conversion_worker.progress_updated.connect(self.progress_bar.setValue)
        self.conversion_worker.log_updated.connect(self.log)
        self.conversion_worker.conversion_finished.connect(self.on_conversion_finished)

        self.conversion_worker.start()
        self.log("开始转换...")

    def on_conversion_finished(self, success, message):
        """转换完成处理"""
        self.convert_btn.setEnabled(True)
        self.convert_btn.setText("开始转换")

        if success:
            QMessageBox.information(self, "成功", message)
            self.log("转换完成！")
        else:
            QMessageBox.warning(self, "警告", message)
            self.log("转换完成，但有错误发生")

        self.statusBar().showMessage("转换完成")

    def log(self, message):
        """添加日志消息"""
        timestamp = datetime.datetime.now().strftime("%H:%M:%S")
        self.log_text.append(f"[{timestamp}] {message}")
        self.log_text.ensureCursorVisible()

    def on_hu_mapping_toggled(self, state):
        """HU值映射开关切换"""
        enabled = state == Qt.Checked
        self.mapping_strategy_combo.setEnabled(enabled)
        self.params_group.setEnabled(enabled)

    def on_strategy_changed(self, text):
        """映射策略改变"""
        self.update_params_ui()

    def update_params_ui(self):
        """更新参数配置界面"""
        # 清除现有控件
        for i in reversed(range(self.params_layout.count())):
            child = self.params_layout.itemAt(i).widget()
            if child:
                child.setParent(None)

        strategy = self.mapping_strategy_combo.currentText().split(' - ')[0]

        if strategy == "tissue":
            # 组织类型选择
            tissue_layout = QHBoxLayout()
            tissue_layout.addWidget(QLabel("组织类型:"))
            self.tissue_type_combo = QComboBox()
            self.tissue_type_combo.addItems(["chest - 胸部", "abdomen - 腹部"])
            tissue_layout.addWidget(self.tissue_type_combo)
            self.params_layout.addLayout(tissue_layout)

        elif strategy == "linear":
            # 线性映射参数
            grid = QGridLayout()

            grid.addWidget(QLabel("最小HU值:"), 0, 0)
            self.min_hu_spin = QSpinBox()
            self.min_hu_spin.setRange(-1024, 3000)
            self.min_hu_spin.setValue(-1000)
            grid.addWidget(self.min_hu_spin, 0, 1)

            grid.addWidget(QLabel("最大HU值:"), 1, 0)
            self.max_hu_spin = QSpinBox()
            self.max_hu_spin.setRange(-1024, 3000)
            self.max_hu_spin.setValue(1000)
            grid.addWidget(self.max_hu_spin, 1, 1)

            self.params_layout.addLayout(grid)

        elif strategy == "windowing":
            # 窗宽窗位参数
            grid = QGridLayout()

            grid.addWidget(QLabel("窗位 (Center):"), 0, 0)
            self.window_center_spin = QSpinBox()
            self.window_center_spin.setRange(-1000, 1000)
            self.window_center_spin.setValue(40)
            grid.addWidget(self.window_center_spin, 0, 1)

            grid.addWidget(QLabel("窗宽 (Width):"), 1, 0)
            self.window_width_spin = QSpinBox()
            self.window_width_spin.setRange(1, 4000)
            self.window_width_spin.setValue(350)
            grid.addWidget(self.window_width_spin, 1, 1)

            # 预设按钮
            preset_layout = QHBoxLayout()
            preset_layout.addWidget(QLabel("预设:"))

            lung_btn = QPushButton("肺窗")
            lung_btn.clicked.connect(lambda: self.apply_window_preset(-600, 1600))
            preset_layout.addWidget(lung_btn)

            mediastinum_btn = QPushButton("纵隔窗")
            mediastinum_btn.clicked.connect(lambda: self.apply_window_preset(40, 350))
            preset_layout.addWidget(mediastinum_btn)

            bone_btn = QPushButton("骨窗")
            bone_btn.clicked.connect(lambda: self.apply_window_preset(300, 1500))
            preset_layout.addWidget(bone_btn)

            self.params_layout.addLayout(grid)
            self.params_layout.addLayout(preset_layout)

    def apply_window_preset(self, center, width):
        """应用窗宽窗位预设"""
        if hasattr(self, 'window_center_spin'):
            self.window_center_spin.setValue(center)
        if hasattr(self, 'window_width_spin'):
            self.window_width_spin.setValue(width)

    def get_hu_mapping_params(self):
        """获取HU值映射参数"""
        if not hasattr(self, 'enable_hu_mapping_cb') or not self.enable_hu_mapping_cb.isChecked():
            return False, MappingStrategy.LINEAR, {}

        strategy_text = self.mapping_strategy_combo.currentText().split(' - ')[0]

        if strategy_text == "tissue":
            strategy = MappingStrategy.TISSUE_BASED
            tissue_type = self.tissue_type_combo.currentText().split(' - ')[0] if hasattr(self, 'tissue_type_combo') else 'chest'
            params = {'tissue_type': tissue_type}
        elif strategy_text == "linear":
            strategy = MappingStrategy.LINEAR
            min_hu = self.min_hu_spin.value() if hasattr(self, 'min_hu_spin') else -1000
            max_hu = self.max_hu_spin.value() if hasattr(self, 'max_hu_spin') else 1000
            params = {'min_hu': min_hu, 'max_hu': max_hu}
        elif strategy_text == "windowing":
            strategy = MappingStrategy.WINDOWING
            center = self.window_center_spin.value() if hasattr(self, 'window_center_spin') else 40
            width = self.window_width_spin.value() if hasattr(self, 'window_width_spin') else 350
            params = {'window_center': center, 'window_width': width}
        else:
            strategy = MappingStrategy.LOOKUP_TABLE
            params = {}

        return True, strategy, params


def main():
    app = QApplication(sys.argv)

    # 设置应用程序样式
    app.setStyle('Fusion')

    window = EnhancedJpgToDicomGUI()
    window.show()

    sys.exit(app.exec_())


if __name__ == "__main__":
    main()
