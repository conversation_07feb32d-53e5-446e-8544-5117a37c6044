import numpy as np
import logging
import subprocess
import tempfile
import os
from pathlib import Path
from typing import Dict, Optional

try:
    from totalsegmentator.python_api import totalsegmentator
    TOTALSEG_AVAILABLE = True
except ImportError:
    TOTALSEG_AVAILABLE = False
    logging.warning("TotalSegmentator未安装，请运行: pip install TotalSegmentator")

class TotalSegmentatorModel:
    """TotalSegmentator模型封装类"""
    
    def __init__(self):
        if not TOTALSEG_AVAILABLE:
            raise ImportError("TotalSegmentator未安装")
        
        self.logger = logging.getLogger(__name__)
        self.logger.info("初始化TotalSegmentator模型")
        self.task = "lung_basic"  # 默认任务类型
    
    def set_task(self, task: str):
        """
        设置分割任务类型
        
        Args:
            task: 任务类型，可选 'lung_basic', 'lung_vessels', 'total'
        """
        valid_tasks = ["lung_basic", "lung_vessels", "total"]
        if task not in valid_tasks:
            self.logger.warning(f"不支持的任务类型: {task}，将使用默认值 'lung_basic'")
            task = "lung_basic"
            
        self.task = task
        self.logger.info(f"设置TotalSegmentator任务类型为: {task}")
    
    def segment(self, image: np.ndarray, 
               enable_lobes: bool = True,
               enable_airways: bool = True, 
               enable_vessels: bool = False,
               fast_mode: bool = False) -> Dict[str, np.ndarray]:
        """
        使用TotalSegmentator进行分割
        
        Args:
            image: 输入CT图像
            enable_lobes: 是否分割肺叶
            enable_airways: 是否分割气道
            enable_vessels: 是否分割血管
            fast_mode: 是否使用快速模式
            
        Returns:
            分割结果字典
        """
        try:
            # 记录输入图像信息
            self.logger.info(f"TotalSegmentator输入图像: 形状={image.shape}, 类型={image.dtype}, 范围=[{np.min(image):.1f}, {np.max(image):.1f}]")
            self.logger.info(f"分割选项: 肺叶={enable_lobes}, 气道={enable_airways}, 血管={enable_vessels}, 快速模式={fast_mode}")
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # 保存输入图像
                input_path = os.path.join(temp_dir, "input.nii.gz")
                output_dir = os.path.join(temp_dir, "output")
                os.makedirs(output_dir, exist_ok=True)
                
                self._save_image_as_nifti(image, input_path)
                self.logger.info(f"已保存输入图像到临时文件: {input_path}")
                
                # 根据任务类型和参数选择合适的分割模式
                task = self.task
                
                # 如果启用血管分割且任务不是lung_vessels，则切换到lung_vessels任务
                if enable_vessels and task != "lung_vessels":
                    task = "lung_vessels"
                    self.logger.info("启用血管分割，切换到lung_vessels任务")
                
                # 根据任务类型映射到TotalSegmentator实际任务名称
                ts_task = self._map_task_to_totalsegmentator_task(task)
                
                # 执行分割
                self.logger.info(f"执行TotalSegmentator分割，任务类型: {ts_task}, 快速模式: {fast_mode}")
                totalsegmentator(input_path, output_dir, task=ts_task, fast=fast_mode)
                
                # 检查输出目录中的文件
                output_files = os.listdir(output_dir)
                self.logger.info(f"TotalSegmentator输出文件: {output_files}")
                
                # 加载分割结果
                results = self._load_segmentation_results(output_dir, 
                                                         enable_lobes,
                                                         enable_airways,
                                                         enable_vessels)
                
                # 记录结果信息
                self.logger.info(f"加载的分割结果: {list(results.keys())}")
                for name, mask in results.items():
                    non_zero = np.count_nonzero(mask)
                    percentage = (non_zero / mask.size) * 100 if mask.size > 0 else 0
                    self.logger.info(f"  - {name}: 形状={mask.shape}, 非零像素={non_zero} ({percentage:.2f}%)")
                
                return results
                
        except Exception as e:
            self.logger.error(f"TotalSegmentator分割失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            return {}
    
    def _save_image_as_nifti(self, image: np.ndarray, filepath: str):
        """将图像保存为NIFTI格式"""
        import SimpleITK as sitk
        
        sitk_image = sitk.GetImageFromArray(image)
        sitk.WriteImage(sitk_image, filepath)
    
    def _load_segmentation_results(self, output_dir: str,
                                 enable_lobes: bool,
                                 enable_airways: bool, 
                                 enable_vessels: bool) -> Dict[str, np.ndarray]:
        """加载分割结果"""
        import SimpleITK as sitk
        
        results = {}
        output_path = Path(output_dir)
        
        # 列出输出目录中的所有文件
        all_files = list(output_path.glob("*.nii.gz"))
        self.logger.info(f"输出目录中的文件数量: {len(all_files)}")
        if len(all_files) > 0:
            self.logger.info("输出目录中的文件:")
            for file in all_files:
                self.logger.info(f"  - {file.name}")
        else:
            self.logger.warning("输出目录中没有找到任何.nii.gz文件")
            return results
        
        # 定义可能的肺部结构文件名模式（支持不同任务类型）
        lung_file_patterns = [
            "lung.nii.gz",                   # 基本肺部
            "lung_left.nii.gz",              # 左肺
            "lung_right.nii.gz",             # 右肺
            "lungs.nii.gz",                  # 肺部（复数形式）
            "lung_whole.nii.gz",             # 整个肺
            "lung_both.nii.gz"               # 两侧肺
        ]
        
        # 定义感兴趣的结构
        structures = {}
        
        # 首先检查目录中是否有肺部相关文件
        for pattern in lung_file_patterns:
            for file in all_files:
                if file.name.lower() == pattern.lower():
                    structures["lung"] = file.name
                    break
            if "lung" in structures:
                break
        
        # 检查左右肺
        for file in all_files:
            if "lung_left" in file.name.lower() or "left_lung" in file.name.lower():
                structures["left_lung"] = file.name
            elif "lung_right" in file.name.lower() or "right_lung" in file.name.lower():
                structures["right_lung"] = file.name
        
        # 检查气管
        for file in all_files:
            if "trachea" in file.name.lower():
                structures["trachea"] = file.name
                break
        
        # 如果启用肺叶分割，查找肺叶相关文件
        if enable_lobes:
            lobe_patterns = {
                "left_upper_lobe": ["lung_upper_lobe_left", "upper_lobe_left", "left_upper_lobe"],
                "left_lower_lobe": ["lung_lower_lobe_left", "lower_lobe_left", "left_lower_lobe"],
                "right_upper_lobe": ["lung_upper_lobe_right", "upper_lobe_right", "right_upper_lobe"],
                "right_middle_lobe": ["lung_middle_lobe_right", "middle_lobe_right", "right_middle_lobe"],
                "right_lower_lobe": ["lung_lower_lobe_right", "lower_lobe_right", "right_lower_lobe"]
            }
            
            for lobe_name, patterns in lobe_patterns.items():
                for file in all_files:
                    for pattern in patterns:
                        if pattern in file.name.lower():
                            structures[lobe_name] = file.name
                            break
                    if lobe_name in structures:
                        break
        
        # 如果启用气道分割，查找气道相关文件
        if enable_airways:
            airway_patterns = ["lung_trachea_bronchia", "trachea_bronchia", "airways", "bronchi"]
            for file in all_files:
                for pattern in airway_patterns:
                    if pattern in file.name.lower():
                        structures["airways"] = file.name
                        break
                if "airways" in structures:
                    break
        
        # 如果启用血管分割，查找血管相关文件
        if enable_vessels:
            vessel_patterns = {
                "lung_vessels": ["lung_vessels", "vessels", "pulmonary_vessels"],
                "pulmonary_artery": ["pulmonary_artery", "pulmonary_arteries"]
            }
            
            for vessel_name, patterns in vessel_patterns.items():
                for file in all_files:
                    for pattern in patterns:
                        if pattern in file.name.lower():
                            structures[vessel_name] = file.name
                            break
                    if vessel_name in structures:
                        break
        
        self.logger.info(f"找到的结构: {structures}")
        
        # 加载每个结构
        for name, filename in structures.items():
            filepath = output_path / filename
            if filepath.exists():
                try:
                    self.logger.info(f"加载结构: {name} 从文件: {filename}")
                    sitk_image = sitk.ReadImage(str(filepath))
                    array = sitk.GetArrayFromImage(sitk_image)
                    
                    # 检查数组是否为空
                    non_zero = np.count_nonzero(array)
                    if non_zero == 0:
                        self.logger.warning(f"结构 {name} 的掩码为空（全零）")
                    else:
                        self.logger.info(f"结构 {name} 加载成功，非零像素: {non_zero}")
                    
                    # 转换为二值掩码
                    mask = (array > 0).astype(np.uint8)
                    results[name] = mask
                    
                except Exception as e:
                    self.logger.warning(f"无法加载 {name} 从 {filepath}: {e}")
            else:
                self.logger.warning(f"文件不存在: {filepath}")
        
        # 如果没有找到任何肺部结构，尝试查找其他可能的文件
        if "lung" not in results and "left_lung" not in results and "right_lung" not in results:
            self.logger.warning("未找到标准肺部结构，尝试查找其他可能的文件")
            for file in all_files:
                if "lung" in file.name.lower():
                    try:
                        name = file.stem.replace(".nii", "")  # 移除扩展名
                        self.logger.info(f"尝试加载额外的肺部结构: {name} 从文件: {file.name}")
                        sitk_image = sitk.ReadImage(str(file))
                        array = sitk.GetArrayFromImage(sitk_image)
                        mask = (array > 0).astype(np.uint8)
                        results[name] = mask
                        self.logger.info(f"额外结构 {name} 加载成功，非零像素: {np.count_nonzero(mask)}")
                    except Exception as e:
                        self.logger.warning(f"无法加载额外结构 {file.name}: {e}")
        
        # 如果找到了左右肺但没有合并的肺部掩码，创建一个
        if "left_lung" in results and "right_lung" in results and "lung" not in results:
            self.logger.info("创建合并的肺部掩码")
            results["lung"] = (results["left_lung"] | results["right_lung"]).astype(np.uint8)
            self.logger.info(f"合并肺部掩码创建成功，非零像素: {np.count_nonzero(results['lung'])}")
        
        # 如果找到了肺叶但没有左右肺，创建左右肺掩码
        if "left_upper_lobe" in results and "left_lower_lobe" in results and "left_lung" not in results:
            self.logger.info("从左侧肺叶创建左肺掩码")
            results["left_lung"] = (results["left_upper_lobe"] | results["left_lower_lobe"]).astype(np.uint8)
            self.logger.info(f"左肺掩码创建成功，非零像素: {np.count_nonzero(results['left_lung'])}")
            
        if all(lobe in results for lobe in ["right_upper_lobe", "right_middle_lobe", "right_lower_lobe"]) and "right_lung" not in results:
            self.logger.info("从右侧肺叶创建右肺掩码")
            results["right_lung"] = (results["right_upper_lobe"] | results["right_middle_lobe"] | results["right_lower_lobe"]).astype(np.uint8)
            self.logger.info(f"右肺掩码创建成功，非零像素: {np.count_nonzero(results['right_lung'])}")
            
        # 如果此时有了左右肺但仍然没有合并的肺部掩码，创建一个
        if "left_lung" in results and "right_lung" in results and "lung" not in results:
            self.logger.info("从左右肺创建合并的肺部掩码")
            results["lung"] = (results["left_lung"] | results["right_lung"]).astype(np.uint8)
            self.logger.info(f"合并肺部掩码创建成功，非零像素: {np.count_nonzero(results['lung'])}")
        
        return results
    
    def _map_task_to_totalsegmentator_task(self, task: str) -> str:
        """将我们的任务类型映射到TotalSegmentator实际任务名称"""
        task_mapping = {
            "lung_basic": "lung",
            "lung_vessels": "lung_vessels",
            "total": "total"
        }
        
        if task in task_mapping:
            return task_mapping[task]
        else:
            self.logger.warning(f"未知任务类型: {task}，使用默认值'lung'")
            return "lung"