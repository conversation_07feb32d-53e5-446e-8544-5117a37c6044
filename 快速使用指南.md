# 快速使用指南

## 🚀 快速开始

### 方法一：使用启动器（推荐）
双击运行 `启动器.py` 文件，在菜单中选择需要的功能。

### 方法二：直接运行功能脚本
双击以下任一脚本文件：

- **`run_lung_segmentation.py`** - 肺组织分割工具
- **`run_lung_tissue_analyzer.py`** - 肺组织分析工具  
- **`run_image_to_dicom.py`** - 图片转DICOM工具
- **`run_dicom_renumber.py`** - DICOM重新编号工具
- **`run_ct_region_cropper.py`** - CT区域裁剪工具

### 方法三：命令行运行
```bash
# 启动器
python 启动器.py

# 或直接运行具体功能
python run_lung_segmentation.py
python run_lung_tissue_analyzer.py
python run_image_to_dicom.py
python run_dicom_renumber.py
python run_ct_region_cropper.py
```

## 📁 项目结构

```
lung_ct_segmenter/
├── lung_segmentation/          # 🫁 肺组织分割模块
├── image_to_dicom/             # 🖼️ 图片转DICOM模块  
├── dicom_tools/                # 🔧 DICOM工具模块
├── ct_region_tools/            # ✂️ CT区域裁剪工具
├── shared_utils/               # 🛠️ 共享工具模块
├── run_*.py                    # 🚀 启动脚本
├── 启动器.py                   # 📋 功能选择菜单
└── requirements.txt            # 📦 依赖包列表
```

## 🔧 功能说明

### 1. 肺组织分割工具
- **功能**：从CT影像中自动分割肺部结构
- **支持模型**：Lungmask (R231, LTRCLobes), TotalSegmentator
- **输入**：NIFTI文件 (.nii, .nii.gz) 或 DICOM文件夹
- **输出**：分割掩码文件

### 2. 肺组织分析工具  
- **功能**：定量分析肺部组织，计算肺气肿等指标
- **输入**：原始CT影像 + 肺部掩码文件
- **输出**：分析报告、三维重建、Excel数据

### 3. 图片转DICOM工具
- **功能**：将JPG/PNG图片转换为DICOM格式
- **特色**：支持HU值映射、批量转换、元数据管理
- **输入**：JPG/PNG图片文件或文件夹
- **输出**：标准DICOM文件

### 4. DICOM重新编号工具
- **功能**：重新编号DICOM文件序列，修复排序问题
- **输入**：DICOM文件夹
- **输出**：重新编号的DICOM文件

### 5. CT区域裁剪工具
- **功能**：裁剪CT影像的指定区域，保持完整元数据
- **输入**：DICOM文件夹
- **输出**：裁剪后的DICOM文件

## ⚠️ 注意事项

1. **首次使用**：请确保已安装所有依赖包
   ```bash
   pip install -r requirements.txt
   ```

2. **AI模型下载**：首次使用肺组织分割功能时，会自动下载AI模型文件，请保持网络连接

3. **文件路径**：建议使用英文路径，避免中文字符可能导致的问题

4. **内存要求**：肺组织分割功能需要较大内存，建议8GB以上

## 🆘 常见问题

**Q: 程序启动失败怎么办？**
A: 检查是否已安装Python和所有依赖包，确保Python版本≥3.8

**Q: 肺组织分割速度很慢？**  
A: 可以在设置中勾选"强制使用CPU"选项，或关闭其他占用内存的程序

**Q: DICOM文件无法识别？**
A: 确保DICOM文件格式正确，可以使用DICOM调试工具检查文件

**Q: 需要技术支持？**
A: 请查看详细的使用说明文档或联系技术支持
