#!/usr/bin/env python3
"""
批量处理肺部CT分割示例
"""

import logging
import sys
from pathlib import Path
import argparse

# 添加项目路径
sys.path.append(str(Path(__file__).parent.parent))

from lung_ct_segmenter.core.segmenter import LungCTSegmenter

def main():
    parser = argparse.ArgumentParser(description='批量处理肺部CT分割')
    parser.add_argument('--input_dir', required=True, help='输入目录路径')
    parser.add_argument('--output_dir', required=True, help='输出目录路径')
    parser.add_argument('--model_type', default='lungmask', 
                       choices=['lungmask', 'totalsegmentator'],
                       help='AI模型类型')
    parser.add_argument('--model_variant', default='R231',
                       choices=['R231', 'LTRCLobes', 'R231CovidWeb'],
                       help='Lungmask模型变体')
    parser.add_argument('--enable_lobes', action='store_true',
                       help='启用肺叶分割（TotalSegmentator）')
    parser.add_argument('--enable_airways', action='store_true',
                       help='启用气道分割（TotalSegmentator）')
    parser.add_argument('--enable_vessels', action='store_true',
                       help='启用血管分割（TotalSegmentator）')
    parser.add_argument('--gaussian_sigma', type=float, default=2.0,
                       help='高斯平滑的sigma值')
    parser.add_argument('--morphological_radius', type=int, default=1,
                       help='形态学操作的半径')
    parser.add_argument('--fail_on_error', action='store_true',
                       help='如果处理出错则停止并抛出异常')
    parser.add_argument('--log_level', default='INFO',
                       choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       help='设置日志级别')
    
    args = parser.parse_args()
    
    # 配置日志
    log_level = getattr(logging, args.log_level.upper(), logging.INFO)
    logging.basicConfig(level=log_level,
                       format='%(asctime)s - %(levelname)s - %(message)s')
    
    try:
        # 创建分割器
        segmenter = LungCTSegmenter(model_type=args.model_type,
                                   model_variant=args.model_variant,
                                   gaussian_sigma=args.gaussian_sigma,
                                   morphological_radius=args.morphological_radius)
        
        # 配置TotalSegmentator参数
        if args.model_type == 'totalsegmentator':
            segmenter.enable_lobes = args.enable_lobes
            segmenter.enable_airways = args.enable_airways
            segmenter.enable_vessels = args.enable_vessels
        
        # 执行批量处理
        print(f"开始批量处理: {args.input_dir}")
        processed_files = segmenter.batch_process(args.input_dir,
                                                args.output_dir,
                                                file_pattern="*.nii.gz",
                                                fail_on_error=args.fail_on_error)
        
        print(f"批量处理完成！处理的文件数: {len(processed_files)}")
        print("处理的文件列表:")
        for file in processed_files:
            print(f" - {file}")
        
    except Exception as e:
        print(f"批量处理过程中出现错误: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())