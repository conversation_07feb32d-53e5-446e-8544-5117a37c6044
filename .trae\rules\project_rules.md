这是一个包含了肺叶分割和肺内不同组织体积占比统计分析的项目
# 核心科学计算
numpy==1.26.4
scipy==1.15.3
pandas==2.0.0
scikit-learn==1.2.2
matplotlib==3.10.3
seaborn==0.13.2

# 图像处理
opencv-python==********
scikit-image==0.22.0
pillow==11.1.0
imageio==2.37.0
nibabel==5.0.0
simpleitk==2.2.1
tifffile==2025.5.10

# DICOM/NIfTI
pydicom==3.0.1
dicom2nifti==2.6.1
python-gdcm==3.0.25

# 医学图像分割相关
lungmask==0.2.20
totalsegmentator==2.8.0
nnunetv2==2.6.0
dynamic-network-architectures==0.3.1
connected-components-3d==3.23.0
fastremap==1.16.1
fill-voids==2.0.8
batchgenerators==0.25.1
batchgeneratorsv2==0.2.3

# 深度学习相关
torch==2.5.1
torchvision==0.20.1
torchaudio==2.5.1
einops==0.8.1
fft-conv-pytorch==1.2.0
yacs==0.1.8

# 系统与辅助库
tqdm==4.66.1
joblib==1.5.1
pyyaml==6.0.2
fsspec==2025.5.1
packaging==25.0
future==1.0.0
requests==2.32.3