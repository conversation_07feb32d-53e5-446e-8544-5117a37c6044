import numpy as np
import logging
from typing import Optional
import SimpleIT<PERSON> as sitk

try:
    from packaging.version import parse as parse_version
    PACKAGING_AVAILABLE = True
except ImportError:
    PACKAGING_AVAILABLE = False
    logging.warning("packaging library not found. Version comparison might be less robust. Consider 'pip install packaging'.")

try:
    import lungmask
    from lungmask import LMInferer
    LUNGMASK_AVAILABLE = True
except ImportError:
    LUNGMASK_AVAILABLE = False
    logging.warning("Lungmask未安装，请运行: pip install lungmask")

class LungmaskModel:
    """Lungmask模型封装类"""
    
    def __init__(self, model_variant: str = "R231"):
        """
        初始化Lungmask模型
        
        Args:
            model_variant: 模型变体 ('R231', 'LTRCLobes', 'R231CovidWeb')
        """
        if not LUNGMASK_AVAILABLE:
            raise ImportError("Lungmask未安装")
        
        self.model_variant = model_variant
        self.model = None
        self.inferer = None
        self.logger = logging.getLogger(__name__)
        
        self._load_model()
    
    def _load_model(self):
        """加载模型"""
        try:
            # 检查lungmask版本
            import pkg_resources
            current_version_str = pkg_resources.require("lungmask")[0].version
            
            # 根据版本选择API
            use_new_api = False
            if PACKAGING_AVAILABLE:
                if parse_version(current_version_str) > parse_version("0.2.13"):
                    use_new_api = True
            else:  # 回退到原有比较逻辑
                if self._compare_versions(current_version_str, "0.2.13"):
                    use_new_api = True
            
            if use_new_api:
                # 新版本API
                if self.model_variant == "LTRCLobes":
                    self.inferer = LMInferer(modelname='LTRCLobes')
                elif self.model_variant == "R231CovidWeb":
                    self.inferer = LMInferer(modelname='R231CovidWeb')
                else:  # R231
                    self.inferer = LMInferer()
            else:
                # 旧版本API
                from lungmask import mask
                self.model = mask.get_model('unet', self.model_variant)
            
            self.logger.info(f"成功加载Lungmask模型: {self.model_variant}")
            
        except Exception as e:
            self.logger.error(f"加载Lungmask模型失败: {e}")
            raise
    
    def segment(self, image: np.ndarray) -> np.ndarray:
        """
        执行肺部分割
        
        Args:
            image: 输入CT图像
            
        Returns:
            分割结果数组
        """
        try:
            # 添加调试信息
            self.logger.info(f"Lungmask输入图像: 形状={image.shape}, 类型={image.dtype}, 范围=[{image.min():.4f}, {image.max():.4f}]")
            
            # 确保图像是浮点型，lungmask可能需要这种格式
            if image.dtype != np.float32:
                image = image.astype(np.float32)
            
            # 检查图像是否已归一化到[0,1]
            if image.max() <= 1.0 and image.min() >= 0.0:
                # 如果已归一化到[0,1]，转换回HU值范围
                image = image * 1400 - 1000  # 转换到[-1000, 400]范围
                self.logger.info("检测到归一化图像，已转换回HU值范围")
            
            # 创建SimpleITK图像对象
            sitk_image = sitk.GetImageFromArray(image)
            
            if self.inferer is not None:
                # 新版本API
                self.logger.info("使用新版本Lungmask API进行分割")
                segmentation = self.inferer.apply(sitk_image)
            else:
                # 旧版本API
                self.logger.info("使用旧版本Lungmask API进行分割")
                from lungmask import mask
                segmentation = mask.apply(sitk_image, self.model)
            
            # 检查分割结果
            unique_values = np.unique(segmentation)
            self.logger.info(f"分割结果包含的标签: {unique_values}, 形状={segmentation.shape}")
            
            return segmentation
            
        except Exception as e:
            self.logger.error(f"Lungmask分割失败: {e}")
            import traceback
            self.logger.error(traceback.format_exc())
            raise
    
    def _compare_versions(self, current: str, target: str) -> bool:
        """比较版本号 (仅在 packaging 不可用时作为回退)"""
        if PACKAGING_AVAILABLE:
            self.logger.warning("_compare_versions called directly while packaging is available. This should not happen.")
            return parse_version(current) > parse_version(target)
        
        current_parts = list(map(int, current.split('.')))
        target_parts = list(map(int, target.split('.')))
        
        for i in range(max(len(current_parts), len(target_parts))):
            c = current_parts[i] if i < len(current_parts) else 0
            t = target_parts[i] if i < len(target_parts) else 0
            
            if c > t:
                return True
            elif c < t:
                return False
        
        return False