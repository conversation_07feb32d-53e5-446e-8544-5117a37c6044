#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版JPG转DICOM工具
基于真实DICOM metadata最大化还原DICOM信息

作者: AI Assistant
日期: 2025-01-17
"""

import os
import sys
import pydicom
from PIL import Image
import datetime
import numpy as np
import json
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from dicom_tools.debug.dicom_metadata_analyzer import DicomMetadataAnalyzer
from .hu_value_mapper import HUValueMapper, MappingStrategy
import shutil


class EnhancedJpgToDicom:
    """增强版JPG转DICOM转换器"""
    
    def __init__(self, template_path: Optional[str] = None,
                 enable_hu_mapping: bool = True,
                 hu_mapping_strategy: MappingStrategy = MappingStrategy.TISSUE_BASED):
        self.analyzer = DicomMetadataAnalyzer()
        self.hu_mapper = HUValueMapper() if enable_hu_mapping else None
        self.hu_mapping_strategy = hu_mapping_strategy
        self.enable_hu_mapping = enable_hu_mapping

        # 加载metadata模板
        if template_path and os.path.exists(template_path):
            self.template = self.analyzer.load_template(template_path)
        else:
            self.template = self.analyzer.get_default_template()

        # 生成唯一的Study和Series UID
        self._generate_base_uids()
    
    def _generate_base_uids(self):
        """生成基础UID"""
        timestamp = datetime.datetime.now()
        timestamp_str = timestamp.strftime("%Y%m%d%H%M%S")
        microseconds = timestamp.microsecond
        
        # 使用标准的UID前缀
        base_uid = "1.2.826.0.1.3680043.8.498"
        self.study_uid = f"{base_uid}.{timestamp_str}"
        self.series_uid_base = f"{base_uid}.{timestamp_str}"
        self.timestamp_str = timestamp_str
        self.base_timestamp = timestamp
    
    def create_template_from_dicom_dir(self, dicom_dir: str) -> bool:
        """从DICOM目录创建新的模板"""
        try:
            self.template = self.analyzer.create_template_from_directory(dicom_dir)
            return True
        except Exception as e:
            print(f"创建模板失败: {e}")
            return False
    
    def convert_jpg_to_dicom(self, jpg_path: str, output_path: str,
                           patient_name: str = "Anonymous",
                           patient_id: str = None,
                           study_description: str = "Chest",
                           series_number: int = 1,
                           instance_number: int = 1,
                           slice_position: float = 0.0,
                           hu_mapping_params: Optional[Dict[str, Any]] = None) -> bool:
        """
        将JPG转换为DICOM，使用真实metadata模板
        
        Args:
            jpg_path: JPG文件路径
            output_path: 输出DICOM文件路径
            patient_name: 患者姓名
            patient_id: 患者ID
            study_description: 检查描述
            series_number: 序列号
            instance_number: 实例号
            slice_position: 切片位置
        """
        try:
            # 读取JPG图像
            img = Image.open(jpg_path)
            
            # 转换为灰度图像
            if img.mode != 'L':
                img = img.convert("L")
            
            # 获取图像数据
            pixel_array = np.array(img, dtype=np.uint8)

            # 应用HU值映射
            if self.enable_hu_mapping and self.hu_mapper:
                hu_values, mapping_info = self._apply_hu_mapping(pixel_array, hu_mapping_params)
            else:
                hu_values = pixel_array.astype(np.int16)
                mapping_info = {'strategy': 'none', 'rescale_intercept': 0, 'rescale_slope': 1}

            # 创建DICOM数据集
            ds = self._create_dicom_dataset(
                hu_values, output_path, patient_name, patient_id,
                study_description, series_number, instance_number, slice_position, mapping_info
            )
            
            # 保存DICOM文件
            ds.save_as(output_path, write_like_original=False)
            
            return True
            
        except Exception as e:
            print(f"转换失败 {jpg_path}: {e}")
            return False
    
    def _create_dicom_dataset(self, pixel_array: np.ndarray, output_path: str,
                            patient_name: str, patient_id: str, study_description: str,
                            series_number: int, instance_number: int,
                            slice_position: float, mapping_info: Dict[str, Any]) -> pydicom.Dataset:
        """创建DICOM数据集"""
        
        # 生成唯一的UID
        series_uid = f"{self.series_uid_base}.{series_number}"
        sop_instance_uid = f"{series_uid}.{instance_number}.{self.base_timestamp.microsecond}"
        
        # 创建文件元信息
        file_meta = pydicom.dataset.FileMetaDataset()
        file_meta.FileMetaInformationGroupLength = 188
        file_meta.FileMetaInformationVersion = b'\x00\x01'
        file_meta.MediaStorageSOPClassUID = '1.2.840.10008.*******.1.2'  # CT Image Storage
        file_meta.MediaStorageSOPInstanceUID = sop_instance_uid
        file_meta.ImplementationClassUID = '1.2.826.0.1.3680043.8.498.1'
        file_meta.ImplementationVersionName = 'ENHANCED_JPG2DICOM_v1.0'
        file_meta.TransferSyntaxUID = '1.2.840.10008.1.2'  # Implicit VR Little Endian
        
        # 创建DICOM数据集
        ds = pydicom.dataset.FileDataset(output_path, {}, file_meta=file_meta, preamble=b"\0" * 128)
        
        # 设置基本信息
        self._set_patient_info(ds, patient_name, patient_id)
        self._set_study_info(ds, study_description)
        self._set_series_info(ds, series_number)
        self._set_instance_info(ds, instance_number, sop_instance_uid)
        self._set_device_info(ds)
        self._set_acquisition_params(ds)
        self._set_image_params(ds, pixel_array, mapping_info)
        self._set_geometry_info(ds, slice_position)
        
        # 设置像素数据
        ds.PixelData = pixel_array.tobytes()
        
        # 设置传输语法
        ds.is_little_endian = True
        ds.is_implicit_VR = True
        
        return ds
    
    def _set_patient_info(self, ds: pydicom.Dataset, patient_name: str, patient_id: str):
        """设置患者信息"""
        ds.PatientName = patient_name[:64] if patient_name else "Anonymous"
        ds.PatientID = patient_id if patient_id else f"PID_{self.timestamp_str}"
        ds.PatientBirthDate = "19430301"  # 使用模板中的出生日期
        ds.PatientSex = "M"  # 使用模板中的性别
        ds.PatientAge = "079Y"  # 使用模板中的年龄
    
    def _set_study_info(self, ds: pydicom.Dataset, study_description: str):
        """设置检查信息"""
        ds.StudyInstanceUID = self.study_uid
        ds.StudyDescription = study_description
        ds.StudyID = f"STU_{self.timestamp_str}"
        ds.AccessionNumber = f"CT{self.base_timestamp.strftime('%y%m%d')}0072"  # 模仿真实格式
        
        # 设置日期时间
        ds.StudyDate = self.base_timestamp.strftime('%Y%m%d')
        ds.StudyTime = self.base_timestamp.strftime('%H%M%S')
        ds.InstanceCreationDate = self.base_timestamp.strftime('%Y%m%d')
        ds.InstanceCreationTime = self.base_timestamp.strftime('%H%M%S')
        ds.ContentDate = self.base_timestamp.strftime('%Y%m%d')
        ds.ContentTime = self.base_timestamp.strftime('%H%M%S')
        ds.AcquisitionDate = self.base_timestamp.strftime('%Y%m%d')
        ds.AcquisitionTime = self.base_timestamp.strftime('%H%M%S.%f')[:-3]
    
    def _set_series_info(self, ds: pydicom.Dataset, series_number: int):
        """设置序列信息"""
        series_uid = f"{self.series_uid_base}.{series_number}"
        ds.SeriesInstanceUID = series_uid
        ds.SeriesNumber = series_number
        ds.SeriesDescription = self.template.get('series_info', {}).get('SeriesDescription', '10MM std')
        ds.SeriesDate = self.base_timestamp.strftime('%Y%m%d')
        ds.SeriesTime = self.base_timestamp.strftime('%H%M%S')
    
    def _set_instance_info(self, ds: pydicom.Dataset, instance_number: int, sop_instance_uid: str):
        """设置实例信息"""
        ds.SOPClassUID = '1.2.840.10008.*******.1.2'  # CT Image Storage
        ds.SOPInstanceUID = sop_instance_uid
        ds.InstanceNumber = instance_number
        
        # 设置图像类型
        image_type = self.template.get('image_params', {}).get('ImageType', ['ORIGINAL', 'PRIMARY', 'AXIAL'])
        ds.ImageType = image_type
    
    def _set_device_info(self, ds: pydicom.Dataset):
        """设置设备信息"""
        device_info = self.template.get('device_info', {})
        
        ds.Manufacturer = device_info.get('Manufacturer', 'GE MEDICAL SYSTEMS')
        ds.ManufacturerModelName = device_info.get('ManufacturerModelName', 'Discovery CT750 HD')
        ds.StationName = device_info.get('StationName', 'ct99')
        ds.SoftwareVersions = device_info.get('SoftwareVersions', 'gmp_hde.74')
        ds.DeviceSerialNumber = device_info.get('DeviceSerialNumber', '*')
        ds.InstitutionName = device_info.get('InstitutionName', 'HRB MU NO.4 HOSPITAL')
    
    def _set_acquisition_params(self, ds: pydicom.Dataset):
        """设置采集参数"""
        params = self.template.get('acquisition_params', {})
        
        ds.Modality = params.get('Modality', 'CT')
        ds.KVP = params.get('KVP', '120')
        ds.SliceThickness = float(params.get('SliceThickness', '5.0'))
        ds.SpacingBetweenSlices = float(params.get('SpacingBetweenSlices', '10.0'))
        ds.DataCollectionDiameter = float(params.get('DataCollectionDiameter', '500.0'))
        ds.ReconstructionDiameter = float(params.get('ReconstructionDiameter', '450.0'))
        ds.DistanceSourceToDetector = float(params.get('DistanceSourceToDetector', '946.746'))
        ds.DistanceSourceToPatient = float(params.get('DistanceSourceToPatient', '538.520'))
        ds.GantryDetectorTilt = float(params.get('GantryDetectorTilt', '0.0'))
        ds.TableHeight = float(params.get('TableHeight', '120.5'))
        ds.RotationDirection = params.get('RotationDirection', 'CW')
        ds.ExposureTime = int(params.get('ExposureTime', '600'))
        ds.XRayTubeCurrent = int(params.get('XRayTubeCurrent', '150'))
        ds.Exposure = int(params.get('Exposure', '11'))
        ds.FilterType = params.get('FilterType', 'BODY FILTER')
        ds.GeneratorPower = int(params.get('GeneratorPower', '18000'))
        ds.FocalSpots = float(params.get('FocalSpots', '0.7'))
        ds.ConvolutionKernel = params.get('ConvolutionKernel', 'STANDARD')
        ds.PatientPosition = params.get('PatientPosition', 'HFS')
        ds.ScanOptions = params.get('ScanOptions', 'HELICAL MODE')
        ds.ProtocolName = params.get('ProtocolName', '5.1 Routine Chest ASIR30')

    def _set_image_params(self, ds: pydicom.Dataset, pixel_array: np.ndarray, mapping_info: Dict[str, Any]):
        """设置图像参数"""
        image_params = self.template.get('image_params', {})

        # 基本图像信息
        ds.Rows, ds.Columns = pixel_array.shape
        ds.SamplesPerPixel = 1
        ds.PhotometricInterpretation = image_params.get('PhotometricInterpretation', 'MONOCHROME2')

        # 根据是否启用HU映射设置位深度
        if self.enable_hu_mapping and mapping_info.get('strategy') != 'none':
            # HU值需要16位有符号整数
            ds.BitsAllocated = 16
            ds.BitsStored = 16
            ds.HighBit = 15
            ds.PixelRepresentation = 1  # 有符号整数
        else:
            # 普通图像使用8位无符号整数
            ds.BitsAllocated = 8
            ds.BitsStored = 8
            ds.HighBit = 7
            ds.PixelRepresentation = 0  # 无符号整数

        # 像素间距
        pixel_spacing = image_params.get('PixelSpacing', [0.878906, 0.878906])
        ds.PixelSpacing = pixel_spacing

        # 窗宽窗位
        ds.WindowCenter = image_params.get('WindowCenter', 40)
        ds.WindowWidth = image_params.get('WindowWidth', 350)

        # 重建参数 - 根据HU映射设置
        if self.enable_hu_mapping and mapping_info.get('strategy') != 'none':
            # 使用HU映射的参数
            ds.RescaleIntercept = mapping_info.get('rescale_intercept', -1024)
            ds.RescaleSlope = mapping_info.get('rescale_slope', 1)
            ds.RescaleType = 'HU'  # 设置为HU值

            # 设置合适的窗宽窗位
            if mapping_info.get('strategy') == 'windowing':
                ds.WindowCenter = mapping_info.get('window_center', 40)
                ds.WindowWidth = mapping_info.get('window_width', 350)
            else:
                # 使用默认的纵隔窗设置
                ds.WindowCenter = 40
                ds.WindowWidth = 350
        else:
            # 普通图像的线性映射
            ds.RescaleIntercept = 0
            ds.RescaleSlope = 1
            # 不设置RescaleType

        # 像素填充值 - 对于8位无符号图像，使用0作为填充值
        # 不设置PixelPaddingValue，因为JPG图像通常不需要填充值

    def _set_geometry_info(self, ds: pydicom.Dataset, slice_position: float):
        """设置几何信息"""
        geometry = self.template.get('geometry_template', {})

        # 图像方向
        orientation = geometry.get('ImageOrientationPatient', [1.0, 0.0, 0.0, 0.0, 1.0, 0.0])
        ds.ImageOrientationPatient = orientation

        # 图像位置 - 调整Z坐标
        base_position = geometry.get('ImagePositionPatient', [-225.0, -225.0, 0.0])
        position = base_position.copy()
        position[2] = slice_position  # 设置Z坐标
        ds.ImagePositionPatient = position

        # 切片位置
        ds.SliceLocation = slice_position

        # 位置参考指示器
        if 'PositionReferenceIndicator' in geometry:
            ds.PositionReferenceIndicator = geometry['PositionReferenceIndicator']

    def convert_directory(self, jpg_dir: str, output_dir: str,
                         patient_name: str = "Anonymous",
                         patient_id: str = None,
                         study_description: str = "Chest",
                         series_number: int = 1) -> List[str]:
        """
        批量转换目录中的JPG文件

        Returns:
            成功转换的文件列表
        """
        jpg_files = []
        jpg_dir_path = Path(jpg_dir)

        # 查找JPG文件
        for ext in ['*.jpg', '*.jpeg', '*.png']:
            jpg_files.extend(jpg_dir_path.glob(ext))

        if not jpg_files:
            print(f"在目录 {jpg_dir} 中未找到图像文件")
            return []

        # 按文件名排序
        jpg_files.sort(key=lambda x: self._extract_number_from_filename(x.name))

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        success_files = []

        for i, jpg_file in enumerate(jpg_files):
            try:
                # 生成输出文件名
                output_name = f"{jpg_file.stem}.dcm"
                output_path = os.path.join(output_dir, output_name)

                # 计算切片位置
                slice_position = float(i * 10.0)  # 使用10mm间距

                # 转换文件
                if self.convert_jpg_to_dicom(
                    str(jpg_file), output_path, patient_name, patient_id,
                    study_description, series_number, i + 1, slice_position
                ):
                    success_files.append(output_path)
                    print(f"✓ 转换成功: {jpg_file.name} -> {output_name}")
                else:
                    print(f"✗ 转换失败: {jpg_file.name}")

            except Exception as e:
                print(f"✗ 转换失败 {jpg_file.name}: {e}")

        print(f"\n转换完成: {len(success_files)}/{len(jpg_files)} 个文件")

        # 应用坐标系统修复
        if success_files:
            print("正在应用坐标系统修复...")
            self.fix_dicom_geometry(success_files)

        return success_files

    def _extract_number_from_filename(self, filename: str) -> int:
        """从文件名中提取数字用于排序"""
        import re
        name_without_ext = os.path.splitext(filename)[0]

        if name_without_ext.isdigit():
            return int(name_without_ext)
        else:
            numbers = re.findall(r'\d+', name_without_ext)
            return int(numbers[0]) if numbers else 0

    def _apply_hu_mapping(self, pixel_array: np.ndarray,
                         hu_mapping_params: Optional[Dict[str, Any]] = None) -> Tuple[np.ndarray, Dict[str, Any]]:
        """应用HU值映射"""
        if not self.hu_mapper:
            return pixel_array.astype(np.int16), {'strategy': 'none'}

        # 设置默认参数
        if hu_mapping_params is None:
            hu_mapping_params = {}

        # 根据策略应用映射
        if self.hu_mapping_strategy == MappingStrategy.TISSUE_BASED:
            tissue_type = hu_mapping_params.get('tissue_type', 'chest')
            hu_values, mapping_info = self.hu_mapper.map_to_hu(
                pixel_array, MappingStrategy.TISSUE_BASED, tissue_type=tissue_type
            )
        elif self.hu_mapping_strategy == MappingStrategy.LINEAR:
            min_hu = hu_mapping_params.get('min_hu', -1000)
            max_hu = hu_mapping_params.get('max_hu', 1000)
            hu_values, mapping_info = self.hu_mapper.map_to_hu(
                pixel_array, MappingStrategy.LINEAR, min_hu=min_hu, max_hu=max_hu
            )
        elif self.hu_mapping_strategy == MappingStrategy.WINDOWING:
            window_center = hu_mapping_params.get('window_center', 40)
            window_width = hu_mapping_params.get('window_width', 350)
            hu_values, mapping_info = self.hu_mapper.map_to_hu(
                pixel_array, MappingStrategy.WINDOWING,
                window_center=window_center, window_width=window_width
            )
        elif self.hu_mapping_strategy == MappingStrategy.LOOKUP_TABLE:
            lookup_table = hu_mapping_params.get('lookup_table', None)
            hu_values, mapping_info = self.hu_mapper.map_to_hu(
                pixel_array, MappingStrategy.LOOKUP_TABLE, lookup_table=lookup_table
            )
        else:
            # 默认使用组织映射
            hu_values, mapping_info = self.hu_mapper.map_to_hu(
                pixel_array, MappingStrategy.TISSUE_BASED, tissue_type='chest'
            )

        return hu_values, mapping_info

    def set_hu_mapping_strategy(self, strategy: MappingStrategy):
        """设置HU值映射策略"""
        self.hu_mapping_strategy = strategy

    def enable_hu_mapping_feature(self, enable: bool = True):
        """启用或禁用HU值映射功能"""
        self.enable_hu_mapping = enable
        if enable and not self.hu_mapper:
            self.hu_mapper = HUValueMapper()

    def get_available_mapping_strategies(self) -> List[str]:
        """获取可用的映射策略"""
        return [strategy.value for strategy in MappingStrategy]

    def get_hu_mapping_info(self) -> Dict[str, Any]:
        """获取HU值映射信息"""
        return {
            'enabled': self.enable_hu_mapping,
            'strategy': self.hu_mapping_strategy.value if self.hu_mapping_strategy else None,
            'available_strategies': self.get_available_mapping_strategies()
        }

    def fix_dicom_geometry(self, dicom_files: List[str]):
        """修复DICOM文件的几何信息"""
        try:
            print("正在修复DICOM几何信息...")

            # 按文件名排序
            dicom_files.sort(key=lambda x: os.path.basename(x))

            for i, file_path in enumerate(dicom_files):
                try:
                    # 读取DICOM文件
                    ds = pydicom.dcmread(file_path)

                    # 修复几何信息
                    modified = False

                    # 1. Image Position Patient (图像位置)
                    if not hasattr(ds, 'ImagePositionPatient') or ds.ImagePositionPatient is None or len(ds.ImagePositionPatient) < 3:
                        ds.ImagePositionPatient = [-225.0, -225.0, float(i * 10.0)]
                        modified = True
                    else:
                        # 只更新Z坐标，保持X,Y不变
                        ds.ImagePositionPatient[2] = float(i * 10.0)
                        modified = True

                    # 2. Image Orientation Patient (图像方向)
                    if not hasattr(ds, 'ImageOrientationPatient') or ds.ImageOrientationPatient is None or len(ds.ImageOrientationPatient) < 6:
                        ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]
                        modified = True

                    # 3. Pixel Spacing (像素间距)
                    if not hasattr(ds, 'PixelSpacing') or ds.PixelSpacing is None:
                        ds.PixelSpacing = [0.878906, 0.878906]  # 使用模板中的值
                        modified = True

                    # 4. Slice Thickness (切片厚度)
                    if not hasattr(ds, 'SliceThickness') or ds.SliceThickness is None:
                        ds.SliceThickness = 5.0  # 使用模板中的值
                        modified = True

                    # 5. Spacing Between Slices (切片间距)
                    if not hasattr(ds, 'SpacingBetweenSlices') or ds.SpacingBetweenSlices is None:
                        ds.SpacingBetweenSlices = 10.0  # 使用模板中的值
                        modified = True

                    # 6. Slice Location (切片位置)
                    if not hasattr(ds, 'SliceLocation') or ds.SliceLocation is None:
                        ds.SliceLocation = float(i * 10.0)
                        modified = True

                    # 7. Frame of Reference UID (参考坐标系UID)
                    if not hasattr(ds, 'FrameOfReferenceUID') or ds.FrameOfReferenceUID is None:
                        # 使用Study UID作为基础生成Frame of Reference UID
                        ds.FrameOfReferenceUID = f"{self.study_uid}.908.9875.1"
                        modified = True

                    # 如果有修改，保存文件
                    if modified:
                        ds.save_as(file_path, write_like_original=False)

                except Exception as e:
                    print(f"修复几何信息失败 {file_path}: {e}")
                    continue

            print("✓ DICOM几何信息修复完成")

        except Exception as e:
            print(f"修复DICOM几何信息时出错: {e}")

    def apply_coordinate_system_fix(self, dicom_files: List[str]):
        """应用坐标系统修复（解决3D Slicer中的方向问题）"""
        try:
            print("正在应用坐标系统修复...")

            for file_path in dicom_files:
                try:
                    ds = pydicom.dcmread(file_path)

                    # 根据记忆中的坐标系统问题进行修复
                    # 保持轴向视图不变，修复冠状面和矢状面的方向

                    # 确保使用正确的患者位置
                    if hasattr(ds, 'PatientPosition'):
                        if ds.PatientPosition != 'HFS':  # Head First Supine
                            ds.PatientPosition = 'HFS'

                    # 确保图像方向正确
                    # 对于轴向切片，标准方向应该是 [1,0,0,0,1,0]
                    if hasattr(ds, 'ImageOrientationPatient'):
                        ds.ImageOrientationPatient = [1.0, 0.0, 0.0, 0.0, 1.0, 0.0]

                    # 保存修改
                    ds.save_as(file_path, write_like_original=False)

                except Exception as e:
                    print(f"应用坐标系统修复失败 {file_path}: {e}")
                    continue

            print("✓ 坐标系统修复完成")

        except Exception as e:
            print(f"应用坐标系统修复时出错: {e}")


def main():
    """主函数"""
    import argparse

    parser = argparse.ArgumentParser(description='增强版JPG转DICOM工具')
    parser.add_argument('input', help='输入JPG文件或目录')
    parser.add_argument('output', help='输出DICOM文件或目录')
    parser.add_argument('--patient-name', default='Anonymous', help='患者姓名')
    parser.add_argument('--patient-id', help='患者ID')
    parser.add_argument('--study-desc', default='Chest', help='检查描述')
    parser.add_argument('--series-num', type=int, default=1, help='序列号')
    parser.add_argument('--template', help='DICOM模板文件路径')
    parser.add_argument('--create-template', help='从DICOM目录创建模板')

    args = parser.parse_args()

    # 创建转换器
    converter = EnhancedJpgToDicom(args.template)

    # 如果需要创建模板
    if args.create_template:
        if converter.create_template_from_dicom_dir(args.create_template):
            template_path = "custom_dicom_template.json"
            converter.analyzer.save_template(converter.template, template_path)
            print(f"模板已创建并保存到: {template_path}")
        return

    # 检查输入是文件还是目录
    input_path = Path(args.input)

    if input_path.is_file():
        # 单文件转换
        success = converter.convert_jpg_to_dicom(
            args.input, args.output, args.patient_name, args.patient_id,
            args.study_desc, args.series_num, 1, 0.0
        )
        if success:
            print(f"转换成功: {args.input} -> {args.output}")
        else:
            print(f"转换失败: {args.input}")

    elif input_path.is_dir():
        # 目录批量转换
        success_files = converter.convert_directory(
            args.input, args.output, args.patient_name, args.patient_id,
            args.study_desc, args.series_num
        )
        print(f"批量转换完成，成功转换 {len(success_files)} 个文件")

    else:
        print(f"输入路径不存在: {args.input}")


if __name__ == "__main__":
    main()
