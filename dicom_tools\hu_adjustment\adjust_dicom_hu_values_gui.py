#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DICOM文件CT值调整工具 - GUI界面

该工具提供图形界面，用于调整DICOM文件中的CT值
通过修改RescaleIntercept参数使CT值在原有基础上减少约33074

作者: AI Assistant
日期: 2024-06-10
"""

import os
import sys
import threading
import tkinter as tk
from tkinter import filedialog, messagebox, ttk
from pathlib import Path
import queue

# 导入调整器类
from adjust_dicom_hu_values import DicomHUAdjuster


class DicomHUAdjusterGUI:
    """DICOM文件CT值调整器GUI界面"""
    
    def __init__(self, root):
        """初始化GUI界面"""
        self.root = root
        self.root.title("DICOM CT值调整工具")
        self.root.geometry("650x480")
        self.root.resizable(True, True)
        
        # 设置窗口图标（可选）
        # self.root.iconbitmap("icon.ico")
        
        # 创建消息队列用于线程间通信
        self.msg_queue = queue.Queue()
        
        # 创建界面元素
        self.create_widgets()
        
        # 配置风格
        self.style = ttk.Style()
        self.style.configure("TButton", padding=6, relief="flat", background="#ccc")
        
        # 设置默认值
        self.adjustment_value.set(-33074)
        
        # 定期检查消息队列
        self.root.after(100, self.check_msg_queue)
    
    def create_widgets(self):
        """创建GUI界面元素"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 输入框架
        input_frame = ttk.LabelFrame(main_frame, text="输入设置", padding="10")
        input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 输入文件/文件夹选择
        ttk.Label(input_frame, text="输入路径:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.input_path = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.input_path, width=50).grid(row=0, column=1, sticky=tk.EW, padx=5, pady=5)
        ttk.Button(input_frame, text="浏览...", command=self.browse_input).grid(row=0, column=2, sticky=tk.E, padx=5, pady=5)
        
        # 输入类型
        self.input_type = tk.StringVar(value="file")
        ttk.Radiobutton(input_frame, text="单个文件", variable=self.input_type, value="file").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        ttk.Radiobutton(input_frame, text="文件夹", variable=self.input_type, value="dir").grid(row=1, column=1, sticky=tk.W, padx=5, pady=5)
        
        # 文件匹配模式（仅文件夹模式有效）
        ttk.Label(input_frame, text="文件匹配模式:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        self.file_pattern = tk.StringVar(value="*.dcm")
        pattern_entry = ttk.Entry(input_frame, textvariable=self.file_pattern, width=20)
        pattern_entry.grid(row=2, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(input_frame, text="(仅在文件夹模式有效)").grid(row=2, column=2, sticky=tk.W)
        
        # 输出框架
        output_frame = ttk.LabelFrame(main_frame, text="输出设置", padding="10")
        output_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # 输出选项
        self.overwrite_original = tk.BooleanVar(value=False)
        ttk.Checkbutton(output_frame, text="覆盖原始文件", variable=self.overwrite_original, 
                       command=self.toggle_output_path).grid(row=0, column=0, columnspan=2, sticky=tk.W, padx=5, pady=5)
        
        # 输出路径
        ttk.Label(output_frame, text="输出路径:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        self.output_path = tk.StringVar()
        self.output_entry = ttk.Entry(output_frame, textvariable=self.output_path, width=50)
        self.output_entry.grid(row=1, column=1, sticky=tk.EW, padx=5, pady=5)
        self.output_browse_btn = ttk.Button(output_frame, text="浏览...", command=self.browse_output)
        self.output_browse_btn.grid(row=1, column=2, sticky=tk.E, padx=5, pady=5)
        
        # 参数框架
        param_frame = ttk.LabelFrame(main_frame, text="调整参数", padding="10")
        param_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # CT值调整参数
        ttk.Label(param_frame, text="CT值调整量:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        self.adjustment_value = tk.DoubleVar()
        ttk.Entry(param_frame, textvariable=self.adjustment_value, width=15).grid(row=0, column=1, sticky=tk.W, padx=5, pady=5)
        ttk.Label(param_frame, text="(通常为负值，如 -33074)").grid(row=0, column=2, sticky=tk.W)
        
        # 操作框架
        action_frame = ttk.Frame(main_frame)
        action_frame.pack(fill=tk.X, padx=5, pady=15)
        
        # 操作按钮
        ttk.Button(action_frame, text="开始处理", command=self.start_processing).pack(side=tk.LEFT, padx=5)
        ttk.Button(action_frame, text="退出", command=self.root.quit).pack(side=tk.RIGHT, padx=5)
        
        # 状态框架
        status_frame = ttk.LabelFrame(main_frame, text="处理状态", padding="10")
        status_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, orient=tk.HORIZONTAL, length=100, mode='indeterminate')
        self.progress.pack(fill=tk.X, padx=5, pady=5)
        
        # 日志文本框
        self.log_text = tk.Text(status_frame, wrap=tk.WORD, width=70, height=10)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(self.log_text, orient=tk.VERTICAL, command=self.log_text.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        self.log_text.config(yscrollcommand=scrollbar.set)
        
    def toggle_output_path(self):
        """切换输出路径设置的可用状态"""
        if self.overwrite_original.get():
            self.output_entry.config(state=tk.DISABLED)
            self.output_browse_btn.config(state=tk.DISABLED)
        else:
            self.output_entry.config(state=tk.NORMAL)
            self.output_browse_btn.config(state=tk.NORMAL)
    
    def browse_input(self):
        """浏览输入文件/文件夹"""
        if self.input_type.get() == "file":
            file_path = filedialog.askopenfilename(
                title="选择DICOM文件",
                filetypes=[("DICOM文件", "*.dcm"), ("所有文件", "*.*")]
            )
            if file_path:
                self.input_path.set(file_path)
        else:  # dir
            dir_path = filedialog.askdirectory(title="选择包含DICOM文件的文件夹")
            if dir_path:
                self.input_path.set(dir_path)
    
    def browse_output(self):
        """浏览输出文件/文件夹"""
        if self.input_type.get() == "file" and Path(self.input_path.get()).is_file():
            # 如果输入是单个文件，输出也应该是单个文件
            file_path = filedialog.asksaveasfilename(
                title="保存DICOM文件",
                filetypes=[("DICOM文件", "*.dcm"), ("所有文件", "*.*")],
                defaultextension=".dcm"
            )
            if file_path:
                self.output_path.set(file_path)
        else:
            # 如果输入是文件夹，输出也应该是文件夹
            dir_path = filedialog.askdirectory(title="选择输出文件夹")
            if dir_path:
                self.output_path.set(dir_path)
    
    def log_message(self, message):
        """将消息添加到日志中"""
        self.msg_queue.put(message)
    
    def check_msg_queue(self):
        """检查消息队列并更新日志"""
        try:
            while True:
                message = self.msg_queue.get_nowait()
                self.log_text.insert(tk.END, message + "\n")
                self.log_text.see(tk.END)
                self.msg_queue.task_done()
        except queue.Empty:
            pass
        
        self.root.after(100, self.check_msg_queue)
    
    def validate_inputs(self):
        """验证输入参数"""
        # 检查输入路径
        if not self.input_path.get():
            messagebox.showerror("错误", "请选择输入路径")
            return False
        
        input_path = Path(self.input_path.get())
        if not input_path.exists():
            messagebox.showerror("错误", f"输入路径不存在: {input_path}")
            return False
            
        if self.input_type.get() == "file" and not input_path.is_file():
            messagebox.showerror("错误", "选择的输入路径不是文件")
            return False
            
        if self.input_type.get() == "dir" and not input_path.is_dir():
            messagebox.showerror("错误", "选择的输入路径不是文件夹")
            return False
        
        # 检查输出路径（如果不覆盖原文件）
        if not self.overwrite_original.get() and not self.output_path.get():
            messagebox.showerror("错误", "请选择输出路径或选择覆盖原始文件")
            return False
            
        return True
            
    def start_processing(self):
        """开始处理DICOM文件"""
        if not self.validate_inputs():
            return
            
        # 禁用界面元素
        for child in self.root.winfo_children():
            for widget in child.winfo_children():
                if isinstance(widget, (ttk.Button, ttk.Entry)) and widget.winfo_class() != 'TFrame':
                    widget.config(state=tk.DISABLED)
        
        # 启动进度条
        self.progress.start()
        
        # 获取参数
        input_path = self.input_path.get()
        output_path = None if self.overwrite_original.get() else self.output_path.get()
        adjustment_value = self.adjustment_value.get()
        file_pattern = self.file_pattern.get()
        
        # 在新线程中处理文件
        threading.Thread(
            target=self.process_files,
            args=(input_path, output_path, adjustment_value, file_pattern),
            daemon=True
        ).start()
    
    def process_files(self, input_path, output_path, adjustment_value, file_pattern):
        """在后台线程中处理文件"""
        try:
            self.log_message("开始处理DICOM文件...")
            self.log_message(f"调整CT值: {adjustment_value}")
            
            # 创建调整器
            adjuster = DicomHUAdjuster(adjustment_value)
            
            input_path_obj = Path(input_path)
            if input_path_obj.is_file():
                # 处理单个文件
                self.log_message(f"处理文件: {input_path_obj.name}")
                success = adjuster.adjust_single_file(str(input_path_obj), output_path)
                if success:
                    self.log_message("文件处理成功!")
                else:
                    self.log_message("文件处理失败!")
            else:
                # 处理文件夹
                self.log_message(f"处理文件夹: {input_path}")
                self.log_message(f"文件匹配模式: {file_pattern}")
                success, fail = adjuster.batch_process(input_path, output_path, file_pattern)
                self.log_message(f"处理完成: {success}个文件成功, {fail}个文件失败")
            
            self.log_message("所有任务完成!")
            
        except Exception as e:
            self.log_message(f"错误: {str(e)}")
            import traceback
            self.log_message(traceback.format_exc())
            
        finally:
            # 在主线程上执行UI更新
            self.root.after(0, self.processing_complete)
    
    def processing_complete(self):
        """处理完成后恢复界面"""
        # 停止进度条
        self.progress.stop()
        
        # 启用界面元素
        for child in self.root.winfo_children():
            for widget in child.winfo_children():
                if isinstance(widget, (ttk.Button, ttk.Entry)) and widget.winfo_class() != 'TFrame':
                    widget.config(state=tk.NORMAL)
        
        # 如果选择了覆盖原文件，保持输出路径禁用
        self.toggle_output_path()
        
        # 显示完成消息
        messagebox.showinfo("完成", "DICOM文件处理完成!")


def main():
    """主函数"""
    root = tk.Tk()
    app = DicomHUAdjusterGUI(root)
    root.mainloop()


if __name__ == "__main__":
    main() 